import {DEFAULT_FILE_TYPE} from './file'

// qualification: ['workshopLeader', 'experiencedTeacher','studentTutor']
export const QUALIFICATION_TYPE = {
  workshopLeader: 'Workshop leader/Senior lecturer',
  experiencedTeacher: 'Experienced teacher',
  studentTutor: 'Student tutor',
}
export const QUALIFICATION_OPTIONS = Object.entries(QUALIFICATION_TYPE).map((e) => ({label: e[1], value: e[0]}))

// ref: https://dev.classcipe.com/doc/#/fio/service?id=enum
export const SERVICE_ROLE_OPTIONS = [
  {
    value: 'mentoring',
    label: '1v1 Mentor service',
    descTitle: '1v1 mentoring',
    desc: 'As a 1v1 Mentor on Classcipe, you will provide personalized mentoring and teaching in academic subjects or specialized programs. Additionally, this role offers the opportunity to serve as a paid interviewer, evaluating and selecting other service providers.',
  },
  {
    value: 'substitute',
    label: 'Substitute service',
    descTitle: 'Substitute service',
    desc: 'Become a substitute educator and provide online or offline academic support to schools experiencing teacher shortages.',
  },
  {
    value: 'correcting',
    label: 'Correcting service',
    descTitle: 'Correcting service',
    desc: 'As a Corrector on Classcipe, you will review, mark, and provide valuable feedback on academic work. This flexible role allows you to apply your expertise while helping students improve their work.',
  },
  {
    value: 'consultant',
    label: 'Education consultant',
    descTitle: 'Benefit of being an education consultant',
    desc: 'As an education consultant, your chances of being recommended to teachers for relevant mentoring sessions will increase. Additionally, you can recommend suitable sessions to students or parents, and upon a successful conversion, you will receive a corresponding commission.',
    approvalDesc:
      'As a verified consultant, you are now eligible to be recommended for relevant mentoring sessions. Start recommending relevant service packages to students based on their needs and earn a commission for each successful sale.',
  },
  {
    value: 'onCampus',
    label: 'On campus service',
    desc: 'Get verified for on-campus teaching in New Zealand and Australia! This service allows you to qualify as a substitute teacher for flexible teaching opportunities.',
    descTitle: 'On campus',
  },
  {
    value: 'contentContributor',
    label: 'Content contributor',
    descTitle: 'Content contributor ',
    desc: 'This service allows you to maximize your potential by creating high-quality resources for students and teachers. You can sell your content through Classcipe’s global library and verify it as premium course material, earning royalties from each sale.',
  },
]
export const SERVICE_ROLE_TYPE = SERVICE_ROLE_OPTIONS.reduce(mapValueCallback, {})

export function getServiceRoleOptions(key = '') {
  let list = SERVICE_ROLE_OPTIONS
  // on campus
  if (!['mentoring:academic', 'mentoring:steam'].includes(key)) {
    list = list.filter((e) => e.value !== 'onCampus')
  }
  if (
    [
      'mentoring:overseasStudy',
      'mentoring:essay',
      'mentoring:psychology',
      'mentoring:standardizedEnglishPrep',
      'mentoring:teacherTraining',
      'mentoring:teacherTrainingSubject',
      'mentoring:academicPlanning',
      'mentoring:personalStatement',
      'mentoring:interest',
    ].includes(key)
  ) {
    list = list.filter((e) => e.value !== 'substitute' && e.value !== 'correcting')
  }
  return list
}

// // ref: https://docs.google.com/spreadsheets/d/1KxcC-K1JQdh0Qu8DjXF6r19cW1JNjErYBlaqb1UGYAU/edit?gid=**********#gid=**********

// workshop, academic, teacherTranning
export const VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Photo Identification',
    desc: 'Any one of the options',
    child: [
      {label: "Passport/ Driver's license/ National Identity Card", desc: '', type: DEFAULT_FILE_TYPE},
      // {label: 'National Identify Card', desc: '', type: DEFAULT_FILE_TYPE},
    ],
  },
  {
    category: 'Educational Qualification',
    desc: 'Both are mandatory',
    child: [
      {label: 'Bachelors/ Maters/ Doctorate certificate', desc: 'Highest qualification certificate, whichever applicable', type: DEFAULT_FILE_TYPE},
      {label: 'Academic transcript of Bachelor Degree', desc: '', type: DEFAULT_FILE_TYPE},
    ],
  },
  {
    category: 'Professional Qualification',
    desc: '',
    child: [
      {label: 'CV/ Resume', desc: 'Max three pages document', type: DEFAULT_FILE_TYPE},
      {label: 'B.Ed/ M.Ed/ PAGE/ PGDE', desc: 'Any one relevant teaching qualification certificate OR equivalent', type: DEFAULT_FILE_TYPE},
      {
        label: 'Registration/ Professional certificate/ Teaching license',
        desc: 'Any one of the options OR equivalent',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Trainer / Workshop facilitator certificate',
        desc: 'Any Train the trainer course OR Curriculum/ Subject specific workshop facilitator certificate - independently / school enrolled.  *This evidence is mandatory to apply as a trainer',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Safety and Well-being',
    desc: 'Any one of the options OR equivalent',
    child: [{label: 'Police Clearance certificate / Criminal background check', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'Video',
    desc: 'Video clip of recent workshop facilitation OR classroom teaching demo, whichever applicable (MP4 format) *Max file size - 100 MB',
    child: [{label: 'Max 2 minutes video clip', desc: '', type: 'video/*'}],
  },
]
export const TEACHER_TRAINING_SUBJECT_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Personal Information',
    desc: '',
    child: [
      {label: "Passport/ Driver's license/ National Identity Card", desc: 'Any one of the options', type: DEFAULT_FILE_TYPE},
      {label: 'CV/ Resume', desc: 'Max three pages document', type: DEFAULT_FILE_TYPE},
    ],
  },
  {
    category: 'Teaching experience',
    desc: '',
    child: [
      {
        label: 'Teaching portfolio (share drive link to view)',
        desc: "Showcase candidate's teaching philosophy, past lesson plans, samples of student work, subject-specific certifications or evidence of ongoing professional development in that area, any awards or recognitions a teacher has received for their teaching effectiveness and self-reflections on their teaching practice.",
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Motivation and Suitability',
    desc: '',
    child: [
      {
        label: 'Trainer Application Essay',
        desc: 'Briefly explain in 250-300 words your motivation to become a trainer/ mentor and highlight the key skills that make you a strong fit for this role.',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Video',
    desc: 'Video clip of recent workshop facilitation OR teacher training demo, whichever applicable (MP4 format) *Max file size - 100 MB',
    child: [{label: 'Max 2 minutes video clip', desc: '', type: 'video/*'}],
  },
]

export const OVERSEAS_STUDY_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Photo Identification',
    desc: 'Any one of the options',
    child: [{label: 'Passport/ National identity card', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'Residential proof',
    desc: 'Any one document related to chosen country',
    child: [
      {
        label: 'Utility bill (electricity, water or gas bill)',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'English proficiency',
    desc: '',
    child: [
      {
        label: 'IELTS/ TOEFL certificate OR equivalent English proficiency test',
        desc: 'Proof of English ability required, exempted for native English speakers',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Academic transcript (High school and/ or university)',
        desc: 'English marks B+ and above and Medium of Instruction is English',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Audio clip',
    desc: '',
    child: [{label: 'Three minutes conversation in English - example, talking about favourite hobby, book etc.', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'Experience',
    desc: '',
    child: [
      {label: 'CV with work experience', desc: '', type: DEFAULT_FILE_TYPE},
      {label: 'Experience letter from employer', desc: '', type: DEFAULT_FILE_TYPE},
    ],
  },
]

export const ACADEMIC_PLANNING_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Photo Identification',
    desc: 'Please uplaod any one of these',
    child: [
      {label: 'Student identity card/ Student enrollment confirmation / An official document verifying current employment', desc: '', type: DEFAULT_FILE_TYPE},
    ],
  },
  {
    category: 'Qualification Document',
    desc: 'Bachelor’s, Master’s, or PhD / teaching license, lab skills certificates',
    child: [
      {
        label: 'Academic degree certificates / Academic transcripts / Professional certifications',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Evidence of Research Area',
    desc: 'PDF of published papers or conference abstracts. Research summary, proposal, or project report. Recommendation letter from research supervisors or lab affiliations. Links to academic profiles (e.g., Google Scholar, ResearchGate).',
    child: [
      {
        label: 'Published papers / Conference abstracts / Research summary/ Proposal',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'English Proficiency Verification for non-native English speaker',
    desc: 'Please upload any one of these',
    child: [
      {
        label: 'Published papers / Conference abstracts / Research summary/ Proposal',
        desc: 'TOEFL, IELTS, Duolingo, or equivalent / A degree certificate from an English-medium institution',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Video Verification',
    desc: 'Purpose: to evaluate communication skills',
    child: [
      {
        label: 'A video (max 2 minutes)',
        desc: 'A video (max 2 minutes) introducing yourself in English.',
        type: 'video/*',
      },
    ],
  },
]

export const PERSONAL_STATEMENT_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Photo Identification',
    desc: 'Any one of the options',
    child: [{label: 'Passport/ National identity card', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'Work Experience & References',
    desc: 'Should detail years of experience, number of students advised, countries of specialization, etc',
    child: [
      {
        label: 'Resume or Professional Bio ',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Employment Verification or Reference Letters',
        desc: 'Letters or other evidence from previous employers or letters from previous supervisors confirming your role and achievements.',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Qualification Document ',
    desc: '',
    child: [
      {
        label: 'Academic degree certificates / Professional certificate',
        desc: 'Bachelor’s, Master’s, or PhD / ICEF Certified Education Agent, British Council’s Education Agent Training Certificate, QEAC or PIER (for Australia), Canada Course for Education Agents (CCEA), Any country-specific visa advisory training',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Professional Presence & Institutional Partnerships',
    desc: '',
    child: [
      {
        label: 'Affiliations with Universities or Organizations',
        desc: 'If an official partner or authorized agent of any universities, proof of partnerships (e.g., letters, certificates, or websites listing them)',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Website or Social Media Presence',
    desc: '',
    child: [
      {
        label: 'Official website with services, case studies, testimonials',
        desc: 'LinkedIn or any other websites with consistent, professional, and knowledgeable content',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Video Verification',
    desc: 'Purpose: To evaluate communication skills, academic engagement, and teaching/research alignment.',
    child: [
      {
        label: 'A video (max 2 minutes)',
        desc: 'A video (max 2 mins) introducing one case where you helped a client with either his / her application or visa.',
        type: 'video/*',
      },
    ],
  },
]

export const INTEREST_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Photo Identification',
    desc: 'Any one of the options',
    child: [{label: 'Passport/ National identity card', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'English proficiency',
    desc: '',
    child: [
      {
        label: 'Academic transcript (High school and/ or university)',
        desc: 'School and/ or University located in an English speaking country and Medium of Instruction is English)',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Experience',
    desc: '',
    child: [
      {
        label: 'Curriculum Vitae (CV)/  Resume (Max three pages document)',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Experience letter from employer',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
]

export const STEAM_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Photo Identification',
    desc: 'Any one of the options',
    child: [{label: 'Passport/ National identity card, etc.', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'Academic Qualifications',
    desc: '',
    child: [
      {
        label: "Bachelor's degree",
        desc: 'Science, Engineering OR Arts',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: "Master's degree or higher",
        desc: 'Advanced knowledge in STEAM related subjects',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Teaching certification or licence',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Teaching experience',
    desc: '',
    child: [
      {
        label: 'Teaching portfolio (share drive link to view)',
        desc: "Showcase candidate's teaching philosophy, past lesson plans, samples of student work, subject-specific certifications or evidence of ongoing professional development in that area, any awards or recognitions a teacher has received for their teaching effectiveness and self-reflections on their teaching practice.",
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Professional Qualification',
    desc: '',
    child: [
      {
        label: 'Curriculum Vitae (CV)/  Resume',
        desc: 'Max three pages document',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'STEAM-related workshops or training certificates',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Online teaching certifications or training',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Evidence of using technology effectively in the classroom',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Letters of recommendation',
        desc: "From colleagues or administrators who can vouch for the teacher's STEAM expertise and online teaching capability",
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
]

export const PSYCHOLOGY_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Photo Identification',
    desc: 'Any one of the options',
    child: [{label: 'Passport/ National identity card', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'Residential proof',
    desc: 'Any one document related to chosen country',
    child: [
      {
        label: 'Utility bill (electricity, water or gas bill)',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'English proficiency',
    desc: '',
    child: [
      {
        label: 'IELTS/ TOEFL certificate OR equivalent English proficiency test',
        desc: 'Proof of English ability required, exempted for native English speakers',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Academic transcript (High school and/ or university)',
        desc: 'English marks B+ and above and Medium of Instruction is English',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Audio clip',
    desc: '',
    child: [{label: 'Three minutes conversation in English - example, talking about favourite hobby, book etc.', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'Experience',
    desc: '',
    child: [
      {label: 'CV with work experience', desc: '', type: DEFAULT_FILE_TYPE},
      {label: 'Experience letter from employer', desc: '', type: DEFAULT_FILE_TYPE},
    ],
  },
]
export const STANDARDIZED_ENGLISH_PREP_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Photo Identification',
    desc: 'Any one of the options',
    child: [{label: 'Passport/ National identity card', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'Residential proof',
    desc: 'Any one document related to chosen country',
    child: [
      {
        label: 'Utility bill (electricity, water or gas bill)',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'English proficiency',
    desc: '',
    child: [
      {
        label: 'IELTS/ TOEFL certificate OR equivalent English proficiency test',
        desc: 'Proof of English ability required, exempted for native English speakers',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Academic transcript (High school and/ or university)',
        desc: 'English marks B+ and above and Medium of Instruction is English',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Audio clip',
    desc: '',
    child: [{label: 'Three minutes conversation in English - example, talking about favourite hobby, book etc.', desc: '', type: DEFAULT_FILE_TYPE}],
  },
  {
    category: 'Experience',
    desc: '',
    child: [
      {label: 'CV with work experience', desc: '', type: DEFAULT_FILE_TYPE},
      {label: 'Experience letter from employer', desc: '', type: DEFAULT_FILE_TYPE},
    ],
  },
]

export const ESSAY_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: 'Photo Identification',
    desc: '',
    child: [
      {label: 'Passport', desc: '', type: DEFAULT_FILE_TYPE},
      {
        label: "Driver's license",
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {label: 'National Identity Card', desc: '', type: DEFAULT_FILE_TYPE},
    ],
  },
  {
    category: 'Qualification document',
    desc: '',
    child: [
      {
        label: 'Academic degree certificates (Bachelor’s, Master’s, or PhD) ',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Academic transcripts',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Professional certifications (e.g., teaching license, lab skills certificates)',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Evidence of research area',
    desc: '',
    child: [
      {
        label: 'Research paper pdf',
        desc: 'PDF of published papers or conference abstracts',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Research summary, proposal, or project report',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Recommendation letter from research supervisors or lab affiliations',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Links to academic profiles (e.g., Google Scholar, ResearchGate)',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Evidence of Employment/Previous Relevant Experiences',
    desc: '',
    child: [
      {
        label: 'A complete and up-to-date Resume or Curriculum Vitae (CV) in PDF form',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Internship completion certificates or offer letters',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
      {
        label: 'Employment verification letters (must include organization name, position title, and dates)',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'English Proficiency Verification',
    desc: 'English language proficiency test score (TOEFL, IELTS, Duolingo, or equivalent',
    child: [
      {
        label: 'A degree certificate from an English-medium institution',
        desc: '',
        type: DEFAULT_FILE_TYPE,
      },
    ],
  },
  {
    category: 'Video Verification',
    desc: 'Describe one or more academic areas you are passionate about and capable of mentoring or teaching students in. Language- English',
    child: [{label: 'A video (max 2 minutes)', desc: '', type: 'video/*'}],
  },
]

export const AMBASSADOR_VERIFICATION_MATERIAL_OPTIONS = [
  {
    category: '',
    desc: '',
    child: [
      {label: 'School ID or relevant ID', desc: 'To verify your current affiliation with an educational institution.', type: DEFAULT_FILE_TYPE},
      {
        label: 'Upload Letter of Intent or Personal Statement',
        desc: 'To understand your background and motivation for becoming a student/teacher ambassador.',
        type: DEFAULT_FILE_TYPE,
      },
      {label: 'Self Introduction Video', desc: "To introduce yourself and explain why you're suitable for the ambassador role.", type: 'video/*'},
    ],
  },
]

export function getAttachmentType(attachmentType = [], key = '') {
  const result = {}
  attachmentType?.forEach((name) => {
    let options = null
    if (key === 'mentoring:teacherTrainingSubject') {
      options = TEACHER_TRAINING_SUBJECT_VERIFICATION_MATERIAL_OPTIONS
    } else if (key === 'mentoring:essay') {
      options = ESSAY_VERIFICATION_MATERIAL_OPTIONS
    } else if (key === 'mentoring:steam') {
      options = STEAM_VERIFICATION_MATERIAL_OPTIONS
    } else if (key === 'mentoring:psychology') {
      options = PSYCHOLOGY_VERIFICATION_MATERIAL_OPTIONS
    } else if (key === 'mentoring:standardizedEnglishPrep') {
      options = STANDARDIZED_ENGLISH_PREP_VERIFICATION_MATERIAL_OPTIONS
    } else if (key === 'mentoring:overseasStudy') {
      options = OVERSEAS_STUDY_VERIFICATION_MATERIAL_OPTIONS
    } else if (key === 'mentoring:academicPlanning') {
      options = ACADEMIC_PLANNING_VERIFICATION_MATERIAL_OPTIONS
    } else if (key === 'mentoring:personalStatement') {
      options = PERSONAL_STATEMENT_VERIFICATION_MATERIAL_OPTIONS
    } else if (key === 'mentoring:interest') {
      options = INTEREST_VERIFICATION_MATERIAL_OPTIONS
    } else if (key === 'ambassador') {
      options = AMBASSADOR_VERIFICATION_MATERIAL_OPTIONS
    } else {
      options = VERIFICATION_MATERIAL_OPTIONS
    }

    options.forEach((e) => {
      const target = e.child.find((child) => child.label === name)
      if (target) {
        const category = e.category
        if (!result?.[category])
          result[category] = {
            label: category,
            desc: e?.desc ?? '',
            child: [],
          }
        result[category].child.push(target)
      }
    })
  })
  return result
}

function mapValueCallback(acc, cur) {
  acc[cur.value] = cur
  return acc
}

// // ref: https://docs.google.com/spreadsheets/d/1BsKOa4C6ren0zoOAedhoZcBKCnFjmeRsb7DyaFfBieQ/edit?gid=815691089#gid=815691089

// compulsory, single
export const LEARNING_STAGE_OPTION = [
  {
    label: 'Catch up / Emerging',
    value: 'catchUp',
    desc: 'By providing personalized learning support, we empower students to bridge the gap and achieve their full potential.',
  },
  {
    label: 'Foundational / Developing',
    value: 'foundational',
    desc: 'By providing additional support, students can tackle complex challenges, solidifying their grasp of core concepts and skills.',
  },
  {
    label: 'Competent / Secure',
    value: 'competent',
    desc: 'By empowering students to leverage their existing knowledge and skills through targeted guidance, we equip them to tackle complex problems, fostering their ability to solve challenges independently.',
  },
  {
    label: 'Advanced / Mastery',
    value: 'advanced',
    desc: 'By nurturing a deep understanding, advanced students thrive on complex challenges, showcasing not just knowledge mastery but also the ability to analyze, synthesize, and evaluate information in unfamiliar situations.',
  },
]
export const LEARNING_STAGE_MAP = LEARNING_STAGE_OPTION.reduce(mapValueCallback, {})

// compulsory, multiple
export const LEARNING_STYLE_OPTION = [
  {
    label: 'Visual',
    value: 'visual',
    desc: 'Learners who see best learn best! Pictures, drawings, and charts help them remember information. They often like to draw or make pictures themselves to learn new things.',
  },
  {
    label: 'Auditory',
    value: 'auditory',
    desc: 'Learners who listen best learn best by hearing things explained. They often like to talk things through to understand them, like explaining ideas to a friend. This helps them sort through their thoughts and remember information.',
  },
  {
    label: 'Kinesthetic',
    value: 'kinesthetic',
    desc: 'Learners who learn by doing are called kinesthetic learners. They like to move around and get hands-on with things to understand them better. This might involve building something, acting things out, or doing experiments.',
  },
  {
    label: 'Verbal / Linguistic',
    value: 'verbal',
    desc: 'Text-based learners thrive on written information. They learn best by reading and writing, finding it more helpful than seeing or hearing things.',
  },
]
export const LEARNING_STYLE_MAP = LEARNING_STYLE_OPTION.reduce(mapValueCallback, {})

// optional, multiple
export const OTHER_LEARNING_STYLE_OPTION = [
  {
    label: 'Logical / analytical',
    value: 'logical',
    desc: 'Thinkers love figuring things out. They ask "why" and enjoy challenges.',
  },
  {
    label: 'Social / interpersonal',
    value: 'social',
    desc: 'Team players learn best by working with others. They like to talk things out and hear different ideas.',
  },
  {
    label: 'Solitary / intrapersonal',
    value: 'solitary',
    desc: "Lone learners prefer to study by themselves. They're good at motivating themselves and working on their own, but might not enjoy group work as much.",
  },
  {
    label: 'Nature',
    value: 'nature',
    desc: 'Some students learn best in nature. They like quiet, calming places and enjoy being outdoors to learn.',
  },
]
export const OTHER_LEARNING_STYLE_MAP = OTHER_LEARNING_STYLE_OPTION.reduce(mapValueCallback, {})

export function hasVideo(files) {
  const mimes = files.map((e) => e?.mime || '')
  if (mimes.some((e) => e.includes('video'))) {
    return true
  }
  return false
}
