import {boot} from 'quasar/wrappers'
import {format} from 'quasar'
import {pubStore} from 'stores/pub'

import * as ConstList from './const'
import PubAvatar from '../components/pub/PubAvatar.vue'
import PubSelect from 'components/pub/PubSelect.vue'
import PubDialog from 'components/pub/PubDialog.vue'
import PubClickInput from '../components/pub/PubClickInput.vue'
import PubTopBanner from '../components/pub/PubTopBanner.vue'
import PubDrawer from '../components/pub/PubDrawer.vue'
import PubHeader from '../components/pub/PubHeader.vue'
import NavBar from '../components/pub/NavBar.vue'
import NoData from 'components/pub/NoData.vue'
import IntlDate from 'components/pub/IntlDate.vue'

export default boot(async (d) => {
  const {app, urlPath, publicPath, router} = d
  const ubj = new URL(location.href)
  const jwt = ubj.searchParams.get('jwt')
  let token = ''
  const pub = pubStore()
  if (jwt) {
    localStorage.setItem('feathers-jwt', jwt)
    ubj.searchParams.delete('jwt')
    setTimeout(() => {
      router.replace(ubj.pathname.replace('/v2', '') + ubj.search)
    }, 800)
    // Acan.url('jwt', null)
  }
  // auto check logout
  console.warn(pub.user, 'init put')
  if (localStorage.getItem('feathers-jwt')) {
    App.checkLogoutId = setInterval(() => {
      if (localStorage.getItem('feathers-jwt')) return
      clearInterval(App.checkLogoutId)
      $q?.notify({
        message: 'Logout detected, automatically refresh the page after 30 seconds',
        progress: true,
        color: 'primary',
        timeout: 30000,
        actions: [
          {
            label: 'Refresh',
            color: 'white',
            handler: () => {
              location.reload()
            },
          },
        ],
      })
      setTimeout(() => {
        location.reload()
      }, 30000)
    }, 1000)
  }

  app.component('PubAvatar', PubAvatar)
  app.component('PubSelect', PubSelect)
  app.component('PubDialog', PubDialog)
  app.component('PubClickInput', PubClickInput)
  app.component('PubTopBanner', PubTopBanner)
  app.component('PubDrawer', PubDrawer)
  app.component('PubHeader', PubHeader)
  app.component('NavBar', NavBar)
  app.component('NoData', NoData)
  app.component('IntlDate', IntlDate)

  app.config.globalProperties.humanStorageSize = format.humanStorageSize
  app.config.globalProperties.hashToUrl = Fn.hashToUrl
  app.config.globalProperties.isEmpty = Acan.isEmpty
  app.config.globalProperties.Acan = Acan
  for (const key in ConstList) {
    app.config.globalProperties[key] = ConstList[key]
    app.provide(key, ConstList[key])
  }
  const openOldUrl = (app.config.globalProperties.openOldUrl = async (url, role = 'teacher') => {
    if (!pub.user?._id) return toLogin('login', url)
    const {token} = await App.service('users').get('jwtOld', {query: {exp: 0, role}})
    localStorage.setItem('Access-Token', JSON.stringify(token))
    localStorage.setItem('token', token)
    location.href = url
  })

  app.config.globalProperties.regDateFormat = (start, end) => {
    const t1 = new Date(start)
    const t2 = new Date(end)
    const startStr = t1.toLocaleString()
    if (t1.toLocaleDateString() === t2.toLocaleDateString()) {
      return [startStr, t2.toLocaleTimeString()].join(' - ')
    } else {
      return [startStr, t2.toLocaleString()].join(' - ')
    }
  }
  app.provide('openOldUrl', openOldUrl)
  router.beforeEach((to, from, next) => {
    if (!to.meta.role) return next()
    const rs = pub.user.roles.find((v) => v === to.meta.role)
    if (rs) return next()
    console.log('no power')
    router.push(from.path)
  })
  app.directive('roleHas', {
    mounted(el, binding) {
      const roles = pub.user.roles || []
      const rs = binding.value.find((v) => roles.includes(v))
      if (!rs) el.style.display = 'none'
    },
  })
  const metaFn = (data) => {
    console.log('metaFn', data)
    return {
      title: data.title,
      titleTemplate: (title) => `${title} - Classcipe`,
      meta: {
        description: {name: 'description', content: data.desc},
        keywords: {name: 'keywords', content: data.keywords || ''},
        equiv: {'http-equiv': 'Content-Type', content: 'text/html; charset=UTF-8'},
        ogUrl: {name: 'og:url', content: data.url},
        ogImage: {name: 'og:image', content: data.img},
        ogTitle: {name: 'og:title', content: data.title},
        ogDescription: {name: 'og:description', content: data.desc},
      },
    }
  }
  const api = async (uri, method = 'GET', data = null) => {
    let url = `${apiUrl}${uri}`
    const params = {
      method,
      headers: {
        'content-type': 'application/json',
        'x-access-token': token,
      },
    }
    if (data) {
      if (method === 'POST') params.body = JSON.stringify(data)
      else {
        const ubj = new URL(url)
        for (const key in data) {
          ubj.searchParams.set(key, encodeURIComponent(data[key]))
        }
        url = ubj.href
      }
    }
    const rs = await fetch(url, params)
      .then((r) => r.json())
      .catch((e) => {
        console.log(e)
        return {}
      })
    if (rs.code !== 0) return
    return rs.result
  }
  const apiGet = (app.config.globalProperties.apiGet = (uri, data) => {
    return api(uri, 'GET', data)
  })
  const apiPost = (app.config.globalProperties.apiPost = (uri, data) => {
    return api(uri, 'POST', data)
  })
  if (typeof window !== 'undefined') {
    // 客户端渲染用
    token = localStorage.getItem('token')
    window.metaFn = metaFn
    window.apiGet = apiGet
    window.apiPost = apiPost
  }
  const apiUrl = '/classcipe/'
  app.config.globalProperties.isDev = isDev
  app.config.globalProperties.isTest = isTest
  app.config.globalProperties.isCn = isCn
  app.config.globalProperties.hostUrl = hostUrl
  const postData = (data) => {
    return new Promise((res) => {
      data._id = Date.now().toString(36) + '_' + Math.random().toString(36).substring(2)
      // console.warn('postData', data)
      window.parent.postMessage(data, '*')
      postData[data._id] = res
    })
  }
  app.provide('postData', postData)
  app.config.globalProperties.postData = postData
  window.loginGoogleUrl = (type, state, prompt) => {
    const currentUrl = new URL(window.location.href)
    const inviteCode = currentUrl.searchParams.get('inviteCode')
    if (inviteCode) state = {...state, inviteCode}
    return `/fio/google/auth?state=${JSON.stringify(state)}${type ? '&type=' + type : ''}${prompt ? '&prompt=1' : ''}`
  }
  async function toLogin(type, back, prompt) {
    if (location.pathname.includes('/addon/')) {
      console.log('addon login or signup')
      // return postData({act: 'login', args: [loginGoogleUrl('slide', {key: back, close: true, type: 'signup', role: 'teacher'}, 1)]})
      return postData({act: 'login', args: [loginGoogleUrl('slide', {key: back, type: 'signup', role: 'teacher'}, 1)]})
    }
    localStorage.setItem('publicPath', publicPath)
    localStorage.setItem('loginCallUrl', back || urlPath)
    // const url = loginGoogleUrl(type, {back: encodeURIComponent(back || location.pathname), close: true}, prompt)
    const url = loginGoogleUrl(type, {back: encodeURIComponent(back || location.pathname)}, prompt)
    await openLoginWindow(url)
  }
  app.config.globalProperties.toLogin = toLogin
  app.provide('toLogin', toLogin)

  app.provide('countDownFormat', (ex) => {
    const srr = []
    const h = parseInt((ex % 86400) / 3600)
    const m = parseInt((ex % 3600) / 60)
    const s = parseInt(ex % 60)
    srr.push(h < 10 ? '0' + h : h, m < 10 ? '0' + m : m, s < 10 ? '0' + s : s)
    return (ex > 86400 ? parseInt(ex / 86400) + ' Day' + (parseInt(ex / 86400) > 1 ? 's ' : ' ') : '') + srr.join(':')
  })
  app.config.globalProperties.sleep = sleep
  app.config.globalProperties.back = (f) => {
    history.backFn(f)
  }
  async function closeWin(url) {
    window.close()
    await sleep(1000)
    location.href = url ?? '/'
    return
  }
  history.backFn = async (skipCom = null) => {
    const backPos = localStorage.getItem('BackPos')
    console.warn('backFn', backPos, skipCom, history.state.back, history.state.position)
    if (skipCom === 1 && backPos) {
      return history.go(backPos - history.state.position)
    }
    if (history.state.back) return router.back()
    const routePath = location.pathname.replace(publicPath, '/')
    if (['/search'].includes(routePath)) return
    if (routePath.includes('/com/')) return closeWin()
    router.push({path: '/'})
  }

  const oldToken = async () => {
    if (localStorage.getItem('Access-Token')) {
      return JSON.parse(localStorage.getItem('Access-Token'))
    }
    const {token} = await App.service('users').get('jwtOld', {query: {exp: 0, role: 'teacher'}})
    localStorage.setItem('Access-Token', JSON.stringify(token))
    return token
  }

  window.oldProxy = async function (url, data, method = 'POST') {
    const headers = {
      'content-type': 'application/json;charset=UTF-8',
      'x-access-token': await oldToken(),
    }
    return await fetch(url, {method, headers, body: JSON.stringify(data)}).then((r) => r.json())
  }

  window.oldProxyGet = async function (url) {
    const headers = {
      'x-access-token': await oldToken(),
    }
    return await fetch(url, {method: 'GET', headers}).then((r) => r.json())
  }

  await pub.init()
  // await pub.getUnreadCount()
  if (pub.user?._id) return
  // need login
  if (/\/(my|account)\//.test(location.pathname) || location.pathname.includes('/tags')) {
    console.warn('No login', pub.user)
    const currentUrl = new URL(window.location.href)
    // currentUrl.searchParams.set('close', 'true')
    let back = location?.pathname || ''
    if (!back.startsWith('/v2')) back = `/v2${back}`
    currentUrl.searchParams.set('back', back)
    const url = `/v2/login${currentUrl.search}`
    await openLoginWindow(url)
  } else if (/\/(addon)\//.test(location.pathname) && location.pathname !== '/v2/addon/login') {
    const url = '/v2/addon/login'
    location.href = url
  } else if (/\/(sys)\//.test(location.pathname)) {
    console.warn('No login', pub.user)
    await toLogin()
  }

  // same function at `app.js`, `pub.js`
  async function openLoginWindow(url) {
    location.href = url
    // if (window?.authTimer) return
    // if (window?.authDialog) return
    // window.authDialog = 'opening'
    // window.authDialog = await window?.$q
    //   ?.dialog({
    //     title: 'Please login your Classcipe account',
    //     ok: {label: 'I got it', color: 'teal', noCaps: true},
    //     persistent: true,
    //   })
    //   .onOk(() => {
    //     location.href = url
    //     //   const target = 'mozillaWindow'
    //     //   const features = 'popup'
    //     //   let authWindow = window.open(url, target, features)
    //     //   authWindow.closeLogin = true
    //     //   window.authTimer = setInterval(async () => {
    //     //     const res = await window.AppLogin()
    //     //     if (!authWindow.closed && !res) {
    //     //       return
    //     //     }
    //     //     clearInterval(window.authTimer)
    //     //     window.authDialog = null
    //     //     window.authTimer = null
    //     //     if (res) {
    //     //       $q?.notify({type: 'positive', message: 'Login successfully'})
    //     //       setTimeout(() => {
    //     //         location.reload()
    //     //         window?.authDialog?.hide()
    //     //       }, 2000)
    //     //     } else {
    //     //       $q?.notify({type: 'negative', message: 'Login unsuccessfully'})
    //     //       openLoginWindow(url)
    //     //     }
    //     //   }, 1000)
    //   })
  }
})
