<template>
  <div class="row q-mt-md" v-if="!Acan.isEmpty(defaultTpl)">
    <div class="col-12 q-mb-md" :class="{'q-pa-md shadow-3 rounded-borders-md': pub.isStudent}" v-for="(item, lidx) in defaultTpl" :key="lidx">
      <div class="text-h6" :class="{'text-teal-6': !pub.isStudent}">{{ item.name }}</div>
      <div class="inline-block" v-for="(b, bi) in Array.isArray(content[item.code]) ? content[item.code] : [content[item.code]]" :key="bi">
        <template v-if="item.code == 'words'">
          <q-chip class="text-weight-medium" :ripple="false" size="12px" color="teal-1" text-color="primary">{{ b }}</q-chip>
        </template>
        <span v-else>{{ b }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue'

import {pubStore} from 'stores/pub'
const pub = pubStore()

const props = defineProps(['content'])

const defaultTpl = ref(null)

onMounted(() => {
  defaultTpl.value = props.content.template.filter((v) => {
    return ['inquiry', 'overview', 'idea', 'words'].includes(v.code)
  })
})
</script>
