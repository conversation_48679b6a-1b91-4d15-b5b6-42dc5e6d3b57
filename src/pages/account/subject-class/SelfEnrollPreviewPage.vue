<template>
  <div>
    <PubTopBanner title="Self-enroll application preview" :isShowMenu="false" :useBack="true" :back="`/account/classes/subject/${classId}/self-enroll`" />

    <q-page class="column no-wrap pc-max q-px-lg">
      <div v-if="data">
        <div v-if="data?.status === 0" class="bg-blue rounded-md flex justify-between items-center q-my-md q-pa-md">
          <div class="text-h6 text-white">Application: Pending</div>
          <div>
            <q-btn v-if="isUserEditing" flat rounded class="text-white bg-red q-mr-sm" size="md" no-caps label="Reject" @click="onReject" />
            <q-btn v-if="isUserEditing" flat rounded class="text-white bg-teal q-mr-sm" size="md" no-caps label="Approve" @click="bulkApprove" />
          </div>
        </div>
        <div v-if="data?.status === -1" class="bg-red rounded-md flex justify-between items-center q-my-md q-pa-md">
          <div class="text-h6 text-white">Application: Rejected</div>
          <q-btn v-if="isUserEditing" flat rounded class="text-white bg-teal-7 q-mr-sm" size="md" no-caps label="Approve" @click="bulkApprove" />
        </div>
        <div v-if="data?.status === 1" class="bg-green rounded-md flex justify-between items-center q-my-md q-pa-md">
          <div class="text-h6 text-white">Application: Approved</div>
          <q-btn v-if="isUserEditing" flat rounded class="text-white bg-red-7 q-mr-sm" size="md" no-caps label="Reject" @click="onReject" />
        </div>

        <div class="flex justify-between items-center q-mt-lg">
          <div class="row">
            <PubAvatar :src="userData?.avatar" size="5rem" />
            <div class="col">
              <div class="q-px-sm text-h6">{{ nameFormatter({...data?.studentInfo, fullname: true}) }}</div>
              <div v-if="data?.studentInfo?.gendar" class="text-grey-7">{{ data.studentInfo.gendar }}</div>
            </div>
          </div>
          <div class="text-grey-7 text-subtitle">{{ date.formatDate(data?.updatedAt, TIME_FORMAT) }}</div>
        </div>
        <div class="text-grey-7 q-my-md">
          <div>Email/Calsscipe ID</div>
          <div v-if="!canEdit && false">/</div>
          <div v-else-if="data?.studentInfo?.email && !data?.studentInfo?.email?.includes('@classcipe.com')">{{ data?.studentInfo?.email || '-' }}</div>
          <div v-else-if="data?.studentInfo?.id">{{ data?.studentInfo?.id }}</div>
        </div>

        <q-expansion-item v-if="data?.answers?.length" label="Questions" header-class="bg-teal-4 text-white" class="rounded-md overflow-hidden">
          <q-card>
            <q-card-section v-for="item in data.answers" :key="item.question">
              <div class="text-teal-4 q-mb-xs">{{ item.question }}</div>
              <div class="text-grey-7">{{ item.answer }}</div>
            </q-card-section>
          </q-card>
        </q-expansion-item>
      </div>

      <NoData v-else />
    </q-page>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useQuasar, date} from 'quasar'
import {useRoute} from 'vue-router'

import {TIME_FORMAT} from 'src/boot/const'

import nameFormatter from 'src/utils/formatters/nameFormatter.js'
import useSchool from 'src/composables/common/useSchool'
import useUser from 'src/composables/common/useUser'
import useClassApply from 'src/composables/account/school/useClassApply.js'

const $q = useQuasar()
const $route = useRoute()
const {isUserEditing} = useSchool()
const {patchOneById, getOneById} = useClassApply()
const {getOneById: getUserById} = useUser()

const canEdit = ref(true)
const classId = computed(() => $route?.params?.id)
const appliedId = computed(() => $route?.params?.appliedId)

async function onReject() {
  await updateStatus(-1)
}

async function bulkApprove() {
  await updateStatus(1)
}

async function updateStatus(status = 0) {
  try {
    $q.loading.show()
    await patchOneById(appliedId.value, {status})
    await getData()
  } catch (error) {
    console.error(error)
  } finally {
    $q.loading.hide()
  }
}

// function goToApplicationSetting() {
//   const path = `/account/classes/subject/${classId.value}/self-enroll/setting`
//   const query = {back: $route.path}
//   $router.push({path, query})
// }

const userData = ref(null)
const data = ref(null)
async function getData() {
  try {
    $q.loading.show()
    data.value = await getOneById(appliedId.value)
    const uid = data.value?.studentInfo?.uid
    userData.value = await getUserById(uid)
  } catch (error) {
    console.error(error)
  } finally {
    $q.loading.hide()
  }
}

onMounted(async () => {
  await getData()
})
</script>

<style lang="scss"></style>
