<template>
  <div>
    <q-card v-if="isBannerShow" class="q-pa-md bg-amber-2 flex justify-between items-center full-width" flat>
      <div class="flex items-center q-gutter-sm">
        <q-icon name="error" color="amber-7" size="sm" />
        <div class="text-grey-8">The students self-enrollment is disabled for this class</div>
      </div>
      <q-btn dense rounded flat size="sm" icon="close" @click.stop="isBannerShow = false" />
    </q-card>

    <PubTopBanner title="Self-enroll application" :isShowMenu="false" />

    <q-page class="column no-wrap pc-max q-px-lg">
      <div class="flex justify-between items-center q-mt-sm">
        <AppTab :tab="tab" :tabOptions="tabOptions" @change="(str) => (tab = str)" />

        <div>
          <q-btn
            v-if="tab !== 'rejected' && isUserEditing"
            flat
            rounded
            class="text-white bg-red q-mr-sm"
            size="md"
            no-caps
            label="Bulk reject"
            :disable="!selectedStudents?.length"
            @click="bulkReject" />
          <q-btn
            v-if="tab !== 'approved' && isUserEditing"
            flat
            rounded
            class="text-white bg-teal q-mr-sm"
            size="md"
            no-caps
            label="Bulk approve"
            :disable="!selectedStudents?.length"
            @click="bulkApprove" />
          <q-btn flat rounded class="text-white bg-blue" size="md" no-caps label="Application format setting" @click="goToApplicationSetting" />
        </div>
      </div>

      <q-table
        class="q-my-md"
        :columns="currentColumns"
        :rows="filteredList"
        row-key="id"
        :selection="canEdit ? 'multiple' : 'none'"
        v-model:selected="selectedStudents"
        v-model:pagination="pagination"
        @request="updateFilteredList"
        :rows-per-page-options="[5, 10, 20]">
        <template v-slot:body-cell-name="{row}">
          <q-td>
            <div :class="[row?.del ? '' : '']">
              <div>{{ nameFormatter(row?.studentInfo) }}</div>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-email="{row}">
          <q-td>
            <div :class="[row?.del ? '' : '']">
              <div v-if="isSchool">
                <div v-if="!canEdit && false">/</div>
                <div v-else-if="row?.studentInfo?.email && !row?.studentInfo?.email?.includes('@classcipe.com')">{{ row?.studentInfo?.email || '-' }}</div>
                <div v-else-if="row?.studentInfo?.id">{{ row?.studentInfo?.id }}</div>
              </div>
              <div v-else>
                <div v-if="!canEdit && false">/</div>
                <div v-else-if="row?.studentInfo?.id">{{ row.studentInfo?.id }}</div>
                <div v-else-if="row?.studentInfo?.mobile">{{ row.studentInfo?.mobile }}</div>
                <div v-else-if="!value?.includes('@classcipe.com')">{{ row?.studentInfo?.studentInfo?.email || '-' }}</div>
              </div>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-approvedAt="{row}">
          <q-td>
            <div :class="[row?.del ? '' : '']">
              <div>{{ date.formatDate(row?.approvedAt || new Date(), TIME_FORMAT) }}</div>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-actions="{row}">
          <q-td>
            <div :class="[row?.del ? '' : '']">
              <q-btn icon="visibility" flat dense rounded class="text-grey" @click="onPreviewClick(row)" :disable="tab !== 'archive' && row?.del" />
            </div>
          </q-td>
        </template>

        <template v-slot:no-data>
          <div class="flex flex-center full-width">
            <NoData message="No student" messageColor="grey" />
          </div>
        </template>
      </q-table>
    </q-page>
  </div>
</template>

<script setup>
import {ref, watch, computed, onMounted} from 'vue'
import {date} from 'quasar'
import {useRoute, useRouter} from 'vue-router'

import {TIME_FORMAT} from 'src/boot/const'
import AppTab from 'components/pub/AppTab.vue'

import nameFormatter from 'src/utils/formatters/nameFormatter.js'
import useSchool from 'src/composables/common/useSchool'
// import useClasses from 'src/composables/account/school/useClasses'
import useClassApply from 'src/composables/account/school/useClassApply.js'

const $router = useRouter()
const $route = useRoute()
const {isSchool, isUserEditing} = useSchool()
// const {list} = useClasses()
const {getList: getAppliedStudents, patchOneById} = useClassApply()

const isBannerShow = ref(true)
const classId = computed(() => $route?.params?.id)
const tabOptions = [
  {name: 'pending', label: `Pending`, value: 0},
  {name: 'approved', label: `Approved`, value: 1},
  {name: 'rejected', label: `Rejected`, value: -1},
]
const tabMap = computed(() =>
  tabOptions.reduce((acc, cur) => {
    acc[cur.name] = cur
    return acc
  }, {})
)

const tab = ref('')
watch(tab, async () => {
  if (!tab.value) return
  const path = `/account/classes/subject/${classId.value}/self-enroll/${tab.value}`
  $router.replace({path, query: $route?.query})
  await updateFilteredList()
})

const canEdit = ref(true)

const columns = [
  {name: 'name', label: 'Name', required: true, align: 'left'},
  {name: 'email', label: 'Email/Classcipe ID', required: true, align: 'left', field: (row) => row?.email || row?.mobile},
  {name: 'approvedAt', label: 'Approved at', required: true, align: 'left', field: (row) => row},
  {name: 'actions', label: 'Actions', required: true, align: 'left'},
]
const currentColumns = computed(() => {
  let list = columns
  if (canEdit.value) {
    return list
  } else {
    return list.slice(0, -1)
  }
})

const selectedStudents = ref([])
// const selectedStudentsMap = computed(
//   () =>
//     selectedStudents.value?.reduce((acc, cur) => {
//       acc[cur._id] = cur
//       return acc
//     }, {}) ?? {}
// )

const searchText = ref('')
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
})

const studentList = ref([])
async function updateFilteredList(arg) {
  if (arg?.pagination) pagination.value = arg?.pagination
  const {page, rowsPerPage} = pagination.value
  const ext = {$skip: (page - 1) * rowsPerPage, $limit: rowsPerPage}
  const status = tabMap.value?.[tab.value]?.value || 0
  const res = await getAppliedStudents({class: classId.value, status, ...ext})
  studentList.value = res?.data
  pagination.value.rowsNumber = res.total
}

const filteredList = computed(() => {
  let list = Acan.clone(studentList.value)
  if (searchText.value) {
    list = list.filter((e) => {
      const {
        name: [firstName, lastName],
      } = e
      return [firstName, lastName].some((_) => _.toLowerCase().includes(searchText.value.toLowerCase()))
    })
  }
  return list
  // Ref:https://dev.classcipe.com/doc/#/fio/students
  // list = list
  //   .filter((e) => {
  //     // if (tab.value === 'all') return true
  //     // if (tab.value === 'archive') return e?.del
  //     if (tab.value === 'pending') return e.status === 2 && !e?.del
  //     if (tab.value === 'approved') return e.status === 0 && !e?.del
  //     if (tab.value === 'rejected') return e.status === -1 && !e?.del
  //   })
  //   .filter((e) => {
  //     if ($route.query?.classId) {
  //       return e.class.includes($route.query.classId)
  //     } else {
  //       return true
  //     }
  //   })
  // return list
})

function onPreviewClick(item) {
  const id = item?._id
  const path = `/account/classes/subject/${classId.value}/self-enroll/preview/${id}`
  $router.push(path)
}

// async function turnOnSelfEnroll() {
//   const title = ''
//   const message = 'Are you sure to turn on student self-enroll'
//   const okButtonLabel = 'Yes'
//   const cancelButtonLabel = 'Not now'
//   $q.dialog({
//     component: ConfirmDialog,
//     componentProps: {title, message, okButtonLabel, cancelButtonLabel},
//   })
//     .onOk(async () => {
//       $q.loading.show()
//       // do something
//       $q.loading.hide()
//     })
//     .onCancel(() => {})
//     .onDismiss(() => {})
// }

// async function turnOffSelfEnroll() {
//   const title = ''
//   const message = 'Are you sure to turn off student self-enroll'
//   const okButtonLabel = 'Yes'
//   const cancelButtonLabel = 'Not now'
//   $q.dialog({
//     component: ConfirmDialog,
//     componentProps: {title, message, okButtonLabel, cancelButtonLabel},
//   })
//     .onOk(async () => {
//       $q.loading.show()
//       // do something
//       $q.loading.hide()
//     })
//     .onCancel(() => {})
//     .onDismiss(() => {})
// }
//
// async function showUnfinishedDialog() {
//   const title = ''
//   const message = 'The required fiels need to be completed before enabling the student self-enroll.'
//   $q.dialog({
//     component: OkDialog,
//     componentProps: {title, message},
//   })
//     .onOk(async () => {
//       $q.loading.show()
//       // do something
//       $q.loading.hide()
//     })
//     .onCancel(() => {})
//     .onDismiss(() => {})
// }
//
// async function showEnrollmentDisabledDialog() {
//   const title = ''
//   const message = 'The self-enrollment for this class is disabled.'
//   $q.dialog({
//     component: OkDialog,
//     componentProps: {title, message},
//   }).onOk(async () => {
//     $q.loading.show()
//     // do something
//     $q.loading.hide()
//   })
// }

async function bulkReject() {
  await updateStatus(-1)
}

async function bulkApprove() {
  await updateStatus(1)
  // Test
  // await updateStatus(0)
}

async function updateStatus(status = 0) {
  try {
    $q.loading.show()
    const ids = selectedStudents.value.map((e) => e._id)
    await Promise.all(ids.map((id) => patchOneById(id, {status})))
    await updateFilteredList()
  } catch (error) {
    console.error(error)
  } finally {
    $q.loading.hide()
  }
}

function goToApplicationSetting() {
  const path = `/account/classes/subject/${classId.value}/self-enroll/setting`
  const query = {back: $route.path}
  $router.push({path, query})
}

onMounted(async () => {
  if (!tab.value) {
    tab.value = 'pending'
    setTimeout(() => {
      const path = `/account/classes/subject/${classId.value}/self-enroll/${tab.value}`
      $router.replace({path, query: $route?.query, key: tab.value})
    }, 100)
    return
  }
})
</script>
