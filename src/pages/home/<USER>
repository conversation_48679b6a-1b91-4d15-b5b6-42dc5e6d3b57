<template>
  <HeaderDrawer @change="onDrawerLoad" @update="onTabUpdate"></HeaderDrawer>

  <q-page-container>
    <q-page class="q-pa-md pc-body" v-if="noclass">
      <div class="rounded-borders-md shadow-3 bg-white overflow-hidden">
        <NoData></NoData>
      </div>
    </q-page>
    <q-page v-else-if="tab === 'substitute'" class="q-pa-md pc-body">
      <SubstituteList />
    </q-page>

    <q-page v-else class="q-pa-md pc-body">
      <template v-if="isMyClass">
        <div class="q-py-md text-h5 text-weight-medium">{{ title }}</div>
        <div class="row q-mb-md q-gutter-md hidden">
          <q-item clickable class="col-4 shadow-3 rounded-borders-md bg-white">
            <q-item-section>
              <q-item-label class="text-h6 text-weight-medium">Report</q-item-label>
              <q-item-label class="row q-py-md justify-between">
                <div>Info:11</div>
                <div>Info:3</div>
              </q-item-label>
            </q-item-section>
            <q-item-section side top>
              <q-icon name="chevron_right"></q-icon>
            </q-item-section>
          </q-item>
          <q-item clickable class="col-4 shadow-3 rounded-borders-md bg-white">
            <q-item-section>
              <q-item-label class="text-h6 text-weight-medium">Journal</q-item-label>
              <q-item-label class="row q-py-md justify-between">
                <div>Published:11</div>
                <div>In approval:3</div>
              </q-item-label>
            </q-item-section>
            <q-item-section side top>
              <q-icon name="chevron_right"></q-icon>
            </q-item-section>
          </q-item>
        </div>
      </template>

      <div v-else class="rounded-borders-md shadow-3 bg-white overflow-hidden">
        <div v-if="isMyClass" class="q-pa-md text-h6 text-weight-medium">Sessions</div>
        <div v-else class="q-pa-md text-h5 text-weight-medium">{{ title }}</div>
        <template v-if="isMyPurchased && tab == 'workshop' && indexPromotions?.length">
          <CardList title="" recommend category="promotion" :list="indexPromotions" />
          <div class="text-h6 q-px-md">Sessions</div>
        </template>
        <div class="row items-center q-px-md justify-between">
          <div class="col-xs-12 col-sm-7 col-md-7 q-pb-md">
            {{ console.log('value of tab', tab) }}
            {{ console.log('value of subtabs,', subtabs) }}
            <q-tabs
              dense
              align="left"
              :breakpoint="0"
              indicator-color="primary"
              active-color="primary"
              v-model="subtab"
              inline-label
              mobile-arrows
              shrink
              @update:model-value="find(0)">
              <q-tab v-for="(item, index) in subtabs" :name="item.name" :label="item.label" :key="index" no-caps></q-tab>
            </q-tabs>
          </div>
          <div class="row col-xs-12 col-sm-4 q-px-md q-py-sm flex items-center justify-start">
            <q-space></q-space>
            <q-btn
              v-if="(isMyWorkshop || isMyClass) && tab !== 'mentoring' && tab !== 'taskManagement' && tab !== 'myAssociatedTask'"
              @click="showImportDialog = true"
              rounded
              color="red-4"
              label="Import"
              no-caps />
            <q-btn v-if="isMyClass" class="q-ml-sm" flat round size="0.7rem" @click.prevent icon="more_vert">
              <q-menu>
                <q-list>
                  <q-item v-if="schoolId" clickable v-close-popup @click="onJournalClick">
                    <q-item-section>Journal</q-item-section>
                  </q-item>
                  <q-item clickable v-close-popup @click="onAnnouncementClick">
                    <q-item-section>Announcement</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <q-btn v-if="isMyWorkshop" class="q-ml-sm" flat round size="0.7rem" @click.prevent icon="more_vert">
              <q-menu>
                <q-list>
                  <q-item v-if="schoolId" clickable v-close-popup @click="journalClassPopup = true">
                    <q-item-section>Journal</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </div>
        </div>
        <div class="q-px-md" v-if="mounted">
          <GeneralFilters
            v-model="filters"
            :appendage="appendage"
            :pagination="pagination"
            :options="filterOptions"
            @update:modelValue="onFilterUpdate"></GeneralFilters>
        </div>
        <div class="column" style="height: 100vh">
          <div class="col q-pa-md" v-if="isCourseOnly && filters.courseView == 'gantt'">
            <div class="full-width flex justify-center" v-if="isEmpty(listData)">
              <q-spinner-ball color="primary" size="2em" class="full-width" v-if="loading" />
              <NoData size="9rem" v-else message="no live session" />
            </div>
            <div class="q-mb-md border-top-05 fit" v-if="!isEmpty(listData)">
              <q-scroll-area visible class="fit">
                <div class="row">
                  <div class="col-3 border-left-05 border-bottom-05">
                    <div class="text-center border-05 q-pa-sm ellipsis" style="height: 40px">Course Name</div>

                    <div
                      v-for="(course, index) in listData"
                      :key="index"
                      style="height: 55px"
                      class="ellipsis col-3 text-left border-05 q-pa-md cursor-pointer"
                      @click="onCourseClick(course)">
                      <q-img spinner-color="white" fit="cover" style="max-width: 30px" :ratio="3 / 2" :src="course.image"> </q-img>
                      <span class="q-pl-xs text-weight-medium gt-xs">
                        {{ course.name }}
                      </span>
                    </div>
                  </div>
                  <div class="col border-right-05 border-bottom-05" :style="`max-width: ${150 * months.length}px`">
                    <q-scroll-area ref="scrollAreaRef" visible class="fit">
                      <div class="row full-width no-wrap">
                        <div v-for="(month, index) in months" :key="index" style="width: 150px; height: 40px" class="text-center q-py-sm border-05">
                          {{ month }}
                        </div>
                      </div>
                      <div class="row full-width" v-for="(course, index) in listData" :key="index">
                        <div v-for="(month, index) in months" :key="index" style="width: 150px; height: 55px" class="text-center q-py-sm border-05"></div>
                        <div class="absolute bg-primary q-pa-sm rounded-borders q-my-md" :style="barStyle(course)"></div>
                      </div>
                    </q-scroll-area>
                  </div>
                </div>
              </q-scroll-area>
            </div>
          </div>
          <template v-else>
            <template v-if="isEmpty(listData)">
              <NoData v-if="!loading" />
              <div v-else class="text-center q-pa-xl text-grey">
                <q-spinner-ball color="primary" size="2em" class="full-width" />
              </div>
            </template>
            <q-virtual-scroll v-else separator class="col q-px-sm full-width overflow-auto" :items="listData" @virtual-scroll="scrollFn">
              <template v-slot:after>
                <div v-if="list.data.length === list.limit" class="row justify-center q-my-md">
                  <q-spinner-ball color="primary" size="2em" />
                </div>
                <div v-else class="q-pa-md text-grey text-center hidden">It's over</div>
              </template>
              <template v-slot="{item: o, index: i}">
                <div :key="i" class="q-ma-sm q-mb-md">
                  <!--
                <BookingBoard stamp v-if="tab == 'mentoring' && subtab == 'booked'" :booking="{...o, ...{_id:null}}"> </BookingBoard>
                -->
                  <TaskCard
                    v-if="tab == 'myAssociatedTask'"
                    isView
                    isMyAssociatedTask
                    :creditDeficit="o.sectionCreditDeficit?.points"
                    :task="o.snapshot"
                    :clickAllowed="true"
                    :associatePackUserId="o._id"
                    :orderPrice="o.price" />
                  <div v-else-if="tab == 'taskManagement'">
                    <SectionTrackBoard v-if="subtab == 'section-tracking'" :sectionTracking="o" />
                    <TaskCard
                      v-else
                      isView
                      isFacilitate
                      :task="o.servicePackUser?.snapshot"
                      :clickAllowed="true"
                      :associatePackUserId="o.servicePackUser?._id" />
                  </div>
                  <BookingBoard v-else-if="tab == 'mentoring' && subtab == 'booked'" :booker="isMyServices" :booking="o"> </BookingBoard>
                  <SessionBoard
                    v-else-if="!o.hidden"
                    :isMyPurchased="isMyPurchased"
                    :isMyWorkshop="isMyWorkshop"
                    :isMyClass="isMyClass"
                    :subtab="subtab"
                    :tab="tab"
                    :categories="categoryOptions"
                    :session="o"
                    :isPointMode="isPointMode"
                    isListing
                    @enrolled="onEnrolled"
                    @change="resetFn()"></SessionBoard>
                </div>
              </template>
            </q-virtual-scroll>
          </template>
        </div>
      </div>
    </q-page>
    <ImportDialog
      :show="showImportDialog"
      :premium="importPremium"
      :service="isMyServices"
      :class-name="isMyClass ? title : ''"
      type="list"
      @hide="onImportDialogHide"></ImportDialog>
    <WorkshopJournalClassrooms v-model="journalClassPopup" />
  </q-page-container>
</template>
<script setup>
/*
imports
*/
import {ref, onMounted, onBeforeUnmount, watch, computed, inject} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import {LocalStorage, date} from 'quasar'
import {subjectsStore} from 'stores/subjects'

import AccountMenu from 'components/AccountMenu.vue'
import NoticePage from 'components/NoticePage.vue'
import SessionBoard from 'components/SessionBoard.vue'
import BookingBoard from 'components/BookingBoard.vue'
import GeneralFilters from 'components/GeneralFilters.vue'
import HeaderDrawer from 'components/HeaderDrawer.vue'
import ImportDialog from 'components/ImportDialog.vue'
import CourseCard from 'components/CourseCard.vue'
import SessionItem from 'components/SessionItem.vue'
import CardList from 'components/CardList.vue'
import PersonAvatar from 'src/pages/account/assessment-tool/components/PersonAvatar.vue'
import useSchool from 'src/composables/common/useSchool'
import {PlatformGrades, EducatorGrades, TaskSessionTypes} from 'src/boot/const'
import {pointStore} from 'stores/point'
import SubstituteList from 'src/pages/home/<USER>'
import WorkshopJournalClassrooms from '../account/journal/WorkshopJournalClassrooms.vue'
import TaskList from 'src/components/ServiceTask/TaskList.vue'
import SectionTrackBoard from 'components/ServiceTask/SectionTrackBoard.vue'
import NoData from '../../components/pub/NoData.vue'
import TaskCard from '../../components/ServiceTask/TaskCard.vue'

/*
consts
*/

const pStore = pointStore()
const indexPromotions = ref([])
const title = ref(null)
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const subjects = subjectsStore()
const filters = ref({})
const tab = ref(null)
const subtab = ref(null)
const loading = ref(false)
const mounted = ref(false)
const listType = ref('table')
const {schoolId} = useSchool()
const coursesList = ref({})

const list = ref({})
const listData = ref([])
const sectionData = ref([])

const isPointMode = ref(false)
const importPremium = ref(false)
const isMyWorkshop = ref(false)
const isMyServices = ref(false)
const isMyPurchased = ref(false)
const isMyClass = ref(false)
const isWorkshop = ref(false)
const enrolling = ref(false)
const months = ref([])
const scrollAreaRef = ref(null)
const roomStatistics = ref({})
const launchBy = ref('me')

const categoryOptions = ref([])
const subjectOptions = ref([])

const showImportDialog = ref(false)
const contentsType = inject('ContentsType')
const virtualListIndex = ref(0)
const noclass = ref(false)
const dateFormat = inject('DATE_FORMAT')

const journalClassPopup = ref(false)
const isStudent = computed(() => {
  return route.query.tab === 'myAssociatedTask'
})
const isServiceProvider = computed(() => {
  return route.query.tab === 'taskManagement'
})

/*
computed
*/
const pagination = computed(() => {
  if (subtab.value === 'section-tracking') {
    console.log('gbtgbugfgufuf')
    return {start: virtualListIndex.value + 1, end: sectionData.value.length, total: sectionData.value.length}
  }
  return {start: virtualListIndex.value + 1, end: listData.value.length, total: list.value.total}
})

const appendage = computed(() => {
  return ((isMyServices.value || isMyPurchased.value) && tab.value == 'workshop') || isMyClass.value
})

const filterOptions = computed(() => {
  const _participant = {
    key: 'participant',
    label: 'Participants',
    type: 'radio',
    default: 'students',
    options: [
      {label: 'Educators', value: 'educators'},
      {label: 'Students', value: 'students'},
    ],
  }
  const _sessionType = {
    key: 'sessionType',
    label: 'Session type',
    type: 'radio',
    default: 'live',
    options: TaskSessionTypes.filter((e) => (!isMyClass.value ? e.label == 'live' : e.label)).map(({label, title}) => ({label: title, value: label})),
  }
  if (isMyClass.value) {
    _sessionType.type = 'checkbox'
    _sessionType.default = []
  }
  const _subjects = {
    key: 'subjects',
    label: 'Subject(s)',
    type: 'checkbox',
    default: [],
    hidden: filters.value.participant == 'seeker',
    options: categoryOptions.value.filter((item) => item.participants == filters.value.participant),
  }
  if (filters.value.participant == 'students') {
    _subjects.options.unshift({label: 'Academic', value: 'academic'})
  }
  const _range = {
    key: 'range',
    label: 'Period',
    type: 'calendar',
    default: [],
  }
  const _grades = {
    key: 'grades',
    label: 'Grade',
    type: 'checkbox',
    default: [],
    options: EducatorGrades.map((item) => ({label: item, value: item})),
  }
  const _taughtBy = {
    key: 'taughtBy',
    label: 'Taught by',
    type: 'checkbox',
    default: [],
    options: [
      {label: 'Me', value: 'me'},
      {label: 'Others', value: 'others'},
    ],
  }
  const _workshopType = {
    key: 'workshopType',
    label: 'Workshop type',
    type: 'checkbox',
    default: [],
    options: [
      {label: 'Premium', value: 'premium'},
      {label: 'Standard', value: 'standard'},
    ],
  }
  const options = []

  if (isMyServices.value) {
    if (tab.value == 'workshop') {
      options.push(_participant, _subjects, _sessionType)
    } else if (tab.value == 'mentoring') {
      if (subtab.value !== 'booked') {
        _participant.options.push({label: 'Job seeker', value: 'seeker'})
      }
      options.push(_participant)
      options.push(_subjects)
    }
  } else if (isMyPurchased.value) {
    if (tab.value == 'workshop') {
      if (subtab.value == 'featured') {
        options.push(_workshopType)
        options.push(_grades)
      }
      options.push(_sessionType)
    } else if (tab.value == 'mentoring') {
    }
  } else if (isMyClass.value) {
    if (!isPersonal.value) {
      options.push(_taughtBy)
    }
    options.push(_sessionType)
  }
  if (subtab.value == 'ended') {
    options.push(_range)
  }
  return options
})

const isAdmin = computed(() => {
  return pub.user?.schoolUser?.role?.includes('admin')
})

const isEducator = computed(() => {
  return !pub.user?.roles?.includes('student')
})

const isPersonal = computed(() => {
  return !pub.user?.schoolInfo?._id
})

const isCourseOnly = computed(() => {
  return filters.value.type?.length == 1 && filters.value.type[0] == 'courses'
})

const subtabs = computed(() => {
  let _tabs
  if (tab.value === 'myAssociatedTask') {
    _tabs = [
      {name: 'ongoing', _label: 'Ongoing', label: 'Ongoing'},
      {name: 'completed', _label: 'Completed', label: 'Completed'},
      {name: 'unassigned', _label: 'Unassigned', label: 'Unassigned'},
    ]
  } else if (tab.value === 'taskManagement') {
    _tabs = [
      {name: 'ongoing', _label: 'Ongoing', label: 'Ongoing'},
      {name: 'section-tracking', _label: 'Section Tracking', label: 'Section Tracking'},
    ]
  } else {
    _tabs = [
      {name: 'scheduled', _label: 'Scheduled', label: 'Scheduled'},
      {name: 'ongoing', _label: 'Ongoing', label: 'Ongoing'},
      {name: 'ended', _label: 'Ended', label: 'Ended'},
    ]
    if (['mentoring', 'substitute'].includes(tab.value)) {
      _tabs.unshift({name: 'booked', _label: 'Booked', label: 'Booked'})
    } else if (isMyPurchased.value) {
      _tabs.unshift({name: 'featured', _label: 'Featured', label: 'Featured'})
    }
    //https://github.com/zran-nz/bug/issues/5617#issue-2746298478
    if (isMyPurchased.value && tab.value == 'mentoring' && !isPersonal.value) {
      _tabs.unshift({name: 'featured', _label: 'Featured', label: 'Featured'})
    }
  }
  const total = subtab.value === 'section-tracking' ? sectionData.value.length : (list.value?.total ?? 0)

  return _tabs.map((item) => {
    if (item.name === subtab.value) {
      return {...item, label: item.label + `(${total})`}
    }
    return item
  })
})

/*
methods
*/

const getIndexPromotion = async () => {
  await App.service('session')
    .get('indexPromotion')
    .then((res) => {
      if (res?.total) {
        indexPromotions.value = res.data
      }
    })
}

const localstorageForFilter = (setting) => {
  let key = 'FILTER_OF_MY_SERVICES_WORKSHOP'
  if (isMyPurchased.value) {
    key = 'FILTER_OF_MY_PURCHASED'
  } else if (isMyClass.value) {
    key = 'FILTER_OF_MY_CLASSES'
  }

  if (setting) {
    LocalStorage.set(key, {...filters.value, ...{subtab: subtab.value}})
  } else {
    const localFilters = LocalStorage.getItem(key)
    if (!Acan.isEmpty(localFilters)) {
      delete localFilters.search
      delete localFilters.subtab
      filters.value = localFilters
    }
    if (localFilters?.subtab) {
      subtab.value = localFilters.subtab
    } else if (isMyPurchased.value) {
      subtab.value = 'featured'
    } else {
      subtab.value = 'ongoing'
    }
  }
}

const onImportDialogHide = (obj) => {
  showImportDialog.value = false
  importPremium.value = false
  if (obj) {
    router.push(obj)
  }
}

const onCourseClick = (course) => {
  //if (isLiveWorkshop.value && liveTab.value == 'featured') return
  //router.push(`/com/course/${course._id}?back=${route.path}`)
}

const viewIsActive = (view) => {
  return filters.value.courseView == view
}

const scrollFn = async ({index, to}) => {
  virtualListIndex.value = index
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data)) return // has last page
  console.warn('need load more')
  find(skip + limit)
}

/*courses*/
const onEnrolled = (enrolled) => {
  if (enrolled) {
    subtab.value = 'scheduled'
  }
  find(0)
}

const find = async ($skip) => {
  let start = null
  let end = null
  let isAllType = true
  let isCourses = false

  if (!Acan.isEmpty(filters.value?.type)) {
    if (filters.value.type.length == 1) {
      if (filters.value.type[0] == 'courses') {
        isCourses = true
      }
      isAllType = false
    }
  }

  if ($skip === 0) {
    resetData()
  }

  const query = {
    pid: {$exists: false},
    del: false,
    $sort: {createdAt: -1},
    status: {$ne: 'close'},
    classId: null,
    $limit: 10,
    $skip,
  }

  const now = new Date()
  const zone = now.getTimezoneOffset()

  if (filters.value.range) {
    const selectedDateArr = filters.value.range.split('~')
    start = new Date(date.startOfDate(selectedDateArr[0], 'day'))
    end = new Date(date.endOfDate(selectedDateArr[1], 'day'))
    //query.start = {$gte: start, $lte: end}
  }

  if (isWorkshop.value) {
    if (pub.user.schoolInfo?._id) {
      query.school = pub.user.schoolInfo._id
    }

    if (isMyPurchased.value) {
      //I participate
      if (subtab.value == 'featured') {
        query.regDate = {$gte: start && start > now ? start : now}
        query.isLib = true
        query.$sort = {_id: -1}
      } else {
        if (isPersonal.value) {
          if (tab.value == 'mentoring') {
            query['students'] = pub.user._id
          } else {
            query['reg._id'] = pub.user._id
          }
        } else {
          query.$or = [{'reg._id': pub.user._id}, {students: pub.user._id}]
        }
      }
      const types = Object.values(contentsType).filter(
        (item) =>
          !item.content &&
          item.educator &&
          ((tab.value == 'workshop' && item.type == 'workshop') || (tab.value == 'mentoring' && (item.type == 'booking' || item.type == 'seeker'))) &&
          (subtab.value == 'featured' ? item.public : true) &&
          (isPersonal.value ? !item.school : item.school) &&
          (isAllType ? true : isCourses ? item.course : item.session || item.tool)
      )
      query.type = {$in: types.map((item) => item.value)}
    } else {
      //I facilitate
      query.uid = pub.user._id

      const types = Object.values(contentsType).filter(
        (item) =>
          !item.content &&
          (isPersonal.value ? !item.school : item.school) &&
          (filters.value.participant == 'educators' ? item.educator : !item.educator) &&
          ((tab.value == 'mentoring' && item.booking) ||
            (tab.value !== 'mentoring' && !item.booking && (isAllType ? true : isCourses ? item.course : item.session || item.tool)))
      )
      query.type = {$in: types.map((item) => item.value)}
      if (filters.value.participant == 'seeker') {
        query.type = {
          $in: Object.values(contentsType)
            .filter((e) => e.seeker)
            .map((e) => e.value),
        }
      }
    }
  } else {
    //isMyClass

    const types = Object.values(contentsType).filter(
      (item) => !item.content && item.class && (isAllType ? true : isCourses ? item.course : item.session || item.tool)
    )

    query.type = {$in: types.map((item) => item.value)}

    if (Array.isArray(filters.value.taughtBy) && filters.value.taughtBy.length == 1 && !isPersonal.value) {
      const by = filters.value.taughtBy.join()
      query.uid = by === 'me' ? pub.user._id : {$ne: pub.user._id}
    }
    //query.uid = pub.user._id
    query.classId = tab.value
  }

  if (subtab.value === 'scheduled') {
    query.$sort = {start: 1}
  } else if (subtab.value === 'ongoing') {
    //#3984
    query.$sort = {start: -1}
  } else if (subtab.value === 'ended') {
    query.$sort = {ended: -1}
  }
  query.status = subtab.value
  if (subtab.value == 'featured') {
    query.status = 'scheduled'
  }
  if (subtab.value == 'ended') {
    query.dateRange = [start, end, zone]
  }

  //if (filters.value.subjects?.length) {
  if (isMyWorkshop.value) {
    const _subjectsOptions = filterOptions.value.find((e) => e.key == 'subjects')?.options || []
    const _subjects = filters.value.subjects?.filter((e) => _subjectsOptions.some((f) => f.value == e))
    if (filters.value.participant == 'students') {
      if (_subjects?.length == 1 && _subjects[0] == 'academic') {
        query.type = {
          $in: query.type.$in.filter((item) => {
            return !contentsType[item]['service']
          }),
        }
      } else if ((_subjects?.length > 1 && _subjects.includes('academic')) || _subjects?.length == 0) {
        query.$or = [
          {
            'task.service.type': {
              $in:
                _subjects.length == 0
                  ? _subjectsOptions.map((e) => e.value)
                  : _subjects.filter((item) => {
                      return item !== 'academic'
                    }),
            },
            type: {
              $in: query.type.$in.filter((item) => {
                return contentsType[item]['service']
              }),
            },
          },
          {
            type: {
              $in: query.type.$in.filter((item) => {
                return !contentsType[item]['service']
              }),
            },
          },
        ]
        delete query.type
      } else if (_subjects?.length) {
        query['task.service.type'] = {$in: _subjects}
        query.type = {
          $in: query.type.$in.filter((item) => {
            return contentsType[item]['service']
          }),
        }
      }
    } else if (filters.value.participant == 'educators') {
      query['task.service.type'] = {$in: _subjects?.length == 0 ? _subjectsOptions.map((e) => e.value) : _subjects}
    }
  }

  if (filters.value.search) {
    query.$or = [{name: {$search: filters.value.search}}]
  }

  if (isMyClass.value && filters.value.sessionType.length == 1) {
    query.sessionType = filters.value.sessionType[0]
  } else if (['live', 'student'].includes(filters.value.sessionType) && tab.value !== 'mentoring') {
    query.sessionType = filters.value.sessionType
  }

  if (filters.value.grades?.length) {
    if (isMyPurchased.value) {
      query['task.grades.value'] = {$in: filters.value.grades}
    } else if (isMyWorkshop.value) {
      query.grades = {$in: filters.value.grades}
    }
  }

  if (isCourseOnly.value && filters.value.courseView == 'gantt') {
    query.$limit = 1000
  }

  loading.value = true
  if (isWorkshop.value && launchBy.value == 'others') {
    //todo: temporary
    //remove this block if launch by others enabled
    //list.value = {}
    //listData.value = []
    resetData()
  } else {
    if (isMyPurchased.value && subtab.value == 'featured') {
      query.uid = {$ne: pub.user._id}
      if (filters.value.workshopType?.length == 1) {
        if (filters.value.workshopType[0] == 'premium') {
          query.premium = true
        } else {
          query.premium = false
        }
      }
    }
    if (tab.value == 'mentoring' && subtab.value == 'booked') {
      const _query = {cancel: null, 'servicePackUser.snapshot.subject': query['task.service.type'], $limit: 10, $skip, session: null}
      if (isMyPurchased.value) {
        _query.tab = 'booker'
      } else {
        _query.tab = 'servicer'
      }
      if (filters.value.search) {
        _query['oldSession.name'] = {$regex: filters.value.search, $options: 'i'}
      }
      if (route.query.part == 'seeker') {
        filters.value.participant = 'educators'
      }
      list.value = await App.service('service-booking').find({query: _query})
    } else if (tab.value == 'myAssociatedTask') {
      const query = {}
      if (subtab.value === 'unassigned') {
        query.associatedTaskStatus = {$in: ['unassigned', 'cancelled', 'refunded']}
      } else {
        query.associatedTaskStatus = subtab.value
      }
      query.status = true
      const rs = await App.service('service-pack-user').find({query: {'snapshot.type': 'serviceTask', ...query}})
      list.value = rs
    } else if (tab.value == 'taskManagement') {
      if (subtab.value === 'section-tracking') {
        const query = {
          $limit: 10,
          $skip,
          $sort: {createdAt: -1},
          servicer: pub.user?._id,
        }
        const rsSections = await App.service('section-tracking').find({query})
        list.value = rsSections
      } else {
        const query = {
          $limit: 10,
          $skip,
          $sort: {createdAt: -1},
          tab: 'servicer',
          cancelled: null,
          canceledAt: null,
          completedTime: null,
          type: 'serviceTask',
        }

        try {
          const rs = await App.service('service-booking').find({query})
          list.value = rs
        } catch (error) {
          console.error('Service error:', error)
        }
      }
    } else {
      list.value = await App.service('session').find({query})
      if (list.value.data?.length) {
        await App.service('service-booking')
          .get('packListByBookingIds', {
            query: {_id: list.value.data.map((e) => e.booking)},
          })
          .then((res) => {
            list.value.data.forEach((e) => {
              const booking = res?.[e.booking]
              e.bookingInfo = booking?.packInfo
              if (booking?.topic?.length) {
                e.bookingInfo.topic = booking.topic
              }
            })
          })
      }
    }

    if (isMyServices.value && tab.value == 'mentoring' && subtab.value == 'ended') {
      const $in = []
      list.value.data.forEach((e) => {
        $in.push(e._id)
      })
      await App.service('teaching-accident')
        .find({
          query: {session: {$in}},
        })
        .then((res) => {
          res?.data?.forEach((e) => {
            const session = list.value.data.find((d) => d._id == e.session)
            if (session) {
              session.rating = 2
            }
          })
        })
    }

    if (list.value?.data?.length) {
      listData.value.push(...list.value.data)
    }
    console.log('dvfhry', listData.value)
    getAllMonthes(start, end)
  }
  loading.value = false
  const _query = {tab: tab.value, subtab: subtab.value, stype: filters.value.sessionType, type: filters.value.type?.join(), range: filters.value.range}
  if (tab.value == 'mentoring') {
    delete _query.stype
    delete _query.type
  }
  if (subtab.value !== 'ended') {
    delete _query.range
  }
  if (isMyWorkshop.value) {
    _query.part = filters.value.participant ?? 'educators'
  }
  if (Array.isArray(filters.value.taughtBy) && filters.value.taughtBy.length && !isPersonal.value) {
    _query.by = filters.value.taughtBy.join(',')
  }
  if (isCourseOnly.value) {
    _query.view = filters.value.courseView ?? 'list'
    onCourseViewClick(filters.value.courseView)
  }

  localstorageForFilter(true)
  if ($skip == 0) {
    if (isPointMode.value) {
      _query.isPointMode = 1
    }
    router.replace({query: _query})
  }

  if (importPremium.value) {
    await sleep(100)
    showImportDialog.value = true
  }
}

const getServiceBookings = async () => {}

const resetData = () => {
  listData.value = []
  list.value = {}
}

const onCourseViewClick = async (view) => {
  if (filters.value.courseView !== view) {
    filters.value.courseView = view
  }
  if (view == 'gantt') {
    await sleep(300)
    scrollToHead()
  }
}

const getAllMonthes = (start, end) => {
  months.value = []

  while (months.value.length < 12) {
    months.value.push(date.formatDate(new Date(start), 'MMM'))
    start = date.addToDate(new Date(start), {months: 1})
  }
}

const scrollToHead = () => {
  const sortedCourses = listData.value.toSorted((a, b) => {
    return new Date(a.start) - new Date(b.start)
  })

  if (sortedCourses?.length) {
    const position = barStyle(sortedCourses[0])
    scrollAreaRef.value?.setScrollPosition('horizontal', parseInt(position.left) - 150, 200)
  }
}

const barStyle = (course) => {
  let _start = course.start
  const _end = course.end
  let noEnd = false
  const range = filters.value.range.split('~')
  if (new Date(_start) < new Date(range[0])) {
    _start = range[0]
  }
  const startMonth = date.formatDate(new Date(_start), 'MMM')
  const endMonth = date.formatDate(new Date(_end), 'MMM')

  if (!_end || new Date(_end) > new Date(range[1])) {
    noEnd = true
  }

  let startIndex = 0
  let endIndex = 0

  months.value.forEach((month, index) => {
    if (month == startMonth) {
      startIndex = index
    }

    if (month == endMonth) {
      endIndex = index
    }
  })

  const leftInSeconds = date.getDateDiff(_start, range[0], 'seconds')
  const totalInseconds = date.getDateDiff(`${range[1]} 23:59:59`, range[0], 'seconds')
  const left = (leftInSeconds / totalInseconds) * 12 * 150
  const widthInSeconds = date.getDateDiff(_end, _start, 'seconds')

  let width = (widthInSeconds / totalInseconds) * 12 * 150

  if (noEnd) {
    width = 12 * 150 - left
  }

  return {left: left + 'px', width: width + 'px'}
}

const getStartOrEndOfAll = (startOrEnd) => {
  const sorted = listData.value.toSorted((a, b) => {
    if (startOrEnd == 'start') {
      return new Date(a.start) - new Date(b.start)
    } else {
      return new Date(b.end) - new Date(a.end)
    }
  })

  return sorted[0][startOrEnd]
}

const isClassRoom = (type) => {
  return ['session', 'workshop', 'taskWorkshop'].includes(type)
}

const findRooms = async (list) => {
  const arr = list.filter((v) => isClassRoom(v.type))
  const $in = arr.map((v) => v.sid)
  const rs = await App.service('rooms').get('countStatus', {query: {sid: {$in}}})
  roomStatistics.value = {...roomStatistics.value, ...rs}
}

const getSubjects = async () => {
  categoryOptions.value.push(...(await subjects.getPubPdOptions()))
  subjectOptions.value = await subjects.getOptions(pub.getSchoolOrUserId)
}

const onDrawerLoad = (activeTab) => {
  if (activeTab?.name) {
    title.value = activeTab.name
  }
}

const onTabUpdate = (val, disable) => {
  if (disable) {
    noclass.value = true
    tab.value = null
    router.replace({query: {}})
  } else {
    noclass.value = false
    tab.value = val._id
    title.value = val.name
    if (tab.value !== 'mentoring' && subtab.value == 'booked') {
      subtab.value = 'scheduled'
    } else if (tab.value == 'mentoring' && subtab.value == 'featured') {
      subtab.value = 'booked'
    } else if (tab.value == 'myAssociatedTask') {
      subtab.value = 'ongoing'
    } else if (tab.value == 'taskManagement') {
      subtab.value = 'ongoing'
    }
    if (tab.value !== 'mentoring') {
      filters.value.participant = 'students'
    }
    if (isMyWorkshop.value && tab.value == 'others') {
      list.value = {}
      listData.value = []
    } else {
      find(0)
    }
  }
}

const onFilterUpdate = () => {
  find(0)
}

const defaultRange = () => {
  const today = new Date()
  const startDate = date.startOfDate(today, 'year')
  const endDate = date.endOfDate(today, 'year')
  return `${date.formatDate(new Date(startDate), dateFormat)}~${date.formatDate(new Date(endDate), dateFormat)}`
}

const onJournalClick = () => {
  router.push({
    path: '/journal',
    query: {
      class: tab.value,
    },
  })
}

const onAnnouncementClick = () => {
  router.push({
    path: '/home/<USER>/class',
    query: {
      classId: tab.value,
      schoolId: pub.user?.schoolInfo?._id,
    },
  })
}

/*life Cycle*/
onMounted(async () => {
  loading.value = true
  if (route.path.indexOf('/home/<USER>') != -1) {
    isMyServices.value = true
    isMyWorkshop.value = true

    tab.value = 'workshop'
  }

  if (route.path.indexOf('/home/<USER>') != -1) {
    isMyPurchased.value = true
    tab.value = 'workshop'
  }

  if (isMyWorkshop.value || isMyPurchased.value) {
    isWorkshop.value = true
  }

  if (route.path.indexOf('/home/<USER>') != -1) {
    isMyClass.value = true
    if (route.query.tab) {
      tab.value = route.query.tab
    } else {
      tab.value = null
      noclass.value = true
    }
  }

  localstorageForFilter()

  if (['me', 'others', 'me,others'].includes(route.query.by)) {
    filters.value.taughtBy = route.query.by.split(',')
  } else if (!Array.isArray(filters.value.taughtBy) || !filters.value.taughtBy?.length) {
    filters.value.taughtBy = []
  }

  if (route.query.tab && ['mentoring', 'workshop', 'substitute', 'me', 'others', 'myAssociatedTask', 'taskManagement'].includes(route.query.tab)) {
    tab.value = route.query.tab
  }

  /*subtab*/
  if (tab.value == 'mentoring') {
    subtab.value = 'booked'
  }

  if (tab.value === 'myAssociatedTask') {
    subtab.value = 'ongoing'
  }

  if (tab.value === 'taskManagement') {
    subtab.value = 'ongoing'
  }

  if (route.query.subtab && ['featured', 'scheduled', 'ongoing', 'ended', 'booked', 'section-tracking'].includes(route.query.subtab)) {
    subtab.value = route.query.subtab
  }

  if (tab.value == 'workshop' && subtab.value == 'booked') {
    subtab.value = 'scheduled'
  }

  if (['educators', 'students'].includes(route.query.part) || (tab.value == 'mentoring' && route.query.part == 'seeker')) {
    filters.value.participant = route.query.part
  } else if (!filters.value.participant) {
    filters.value.participant = 'educators'
  } else {
    filters.value.participant = 'students'
  }

  if (!isMyClass.value) {
    if (route.query.stype && ['live', 'student'].includes(route.query.stype)) {
      filters.value.sessionType = route.query.stype
    } else if (!filters.value.sessionType) {
      filters.value.sessionType = 'live'
    }
  }

  if (route.query.view && ['list', 'gantt'].includes(route.query.view)) {
    filters.value.courseView = route.query.view
  } else if (!filters.value.courseView) {
    filters.value.courseView = 'list'
  }

  if (route.query.type && ['courses', 'sessions'].includes(route.query.type)) {
    filters.value.type = [route.query.type]
  } else {
    filters.value.type = []
  }

  if (route.query.range) {
    filters.value.range = route.query.range
  } else if (!filters.value.range) {
    filters.value.range = defaultRange()
  }

  if (route.query.premium) {
    importPremium.value = true
  }

  if (route.query.isPointMode) {
    isPointMode.value = true
    await pStore.getClaimSetting()
  }

  await getSubjects()
  mounted.value = true

  if (isMyPurchased.value && tab.value == 'workshop') {
    getIndexPromotion()
  }
})
</script>
<style lang="sass" scope>
.border-1
  border: 1px solid #49bbbd4d
.border-05
  border: 0.5px solid #D7E3F1
.border-top-05
  border-top: 0.5px solid #D7E3F1
.border-left-05
  border-left: 0.5px solid #D7E3F1
.border-right-05
  border-right: 0.5px solid #D7E3F1
.border-bottom-05
  border-bottom: 0.5px solid #D7E3F1
</style>
