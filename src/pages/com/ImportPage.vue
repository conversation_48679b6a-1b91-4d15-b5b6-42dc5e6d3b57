<template>
  <q-layout view="hHh Lpr lff" class="overflow-hidden">
    <q-header elevated class="bg-white text-black" :class="{hidden: isUploadToClassroom}">
      <div class="row no-wrap">
        <q-toolbar class="col-md-3 col-sm">
          <q-btn flat @click="drawer = !drawer" round dense icon="menu" class="hidden" />
          <q-btn flat round dense icon="navigate_before" @click="onBackClick"></q-btn>
          <q-toolbar-title>Import</q-toolbar-title>
        </q-toolbar>
        <div class="col gt-sm"></div>
        <div class="row col-3 q-pr-md flex items-center">
          <q-space></q-space>
        </div>
      </div>
    </q-header>

    <q-page-container>
      <q-page class="pc-max">
        <q-tabs
          v-if="isImportingFromClasscipeCloud"
          v-model="bookingtab"
          align="left"
          class="text-grey q-pt-md"
          active-color="primary"
          indicator-color="primary"
          inline-label
          mobile-arrows
          no-caps
          shrink>
          <q-route-tab
            name="content"
            label="My content"
            :to="{path: `/booking/import/${route.query.bid}`, query: {tab: 'content', to: route.query.to, group: queryGroup, back: route.query.back}}" />
          <q-route-tab
            name="students"
            label="Student's data"
            v-if="!queryGroup"
            :to="{path: `/booking/import/${route.query.bid}`, query: {tab: 'students', back: route.query.back}}" />
          <q-tab name="premiumContent" label="Premium content" @click="onNextOrImport(0)" exact />
          <q-tab name="classcipe" label="Classcipe cloud" @click="onNextOrImport(1)" exact />
        </q-tabs>
        <div class="row q-pt-md q-pl-lg" v-if="!unitId && !isImportToPublished && !isImportingFromClasscipeCloud">
          <q-tabs indicator-color="primary" v-model="tab" :outside-arrows="$q.screen.lt.sm" inline-label mobile-arrows shrink @update:model-value="requery()">
            <q-tab name="me" label="Created by me" no-caps />
            <q-tab name="other" label="Shared by others" no-caps />
          </q-tabs>
          <q-space></q-space>
        </div>
        <div class="row relative-position">
          <div v-if="activedLink && !isImportingFromClasscipeCloud" class="gt-xs absolute-top-right z-up q-pt-md q-pr-md q-mt-md q-mr-md">
            <q-btn icon="o_edit" rounded label="Edit" no-caps color="primary" @click="onEditClick"></q-btn>
          </div>
          <div class="col-12 col-sm-6">
            <div class="q-pt-md q-px-md" v-if="!unitId && mounted">
              <GeneralFilters v-model="filters" input-only :options="filterOptions" @update:modelValue="onFilterUpdate"></GeneralFilters>
            </div>
            <div
              v-if="!isEmpty(linkList)"
              :style="`height: calc(100vh - ${unitId ? '100px' : '180px'})`"
              class="overflow-auto q-px-md q-pt-sm"
              id="scroll-area-with-virtual-scroll-1">
              <q-virtual-scroll
                separator
                scroll-target="#scroll-area-with-virtual-scroll-1"
                component="q-list"
                class="col full-width overflow-auto q-pb-xl q-mb-xl"
                :items="linkList"
                @virtual-scroll="onVirtualScroll">
                <template v-slot:after>
                  <div v-if="list.data.length === list.limit" class="row justify-center q-my-md">
                    <q-spinner-ball color="primary" size="2em" class="full-width" />
                  </div>
                  <div v-else class="q-pa-md text-grey text-center hidden">It's over</div>
                </template>
                <template v-slot="{item: o, index: i}">
                  <div class="q-my-sm" @click="onLinkClick(o)">
                    <SessionBoard
                      isMyContent
                      isImport
                      :price="bookingtab == 'classcipe' || bookingtab == 'premiumContent' ? o.price : undefined"
                      :isImportToPublished="isImportToPublished"
                      :header="o.importUsers?.some((e) => e == pub.user._id) ? 'Purchased' : ''"
                      :isUnit="isImportingFromUnit"
                      :session="isImportingFromClasscipeCloud ? o.unitSnapshot : o"
                      :verification="verificationList"
                      :official="o.official"
                      :key="i"
                      :activedLink="activedLink"
                      @preview="onPreview"
                      :onLinkItemClick="onLinkItemClick"></SessionBoard>
                  </div>
                </template>
              </q-virtual-scroll>
            </div>
            <template v-else>
              <div v-if="loading" class="text-center q-pa-xl text-grey">
                <q-spinner-ball color="primary" size="2em" class="full-width" />
              </div>
              <NoData v-else />
            </template>
          </div>
          <div class="col-12 col-sm-6 gt-xs">
            <q-scroll-area ref="scrollAreaRef" class="" :style="`height: calc(100vh - ${unitId ? '100px' : '120px'})`">
              <div class="bg-white border-1-grey rounded-borders q-ma-md q-pa-md q-mb-xl q-pb-xl">
                <div v-if="activedLink && $q.screen.gt.xs">
                  <OverviewDetail v-if="isImportingFromUnit || activedLink.mode == 'tool'" :id="activedLink._id" isImport />
                  <div v-else-if="activedLink.mode === 'video'">
                    <VideoPreviewPage v-if="activedLink.video" isPreview :key="activedLink._id" :height="'400px'" :videoId="activedLink.video" />
                    <NoData v-else messageColor="grey" size="9rem" message="Please add video to this content by editing"></NoData>
                  </div>
                  <UnitView v-else :id="activedLink._id" parentIsCourse linkGroup />
                </div>
                <NoData v-else messageColor="grey" size="9rem" message="Please choose one content from the left list to view the details"></NoData>
              </div>
            </q-scroll-area>
          </div>
        </div>
      </q-page>
      <q-dialog v-model="linkDialog" v-if="$q.screen.lt.sm" maximized>
        <q-card class="overflow-hidden">
          <q-card-actions align="between">
            <q-btn flat round dense icon="close" v-close-popup />
            <q-btn v-if="!isImportingFromClasscipeCloud" dense color="primary" no-caps @click="onEditClick">
              <q-icon name="o_edit" size="1rem"></q-icon>
              <span class="q-pl-xs"> Edit </span>
            </q-btn>
          </q-card-actions>
          <q-separator />
          <q-card-section class="scroll q-pb-xl" style="max-height: calc(100% - 30px)">
            <template v-if="activedLink">
              <OverviewDetail v-if="isImportingFromUnit || activedLink.mode == 'tool'" :id="activedLink._id" isImport />
              <div v-else-if="activedLink.mode === 'video'">
                <VideoPreviewPage v-if="activedLink.video" isPreview :key="activedLink._id" :height="'400px'" :videoId="activedLink.video" />
                <NoData v-else messageColor="grey" size="9rem" message="Please add video to this content by editing"></NoData>
              </div>
              <UnitView v-else :id="activedLink._id" parentIsCourse />
            </template>
            <NoData v-else messageColor="grey" size="9rem"></NoData>
          </q-card-section>
        </q-card>
      </q-dialog>
    </q-page-container>
    <q-footer reveal elevated class="bg-white">
      <q-toolbar>
        <q-space></q-space>
        <q-btn
          class="q-mx-sm"
          :class="{'full-width': $q.screen.lt.sm}"
          color="primary"
          :label="isImportingFromClasscipeCloud ? (noNeedToBuy ? 'Import' : 'Buy and import') : isUploadToClassroom ? 'Save' : 'Next'"
          :disabled="nextDisabled"
          rounded
          no-caps
          :loading="submiting"
          @click="onNextOrSave"></q-btn>
      </q-toolbar>
    </q-footer>
  </q-layout>
</template>

<script setup>
/*
imports
*/
import {ref, onMounted, watch, computed, inject} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import {subjectsStore} from 'stores/subjects'
import {VerificationMap} from 'src/pages/teacher-verification/utils'
import SessionBoard from 'components/SessionBoard.vue'
import ZoomDialog from 'components/ZoomDialog.vue'
import ZoomAlert from 'components/ZoomAlert.vue'
import UnitView from 'components/UnitView.vue'
import OverviewDetail from 'components/OverviewDetail.vue'
import useSchool from 'src/composables/common/useSchool'
import useTeacherVerificationAuth from 'src/composables/account/teacher-verification/useTeacherVerificationAuth'
// import useSubject from 'src/composables/account/academic/useSubject.js'
import useUnit from 'src/composables/account/unit/useUnit.js'
import GeneralFilters from 'components/GeneralFilters.vue'
import {servicePackageStore} from 'stores/service-package'
import AlertDialog from 'components/AlertDialog.vue'
import VideoPreviewPage from '../InteractiveVideo/PreviewPage.vue'

/*
consts
*/
const servicePackage = servicePackageStore()
const {list: authList, getList: getAuthList} = useTeacherVerificationAuth()
// const {getSubjectCodeById} = useSubject()
const {getList, relateLinkList} = useUnit()

const pub = pubStore()
const route = useRoute()
const router = useRouter()
const loading = ref(true)
const submiting = ref(false)
const activedLink = ref(null)
const linkDialog = ref(false)
const linkList = ref([])
const list = ref([])
const tab = ref('me')
const bookingtab = ref(route.query.isOrder == 1 ? 'classcipe' : 'premiumContent')
const query = ref({})
const scrollAreaRef = ref(null)
const mode = ref(null)
const unitId = ref(null)
const subjects = subjectsStore()
const serviceType = ref(null)
const serviceTypes = ref(null)
const isImportingFromUnit = ref(false)
const isImportingFromPD = ref(false)
const isImportingFromClasscipeCloud = ref(false)
const isImportingFromTool = ref(false)
const isImportToService = ref(false)
const isImportToClass = ref(false)
const isImportToCourse = ref(false)
const isImportToSession = ref(false)
const isImportToCalendar = ref(false)
const isImportToBooking = ref(false)
const isUploadToClassroom = ref(false)
const isImportToPublished = ref(false)
const isPersonal = ref(true)

const groupId = ref('0')
const queryGroup = ref(null)
const isAllNewImport = ref(false)
const modeMap = inject('ModeMap')
const modeMapFlip = inject('ModeMapFlip')
const {userId} = useSchool()
const contentsType = inject('ContentsType')
const booking = ref({})
const mentoringType = ref(null)
const activeClasscipeCloudItem = ref({})

const filters = ref({})
const mounted = ref(false)

const isOrder = ref(route.query.isOrder)
/*
computed
*/

const filterOptions = ref([])

const nextDisabled = computed(() => {
  //return !activedLink.value || (!isImportingFromUnit.value && (!activedLink.value.filled || (route.query.premium && !activedLink.value._premium)))
  return !activedLink.value || (!isImportingFromUnit.value && !activedLink.value.filled)
})

const isVideo = computed(() => route.params.mode === '9')
const verificationList = computed(() => {
  return authList.value.filter((e) => e.status === 2 && e.type == 'workshop')
})

const noNeedToBuy = computed(() => {
  return (
    isOrder.value == 1 ||
    (activeClasscipeCloudItem.value &&
      (!activeClasscipeCloudItem.value?.unitSnapshot?.questions?.length || activeClasscipeCloudItem.value?.importUsers?.some((e) => e == pub.user._id)))
  )
})

/*
methods
*/

const subjectTitle = (subject, mentoringType) => {
  let title = 'Subject'
  if (mentoringType && mentoringType !== 'academic') {
    const subjectList = servicePackage.serviceSubjects[mentoringType]?.['topic']
    if (!subjectList) return title
    if (mentoringType === 'essay') {
      subjectList.map((topic) => {
        topic.child.map((child) => {
          if (child?._id === subject) {
            title = child.name
          }
        })
      })
      return title
    } else {
      subjectList?.map((topic) => {
        if (topic?._id === subject || topic?._id === subject?._id) {
          title = topic.name || topic.label
        }
      })
      return title
    }
  }
  return title
}

const onFilterUpdate = () => {
  //resetFn()
  requery()
}

const getType = (_attr) => {
  if (_attr.unit) return 'unit'
  if (_attr.video) return 'video'
  return 'task'
}

const onEditClick = () => {
  const o = activedLink.value
  const _attr = contentsType[o.mode]
  router.push({
    path: _attr.tool ? `/account/assessment-tool/${o._id}` : `/com/${getType(_attr)}/edit/${o._id}`,
    query: {go: 1, action: 'check'},
  })
}

const onLinkItemClick = (link) => {
  activedLink.value = link
  scrollAreaRef.value.setScrollPosition('vertical', 0, 300)
  router.replace({query: {...route.query, ...{active: link._id, activeOrderId: link.orderId}}, hash: route.hash})
}

const onLinkClick = async (link) => {
  if (isImportingFromClasscipeCloud.value) {
    try {
      await App.service('service-auth').get(link._id)
    } catch (err) {
      $q.notify({type: 'negative', message: 'Current content is not available, please select another one.'})
      requery()
      return
    }
  } else {
    try {
      await App.service('unit').get(link._id)
    } catch (err) {
      $q.notify({type: 'negative', message: 'Current content is not available, please select another one.'})
      requery()
      return
    }
  }

  activeClasscipeCloudItem.value = link
}

const onPreview = () => {
  linkDialog.value = false
  if ($q.screen.lt.sm) {
    linkDialog.value = true
  }
}

const find = async () => {
  loading.value = true
  if (unitId.value) {
    const rs = await relateLinkList({rid: unitId.value})

    if (isImportToSession.value) {
      linkList.value = rs.filter((item) => item.sessionType == 'student')
    } else {
      if (route.query.live == 'true') {
        linkList.value = rs.filter((item) => item.sessionType == 'live')
      } else if (route.query.self == 'true') {
        linkList.value = rs.filter((item) => item.sessionType == 'student' && item.mode !== 'tool')
      } else if (isPersonal.value) {
        linkList.value = rs.filter((item) => item.mode !== 'tool')
      } else {
        linkList.value = rs
      }
    }
    list.value = {limit: 10, skip: 0, total: linkList.value.length, data: []}
  } else if (isImportingFromClasscipeCloud.value) {
    if (isOrder.value == 1) {
      let query = {
        $limit: 1000,
        status: 200,
        $sort: {createdAt: -1},
        links: {
          $elemMatch: {
            style: 'premium_cloud',
            removed: {$exists: false},
            used: false,
            ...(mentoringType.value !== 'academic' ? {'goods.unitSnapshot.service.type': mentoringType.value} : {}),
          },
        },
      }
      if (filters.value.search) {
        query.links.$elemMatch['goods.unitSnapshot.name'] = {$regex: filters.value.search, $options: 'i'}
      }
      let orderList = await App.service('order').find({query})
      let cloudList = []
      orderList.data.forEach((item) => {
        item.links.forEach((link) => {
          if (link.style == 'premium_cloud' && !link.used && !link.removed) {
            link.goods.orderId = item._id
            link.goods.unitSnapshot.orderId = item._id
            cloudList.push({
              ...link.goods,
              price: +(link.price / 100).toFixed(2),
            })
          }
        })
      })
      list.value = orderList

      linkList.value = cloudList
    } else {
      query.value.status = 2
      query.value['unitSnapshot.mode'] = {$in: isVideo.value ? ['video'] : ['task', 'pdTask']}
      query.value.$sort = {updatedAt: -1}
      if (filters.value.search) {
        query.value['unitSnapshot.name'] = {$regex: filters.value.search, $options: 'i'}
      } else {
        delete query.value['unitSnapshot.name']
      }

      if (booking.value?.servicePackUser?.snapshot?.subject?.length) {
        query.value.subject = {$in: booking.value.servicePackUser.snapshot.subject}
      }

      if (booking.value?.servicePackUser?.snapshot?.topic?.length) {
        if (filters.value.topic?.length) {
          query.value['topic._id'] = {$in: filters.value.topic}
        } else {
          query.value['topic._id'] = {$in: booking.value.servicePackUser.snapshot.topic}
        }
      }

      if (booking.value?.servicePackUser?.snapshot?.curriculum) {
        query.value.curriculum = booking.value.servicePackUser.snapshot.curriculum
      }

      if (mentoringType.value !== 'academic') {
        query.value['unitSnapshot.service.type'] = mentoringType.value
      }

      if (queryGroup.value) {
        query.value['unitSnapshot.sessionType'] = 'student'
      }

      list.value = await App.service('service-auth').get('cloudList', {query: query.value})

      const data = []
      list.value?.data?.forEach((item) => {
        const price = item.unitSnapshot?.questions?.length * 50
        data.push({
          ...item,
          price: +(price / 100).toFixed(2),
        })
      })
      linkList.value.push(...data)
    }
  } else {
    query.value.tab = tab.value
    query.value.uid = tab.value === 'other' ? {$ne: userId.value} : userId.value
    query.value.$sort = {createdAt: -1}
    if (filters.value.search) {
      query.value.name = {$regex: filters.value.search, $options: 'i'}
    } else {
      delete query.value.name
    }

    query.value.del = false
    query.value.mode = Array.isArray(mode.value) ? {$in: mode.value} : mode.value

    if ('#prompts' == route.hash) {
      query.value.mode = {$in: ['task', 'pdTask']}
    }

    if (route.query.live == 'true' && !isImportingFromUnit.value) {
      query.value.sessionType = 'live'
    }

    if ((isImportToPublished.value && '#self' == route.hash) || (isImportToSession.value && !isImportingFromUnit.value)) {
      query.value.sessionType = 'student'
    }

    if (!isImportingFromTool.value) {
      if (isImportingFromUnit.value) {
        query.value.linkNum = {$gt: 0}
      }

      if ((isImportToCourse.value || isImportToService.value || isImportToSession.value) && '#educators' == route.hash) {
        query.value['service.participants'] = 'educators'
      } else if (!isImportToBooking.value) {
        query.value['service.participants'] = {$ne: 'educators'}
      }
    }

    if (route.query.subject) {
      query.value['service.type'] = route.query.subject
    }

    if (isImportToBooking.value && route.query.mentoring !== 'academic' && serviceType.value) {
      query.value['service.type'] = serviceType.value
    }

    if (isImportToBooking.value && queryGroup.value && !isImportingFromUnit.value) {
      query.value.sessionType = 'student'
    }

    list.value = await getList(query.value)
    linkList.value.push(...list.value.data)
  }
  linkList.value = linkList.value.map((e) => {
    if (e?.owner?._id != pub.user._id) {
      e.official = true
    }
    return e
  })
  loading.value = false
}

const requery = async () => {
  activedLink.value = null
  query.value.$skip = 0
  linkList.value.length = 0
  list.value = {}
  await find()
}

const onVirtualScroll = async ({index, to}) => {
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data)) return // has last page
  console.warn('need load more')
  query.value.$skip = skip + limit
  await find()
}

const onBackClick = () => {
  if (route.query.back) {
    router.replace(decodeURIComponent(route.query.back))
    //} else if (isAllNewImport.value) {
    // router.replace('/my/calendar')
  } else {
    router.back()
    //router.push('/home/<USER>')
  }
}

const onNextOrSave = async () => {
  if (isImportingFromClasscipeCloud.value) {
    if (noNeedToBuy.value) {
      submiting.value = true
      if (isOrder.value == 1) {
        await App.service('service-booking').get('importByBooking', {
          query: {serviceAuthId: activeClasscipeCloudItem.value._id, bookingId: route.query.bid, order: activeClasscipeCloudItem.value.orderId},
        })
        await App.service('order').get('usePrompt', {query: {orderId: activeClasscipeCloudItem.value.orderId, prompt: activeClasscipeCloudItem.value._id}})
        requery()
        router.replace({
          path: `/home/<USER>
          query: {tab: 'mentoring', subtab: 'scheduled', part: booking.value?.bookerInfo?.roles?.some((e) => e == 'student') ? 'students' : 'educators'},
        })
      } else {
        await App.service('service-booking').get('importByBooking', {query: {serviceAuthId: activeClasscipeCloudItem.value._id, bookingId: route.query.bid}})
        router.replace({
          path: `/home/<USER>
          query: {tab: 'mentoring', subtab: 'scheduled', part: booking.value?.bookerInfo?.roles?.some((e) => e == 'student') ? 'students' : 'educators'},
        })
      }
    } else if (queryGroup.value) {
      try {
        await App.service('service-auth').get(activeClasscipeCloudItem.value._id)
      } catch (err) {
        $q.notify({type: 'negative', message: 'Operation failed due to content not available.'})
        requery()
        return
      }
      router.push({
        path: `/order/confirm/service_import/${activeClasscipeCloudItem.value._id}`,
        query: {
          bid: route.query.bid,
          payBack: encodeURIComponent(`/com/schedule/${activeClasscipeCloudItem.value?.unit._id}/${route.query.to}/${route.query.group}`),
        },
      })
    } else {
      try {
        await App.service('service-auth').get(activeClasscipeCloudItem.value._id)
      } catch (err) {
        $q.notify({type: 'negative', message: 'Operation failed due to content not available.'})
        requery()
        return
      }
      router.push({
        path: `/order/confirm/service_import/${activeClasscipeCloudItem.value._id}`,
        query: {bid: route.query.bid, back: encodeURIComponent(route.fullPath)},
      })
    }
  } else if (isImportToPublished.value) {
    if (route.hash == '#prompts') {
      router.push({
        path: `/detail/prompts/edit/${activedLink.value._id}`,
        query: {back: route.fullPath},
      })
    } else {
      if (isVideo.value) {
        router.push({
          path: `/com/video/edit/${activedLink.value._id}`,
          query: {action: 'publish', go: true},
        })
      } else {
        router.push({
          path: `/com/task/edit/${activedLink.value._id}`,
          query: {action: 'publish', go: true},
        })
      }
    }
  } else if (isUploadToClassroom.value) {
    const {cover, name, _id} = {...activedLink.value}
    const parts = cover.split('/')
    const code = parts[parts.length - 1]
    if (top) {
      top.postMessage({app: 'web', id: _id, image: code, name}, '*')
    }
  } else if (isImportingFromUnit.value) {
    try {
      await App.service('unit').get(activedLink.value._id)
    } catch (err) {
      $q.notify({type: 'negative', message: 'Operation failed due to content not available.'})
      requery()
      return
    }
    if (isAllNewImport.value) {
      router.push({
        path: `/com/${route.params.type}/import/${modeMap.ALLTASK}/${activedLink.value._id}`,
        query: {start: route.query.start, end: route.query.end, bid: route.query.bid, live: route.query.live},
        hash: route.hash,
      })
    } else {
      if (queryGroup.value) {
        router.push({
          path: `/detail/${route.params.type}/import/${modeMap.ALLTASK}/${activedLink.value._id}`,
          query: {to: route.query.to, group: route.query.group, unit: true, self: true},
        })
      } else {
        router.push({
          path: `/detail/${route.params.type}/import/${modeMap.ALLTASK}/${route.params.to}/${groupId.value}/${activedLink.value._id}`,
          query: {subject: route.query.subject},
          hash: route.hash,
        })
      }
    }
  } else {
    try {
      await App.service('unit').get(activedLink.value._id)
    } catch (err) {
      $q.notify({type: 'negative', message: 'Operation failed due to content not available.'})
      requery()
      return
    }
    if (isImportToBooking.value) {
      if (queryGroup.value) {
        router.push({
          path: `/com/schedule/${activedLink.value._id}/${route.query.to}/${queryGroup.value}`,
          hash: route.hash,
        })
      } else {
        $q.dialog({
          component: ZoomDialog,
          componentProps: {},
        }).onOk((obj) => {
          createSessionForBooking(obj.zoom)
        })
      }
    } else if (isAllNewImport.value) {
      router.push({
        path: `/com/schedule/${activedLink.value._id}`,
        query: {start: route.query.start, end: route.query.end},
        hash: route.hash,
      })
    } else {
      router.push({
        path: `/com/schedule/${activedLink.value._id}/${route.params.to}/${groupId.value}`,
        hash: route.hash,
      })
    }
  }
}

const createSessionForBooking = async (zoom) => {
  $q.loading.show()
  const query = {zoom, block: false, premium: false, promotional: false, guest: false, status: 'live', booking: route.query.bid, students: []}
  const relatives = await getRelateList()
  const auth = await App.service('service-auth').find({query: {status: 2}})
  const associatedTask = await App.service('service-pack-user').find({
    query: {pid: booking.value.packUser, 'snapshot.type': 'serviceTask'},
  })
  const link = activedLink.value
  const isService = ['pdUnit', 'pdTask'].includes(link.mode)

  let rs = null
  if (isService) {
    rs = auth.data?.find((item) => item.curriculum == 'pd' && item.subject == link?.service?.type?.[0])
  } else {
    rs = auth.data?.find((item) => item.curriculum == link.curriculum)
  }

  if (rs) {
    query.premium = true
  }

  query.students.push(booking.value.booker)
  query.start = booking.value.start
  query.end = booking.value.end
  query.cid = link._id
  query.id = link.sid
  query.sessionType = link.sessionType
  query.subjects = link.subjects
  query.name = link.name
  query.image = link.cover
  if (associatedTask.data.length) {
    query.associatedTask = associatedTask.data[0]._id
  }

  const isInterviewTeacher = booking.value?.servicePackUser?.snapshot?.consultant?.type == 'interviewTeacher'
  if (isInterviewTeacher) {
    query.type = link.mode == 'pdTask' ? 'jobSeekerPdTask' : 'jobSeekerTask'
  } else if (link.service?.participants == 'educators') {
    query.type = link.mode == 'pdTask' ? 'bookingPdTask' : 'bookingTask'
  } else {
    query.type = link.mode == 'pdTask' ? 'bookingStuPdTask' : 'bookingStuTask'
  }
  let parentSession = null
  try {
    parentSession = await App.service('session').create(query)
  } catch (err) {
    $q.loading.hide()
    $q.dialog({
      component: AlertDialog,
      componentProps: {
        title: "The current session has been successfully imported, and you can't do it again.",
      },
    })
    console.error('Create session error:', err)
    return
  }

  const childs = []
  for (let i = 0; i < relatives.length; i++) {
    const sublink = relatives[i]
    const obj = await createRelateLinks(sublink, query, parentSession._id)
    childs.push(obj)
  }
  await App.service('session').patch(parentSession._id, {childs})

  $q.loading.hide()
  const href = `/home/<USER>
    isInterviewTeacher ? 'seeker' : link.service?.participants == 'educators' ? 'educators' : 'students'
  }`

  if (!parentSession?.zoom?.id) {
    $q.dialog({
      component: ZoomAlert,
    }).onOk(() => {
      router.replace(href)
    })
  } else {
    router.replace(href)
  }
}

const getUniqueSubjects = (item) => {
  const allSubjects = []
  item.subjects?.forEach((s) => {
    allSubjects.push({label: s.label, value: s.value})
  })
  item.outlineSubjects?.forEach((value) => {
    allSubjects.push({label: value, value})
  })
  const uniqueSubjects = allSubjects.reduce((unique, item) => {
    const found = unique.find((obj) => obj.value === item.value)
    if (!found) {
      unique.push(item)
    }
    return unique
  }, [])

  return uniqueSubjects
}

const createRelateLinks = async (link, pquery, pid) => {
  const query = {}

  query.subjects = getUniqueSubjects(link)
  query.cid = link.id ? link.id : link._id
  query.pid = pid
  query.name = link.name
  query.unitType = link.type
  query.image = link.cover
  query.pidGroup = link.pidGroup
  query.pidGroupName = link.pidGroupName

  if (link.mode == 'tool') {
    query.type = 'toolSession'
  } else if (link.mode == 'pdTask') {
    query.type = 'pdClassSession'
  } else {
    query.type = 'session'
  }

  query.students = pquery.students
  query.classId = pquery.classId
  query.className = pquery.className
  //query.booking = pquery.booking

  query.status = 'student'
  query.countdown = {deadline: null}
  query.start = new Date(link.start).toISOString()
  query.block = false
  query.premium = false
  query.promotion = false
  query.personal = true

  const subSession = await App.service('session').create(query)

  return {
    _id: subSession._id,
    sid: subSession.sid,
    cid: subSession.cid,
    mode: link.mode,
    group: link.group,
    sessionType: 'student',
    groupName: link.pidGroupName,
  }
}

const getGroupNameById = (id) => {
  const group = activedLink.value.linkGroup.find((item) => item._id == id)
  return group?.name
}

const getRelateList = async () => {
  const links = []
  const rs = await relateLinkList({rid: activedLink.value._id})
  activedLink.value.link.forEach((link, index) => {
    const meta = rs.find((item) => item._id == link.id)
    const groupName = getGroupNameById(link.group)
    links[index] = {
      ...meta,
      ...{
        _id: link._id,
        start: groupName == 'Preparations' ? new Date() : new Date(booking.value.end),
        end: 0,
        scheduled: false,
        group: link.group,
        pidGroup: link.group,
        pidGroupName: groupName,
        id: link.id,
      },
    }
  })

  return links
}

const init = () => {
  groupId.value = '0'
  unitId.value = null
  mode.value = null
  isImportingFromUnit.value = false
  isImportingFromTool.value = false
  isImportingFromPD.value = false
  isImportingFromClasscipeCloud.value = false

  isAllNewImport.value = false

  //use Map
  if ([modeMap.TASK, modeMap.PDTASK, modeMap.ALLTASK, modeMap.UNIT, modeMap.PDUNIT, modeMap.ALLUNIT, modeMap.TOOL, modeMap.VIDEO].includes(route.params.mode)) {
    mode.value = modeMapFlip[route.params.mode]
  }

  if ([modeMap.UNIT, modeMap.PDUNIT, modeMap.ALLUNIT].includes(route.params.mode)) {
    isImportingFromUnit.value = true
  }

  if ([modeMap.TOOL].includes(route.params.mode)) {
    isImportingFromTool.value = true
  }

  if ([modeMap.PDTASK, modeMap.PDUNIT].includes(route.params.mode)) {
    isImportingFromPD.value = true
  }

  if ([modeMap.CLASSCIPE].includes(route.params.mode)) {
    isImportingFromClasscipeCloud.value = true
  }

  if (pub.user.schoolInfo?._id) {
    isPersonal.value = false
  }

  switch (route.params.type) {
    case 'service':
      isImportToService.value = true
      break
    case 'class':
      isImportToClass.value = true
      break
    case 'classroom':
      isUploadToClassroom.value = true
      break
    case 'published':
      isImportToPublished.value = true
      break
    case 'course':
      isImportToCourse.value = true
      break
    case 'session':
      isImportToSession.value = true
      break
    case 'booking':
      isImportToBooking.value = true
      break
    default:
      isImportToCalendar.value = true
  }

  if (route.params.group) {
    groupId.value = route.params.group
  }

  if (route.params.from) {
    unitId.value = route.params.from
  }

  if (!route.params.group && !route.params.from && !route.query.group) {
    isAllNewImport.value = true
    if (route.params.id) {
      unitId.value = route.params.id
    }
  }

  if (route.query.group) {
    if (route.query.unit) {
      unitId.value = route.params.id
    }
    queryGroup.value = route.query.group
  }
}

const getServiceTypes = async () => {
  if (isImportToBooking.value) {
    if (route.query.mentoring !== 'academic') {
      serviceType.value = route.query.mentoring
    }
    // if (queryGroup.value) {
    //   //https://github.com/zran-nz/bug/issues/5623
    // } else {
    //   serviceTypes.value = await subjects.getOptions('1', 'pd')
    //   const label = VerificationMap?.[`mentoring:${route.query.mentoring}`]?.label
    //   serviceType.value = serviceTypes.value?.find((item) => item.label == label)?.value
    // }
  }
}

const getBooking = async () => {
  if (isImportToBooking.value && route.query.bid) {
    console.log('🚀 ~ getBooking ~ route.query.bid:', route.query.bid)
    booking.value = await App.service('service-booking').get(route.query.bid)
    mentoringType.value = booking.value?.mentoringType
    //??mentoringType.value = booking.value?.servicePackUser?.snapshot?.mentoringType
    if (queryGroup.value) {
      if (booking.value?.servicePackUser?.snapshot?.curriculum == 'pd') {
        mentoringType.value = booking.value?.servicePackUser?.snapshot?.unitSnapshot?.service?.type?.[0]
      } else {
        mentoringType.value = 'academic'
      }
    }
  }
}

const onNextOrImport = (_isOrder = 0) => {
  const path = `/com/booking/import/${route.params.mode}`
  const query = {mentoring: mentoringType.value, bid: route.query.bid, live: false}
  if (route.query.to) {
    query.to = route.query.to
  }
  if (queryGroup.value) {
    query.group = queryGroup.value
  }

  query.isOrder = _isOrder

  router.push({path, query})
  isOrder.value = _isOrder
  init()
  requery()
}

watch(
  () => route.params.mode,
  () => {
    init()
    requery()
  }
)

onMounted(async () => {
  init()
  await getBooking()
  await getServiceTypes()
  await getAuthList(true, {$skip: 0, $limit: 2000})
  if (unitId.value) {
    await find()
  } else {
    //onFilterUpdate
  }
  if (route.query.active) {
    activedLink.value = linkList.value.find((e) => e._id == route.query.active)
  }

  if (isImportingFromClasscipeCloud.value && mentoringType.value !== 'academic') {
    await servicePackage.init(mentoringType.value)
    filterOptions.value.push({
      key: 'topic',
      label: 'Topic',
      type: 'checkbox',
      options: booking.value?.servicePackUser?.snapshot?.topic?.map((e) => ({value: e, label: subjectTitle(e, mentoringType.value)})),
    })
  }
  mounted.value = true
})
</script>
<style lang="sass" scope>
.border-1-grey
  border: 1px solid $grey-4
.border-1
  border: 1px solid #49bbbd4d
.border-05
  border: 0.5px solid #D7E3F1
.z-up
  z-index: 10 !important
</style>
