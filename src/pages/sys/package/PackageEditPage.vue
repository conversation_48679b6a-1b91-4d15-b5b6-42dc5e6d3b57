<template>
  <q-layout view="hHh LpR fFf">
    <div v-if="servicePackage.type === 'serviceTask'" class="shadow-1">
      <q-sticky>
        <q-toolbar>
          <q-btn flat round icon="chevron_left" @click="goBack" />
          <q-toolbar-title class="text-bold">{{ editProductDetail ? 'Editing product details' : 'Task details' }}</q-toolbar-title>
        </q-toolbar>
      </q-sticky>
    </div>
    <q-page-container class="pc-sm">
      <q-page class="q-ma-md">
        <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-md" v-if="unPublishStatus">
          <template v-if="servicePackage.contentOrientatedEnable">
            <template v-if="reasonStatus?.includes(servicePackage.reason)">
              The current service package is under unpublished status, as the bundled service package does not exist.</template
            >
            <template v-else>The current service package is under unpublished status, as the linked lecture does not exist.</template>
          </template>
          <template v-slot:action>
            <q-btn flat icon="close" class="q-pa-sm" @click="unPublishStatus = false" />
          </template>
        </q-banner>
        <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-md" v-if="packageBannerStatus">
          Your current service package has been unpublished as the bundled mentor/carer/interview service package is removed.
          <template v-slot:action>
            <q-btn flat icon="close" class="q-pa-sm" @click="packageBannerStatus = false" />
          </template>
        </q-banner>
        <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-md" v-if="lectureBannerStatus">
          Your current service package has been unpublished as the user has withdrawn the linked premium course material in teacher & service verification.
          <template v-slot:action>
            <q-btn flat icon="close" class="q-pa-sm" @click="lectureBannerStatus = false" />
          </template>
        </q-banner>
        <ProductDetailEdit
          v-if="servicePackage.type === 'serviceTask' && editProductDetail === false"
          isEditAllow="true"
          :openEditProductDetail="openEditProductDetail" />
        <template v-if="editProductDetail === true"
          ><q-card class="q-my-md rounded-borders-md">
            <q-card-section>
              <div class="text-h6">Product detail</div>
              <div id="error-cover">
                <div class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.cover && isPublishAction}">* Upload image</div>
                <br />
                <div class="row">
                  <div class="q-mr-sm">
                    <q-card
                      class="rounded-borders-md bg-teal-1"
                      style="width: 300px; max-width: 300px; height: 200px; max-height: 200px"
                      @click="uploadCoverImage()">
                      <div v-if="!servicePackage.cover" class="absolute-center text-center text-teal">
                        <q-icon flat name="upload" size="2rem"></q-icon>
                        <q-item-label>Click to select or drag file to upload</q-item-label>
                      </div>
                      <div v-else>
                        <q-img
                          fit="cover"
                          :src="hashToUrl(servicePackage.cover)"
                          style="width: 300px; max-width: 300px; height: 200px; max-height: 200px"></q-img>
                      </div>
                    </q-card>
                  </div>
                  <div class="column reverse" v-if="servicePackage.cover">
                    <div class="self-end q-ml-sm">{{ servicePackage.coverName }}</div>
                    <div v-if="editable">
                      <q-btn flat round color="teal" icon="change_circle" @click="uploadCoverImage()"></q-btn>
                      <q-btn flat round color="red" icon="delete" @click="deleteCover()"></q-btn>
                    </div>
                  </div>
                </div>
              </div>
              <div class="text-weight-medium">
                <br />
                <div class="text-subtitle1 text-weight-medium">
                  Service type:
                  <q-chip square color="teal-1" text-color="teal-7" style="font-weight: 500" :ripple="false">{{
                    servicePackage.mentoringTypeList.find((e) => e.value == servicePackage.mentoringType)?.label
                  }}</q-chip>
                </div>
                <div class="text-subtitle1 text-weight-medium">
                  Service provider qualification:
                  <q-chip square color="teal-1" text-color="teal-7" style="font-weight: 500" :ripple="false">{{
                    servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label
                  }}</q-chip>
                </div>
              </div>
              <div id="error-name">
                <br />
                <div class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.name && isPublishAction}">* Product name</div>
                <br />
                <q-input
                  class="q-mx-md"
                  :disable="!editable"
                  outlined
                  @change="checkDataFinished(false)"
                  v-model.trim="servicePackage.name"
                  stack-label></q-input>
                <br />
                <div class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.name && isPublishAction}">* Product description</div>
                <br />
                <q-input
                  class="q-mx-md"
                  :disable="!editable"
                  outlined
                  @change="checkDataFinished(false)"
                  v-model.trim="servicePackage.description"
                  stack-label></q-input>
              </div>

              <div id="error-points">
                <br />
                <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.points && isPublishAction}"
                  >* Selling points</q-item-label
                >
                <q-item v-for="(point, i) in servicePackage.points" :key="i">
                  <q-item-section>
                    <q-input
                      class="q-mt-sm"
                      @change="checkDataFinished(false)"
                      v-model="servicePackage.points[i]"
                      outlined
                      placeholder="E.g. Essay tutoring 10 times"></q-input>
                  </q-item-section>
                  <q-item-section avatar v-if="servicePackage.points.length > 1">
                    <q-btn flat icon="close" @click="deleteSellingPointClick(i)"></q-btn>
                  </q-item-section>
                </q-item>
                <q-btn
                  v-if="sellingPointAddShow"
                  class="q-mt-md text-teal"
                  flat
                  outlint
                  no-caps
                  icon="add"
                  label="Add selling point"
                  @click="addSellingPoint()"></q-btn>
              </div>
            </q-card-section>
          </q-card>
          <q-card-section class="row items-center justify-between">
            <q-btn
              class="q-mx-sm"
              :disable="!servicePackage.points?.[0].trim() || !servicePackage.name || !servicePackage.description || !servicePackage.cover"
              color="grey"
              text-color="white"
              rounded
              no-caps
              :style="{
                width: '1088px',
                height: '40px',
                fontFamily: 'Roboto',
                fontWeight: '500',
                fontSize: '14px',
                lineHeight: '20px',
                color: '#F1F9F9',
                boxShadow: '0px 1px 3px 1px #00000026, 0px 1px 2px 0px #00000026',
              }"
              @click="isSaveActive">
              Save
            </q-btn>
          </q-card-section>
        </template>
        <Sections v-if="servicePackage.type === 'serviceTask' && editProductDetail === false" :sections="servicePackage.sections" />

        <q-card-section v-if="servicePackage.type === 'serviceTask' && editProductDetail === false">
          <div class="row q-col-gutter-sm q-mt-md">
            <div class="col">
              <Auth :action="'publish'">
                <q-btn
                  class="full-width"
                  :color="isPublishActive ? 'primary' : 'grey'"
                  rounded
                  no-caps
                  :label="servicePackage.status ? 'Unpublish' : 'Publish'"
                  :icon="!servicePackage.status ? `publish` : ``"
                  :disable="!isPublishActive"
                  @click="handlePublishTask" />
              </Auth>
            </div>
            <div class="col" v-if="!servicePackage.status">
              <Auth :action="'delete'">
                <q-btn
                  class="full-width"
                  :disable="servicePackage.status || servicePackage.count?.sold"
                  rounded
                  label="Delete"
                  no-caps
                  icon="delete"
                  color="negative"
                  @click="handleDeleteTask" />
              </Auth>
            </div>
          </div>
        </q-card-section>
        <div v-if="servicePackage.type !== 'serviceTask'">
          <q-card class="q-my-md rounded-borders-md">
            <q-card-section>
              <div class="text-h6">Basic setting for this package</div>
              <div class="text-weight-medium">
                <div>Service role: {{ servicePackage.serviceRolesList.find((e) => e.value == servicePackage.serviceRoles)?.label }}</div>
                <div>Service type: {{ servicePackage.mentoringTypeList.find((e) => e.value == servicePackage.mentoringType)?.label }}</div>
                <div>Service provider qualification: {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}</div>
                <div v-if="servicePackage.serviceRoles === 'mentoring'">
                  Premium content orientated: {{ servicePackage.contentOrientatedEnable ? 'Yes' : 'No' }}
                </div>
                <div v-if="servicePackage.serviceRoles === 'consultant'">
                  Consultant type: {{ servicePackage.consultantTypeList.find((e) => e.value == servicePackage.consultant?.type)?.label }}
                </div>
              </div>
            </q-card-section>
          </q-card>
          <q-card v-if="servicePackage.countryCodeRequired" id="error-countryCode">
            <q-card-section>
              <div class="row items-center">
                <div class="text-subtitle1 text-weight-medium col">*Country</div>
              </div>
              <div class="q-mt-md">
                <q-chip
                  color="primary"
                  text-color="white"
                  outline
                  square
                  :label="overseasStudyList.find((e) => e.value == servicePackage.countryCode?.[0])?.label"></q-chip>
              </div>
            </q-card-section>
          </q-card>

          <q-card class="q-my-md rounded-borders-md">
            <q-card-section>
              <div class="row items-center">
                <div class="text-subtitle1 text-weight-medium col">*Package detail</div>
                <q-btn flat icon="o_edit" v-if="!isViewAction" @click="editDetail"></q-btn>
              </div>
              <div id="error-cover">
                <div class="text-weight-medium" :class="{'text-negative': errorPositions.cover && isPublishAction}">* Upload image</div>
                <q-img
                  v-if="servicePackage.cover"
                  class="rounded-borders-md q-mt-md"
                  fit="cover"
                  :src="hashToUrl(servicePackage.cover)"
                  style="width: 300px; max-width: 300px; height: 200px; max-height: 200px" />
              </div>
              <div class="text-weight-medium q-mt-md" id="error-name" :class="{'text-negative': errorPositions.name && isPublishAction}">
                * Package description
                <div class="q-mt-sm q-ml-sm">
                  {{ servicePackage.name }}
                </div>
              </div>
              <div class="text-weight-medium q-mt-md" id="error-points" :class="{'text-negative': errorPositions.points && isPublishAction}">
                * Selling points
                <div class="q-mt-sm q-ml-sm" v-for="point in servicePackage.points" :key="point">
                  {{ point }}
                </div>
              </div>
            </q-card-section>
          </q-card>

          <q-card class="q-my-md rounded-borders-md" v-if="['mentoring', 'consultant'].includes(servicePackage.serviceRoles)">
            <q-card-section>
              <div id="error-duration">
                <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.duration && isPublishAction}">
                  * Service block setting
                </q-item-label>
                <div v-if="servicePackage.duration" class="text-primary q-mt-md text-bold">
                  Session duration: {{ servicePackage.duration }} mins
                  <template v-if="servicePackage.break"> (with {{ servicePackage.break }} mins break) </template>
                  <template v-else> (without break) </template>
                </div>
                <br />
                <q-btn
                  rounded
                  no-caps
                  color="primary"
                  class="q-mb-md"
                  :label="servicePackage.duration && servicePackage.break ? 'View the service block' : 'Set the service block'"
                  @click="openBlockClick()"></q-btn>
              </div>
            </q-card-section>
          </q-card>
          <q-card class="q-my-md rounded-borders-md" v-if="!isSubstitute">
            <q-card-section>
              <div id="error-freq">
                <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.freq && isPublishAction}">
                  <div class="row items-center">
                    <div class="col">* Minimal frequency of the service</div>
                    <template v-if="!isViewAction">
                      <q-btn flat icon="o_edit" v-if="!(!editable || readonly)" @click="editFreq"></q-btn>
                    </template>
                  </div>
                </q-item-label>
                <q-chip
                  class="q-mt-md"
                  outline
                  v-if="servicePackage.freq"
                  :label="frequencyMap[servicePackage.freq]"
                  color="primary"
                  text-color="white"
                  no-caps
                  square />
              </div>
            </q-card-section>
          </q-card>

          <q-card class="q-my-md rounded-borders-md">
            <q-card-section>
              <div class="row items-center">
                <div class="col text-subtitle1 text-weight-medium">* Package tag setting</div>
                <template v-if="!isViewAction">
                  <q-btn flat icon="o_edit" v-if="!(!editable || tagSettingDisable || readonly)" @click="editTagSetting" />
                </template>
              </div>

              <div v-if="servicePackage.mentoringType" id="error-curriculum">
                <q-item-label class="text-subtitle1 text-weight-medium q-mt-md">*Service type/Curriculum</q-item-label>
                <div class="q-pa-md">
                  {{
                    servicePackage.mentoringType == 'academic'
                      ? servicePackage.curriculum?.label
                      : servicePackage.mentoringTypeList.find((e) => e.value == servicePackage.mentoringType)?.label
                  }}
                </div>
              </div>

              <div
                id="error-subject"
                v-if="
                  servicePackage.type && servicePackage.mentoringType && servicePackage.curriculum.value && servicePackage.mentoringType !== 'overseasStudy'
                ">
                <q-item-label class="text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.subject && isPublishAction}"
                  >* {{ subjectBlockTitle }} ({{ isMultipleSubjectChoice ? 'Multiple choice' : 'Single choice' }})</q-item-label
                >

                <div class="q-py-md" v-if="servicePackage.topicWithChilds">
                  <template v-for="item in essayOrTeacherTrainingSubjectLists" :key="item.label">
                    {{ item?.label }}
                    <div class="q-mt-sm">
                      <q-chip
                        v-for="(subject, i) in item?.value?.filter((e) => e.selected)"
                        outline
                        :key="i"
                        :label="subject.name"
                        color="primary"
                        text-color="white"
                        no-caps
                        square />
                    </div>
                  </template>
                </div>
                <div class="q-py-md" v-else>
                  <span v-for="(subject, i) in currentSubjectList?.filter((e) => e.selected)" :key="i" style="display: inline-block">
                    <span>
                      <q-chip color="primary" text-color="white" outline square :label="subject.name" />
                    </span>
                    <br />
                    <span class="q-mr-md text-caption text-grey-5" style="float: right">{{ subject.subtitle }}</span>
                  </span>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <q-card class="q-my-md rounded-borders-md" v-if="servicePackage.type && servicePackage.mentoringType && servicePackage.curriculum">
            <q-card-section>
              <div id="error-grades">
                <div class="row items-center">
                  <div class="col text-subtitle1 text-weight-medium" :class="{'text-negative': errorPositions.grades && isPublishAction}">
                    * Grade {{ `(${isMultipleGradeChoice ? 'Multiple choice' : 'Single choice'})` }}
                  </div>
                  <template v-if="!isViewAction">
                    <q-btn flat icon="o_edit" v-if="!(!editable || tagSettingDisable || readonly)" @click="editGrade"></q-btn>
                  </template>
                </div>

                <div v-if="servicePackage.isEducatorServiceType">
                  <q-chip
                    v-for="(label, i) in gradeList?.filter((e) => servicePackage.gradeGroup.grades?.includes(e))"
                    :key="i"
                    outline
                    :label="label"
                    color="primary"
                    text-color="white"
                    no-caps
                    square />
                </div>
                <div class="col row" v-else>
                  <div class="q-my-sm">
                    <div v-for="(gradesGroup, i) in gradeList" :key="i">
                      <template v-if="servicePackage.gradeGroup.grades?.includes(i)">
                        <div class="q-py-xs" v-if="gradesGroup.grades[servicePackage.curriculum.value]?.length || servicePackage.mentoringType !== 'academic'">
                          <q-chip color="primary" text-color="white" square outline :label="gradesGroup.label" />
                          <template v-if="servicePackage.mentoringType === 'academic'">
                            <span v-for="(grade, i) in gradesGroup.grades[servicePackage.curriculum.value]" :key="i" class="text-grey-5 q-ma-sm">{{
                              grade
                            }}</span>
                          </template>
                          <template v-else>
                            <span v-for="(grade, i) in gradesGroup.grades.classcipe" :key="i" class="text-grey-5 q-ma-sm">{{ grade }}</span>
                          </template>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <template v-if="servicePackage.contentOrientatedEnable">
            <q-card class="q-my-md rounded-borders-md" id="error-content">
              <q-card-section>
                <div class="text-h6" :class="{'text-negative': errorPositions.content && isPublishAction}">* Premium content setting</div>
                <div class="rounded-borders q-mt-md" v-for="(subject, i) in selectedSubjectsOrCountry" :key="i">
                  <template v-if="isMultipleContents || (!isMultipleContents && i == 0)">
                    <div class="row items-center q-pa-md justify-between">
                      <div class="col row" :class="`${isPublishAction && addContentEnable(subject) ? 'text-negative' : 'text-primary'}`">
                        <template v-if="servicePackage.topicWithChilds">
                          <q-chip color="teal-1" text-color="primary" :ripple="false" :label="showEssayOrTeacherTrainingSubjectLabel(subject)"></q-chip>
                          <q-chip color="primary" outline square :ripple="false" :label="subject.name"></q-chip>
                        </template>
                        <template v-else-if="isMultipleContents">
                          <div class="q-pr-md">{{ subject.name }}</div>
                        </template>
                        <template v-else-if="servicePackage.mentoringType == 'overseasStudy'">
                          <div class="q-pr-md">{{ subject.label }}</div>
                        </template>
                        <template v-else>
                          <div v-for="(e, i) in selectedSubjectsOrCountry" :key="i" class="q-pr-md">{{ e.name }}</div>
                        </template>
                      </div>
                      <q-btn
                        v-if="addContentEnable(subject)"
                        rounded
                        no-caps
                        color="primary"
                        :disabled="addPremiumContentDisable"
                        label="Add content"
                        icon="o_add"
                        @click="onUploadClick(subject)" />
                    </div>
                    <q-expansion-item
                      default-opened
                      v-for="(item, index) in servicePackage.contentOrientated.filter(
                        (e) => (isMultipleContents && e.subject == subject._id) || !isMultipleContents
                      )"
                      :key="index">
                      <template v-slot:header>
                        <q-item-section @click.stop="onPremiumContentClick(item)">
                          <AuthUnitCard :item="item.premium" v-if="item.premium?._id">
                            <div class="col-auto row justify-end" v-if="!isViewAction && !isServicePackApply">
                              <q-btn rounded outline color="negative" icon="o_delete" @click.stop="onDeleteClick(item, true)" label="Remove" no-caps></q-btn>
                            </div>
                          </AuthUnitCard>
                        </q-item-section>
                        <q-item-section side> </q-item-section>
                      </template>

                      <div class="bg-teal-1 rounded-borders q-pa-md">
                        <div class="row q-pb-md items-center">
                          <div class="col-8">
                            <div>No. of sessions required to complete this course</div>
                          </div>
                          <div class="col-2 text-body1 text-weight-medium">{{ item.times }}</div>
                          <div class="col-2 hidden">
                            <q-btn dense rounded flat color="primary" icon="o_edit" @click.stop="onTimesClick(item)"></q-btn>
                          </div>
                        </div>
                        <template v-if="!isViewAction">
                          <q-btn
                            v-if="!item.servicePack"
                            rounded
                            color="primary"
                            label="Add bundled mentor service package"
                            no-caps
                            @click.stop="onAddClick(item)" />
                        </template>
                        <div v-if="item.servicePack" class="row">
                          <div class="col">
                            <PackageCard
                              v-if="item.servicePack?._id"
                              :is-educator="false"
                              :service-no="parseInt(item.times)"
                              :remove="!isViewAction"
                              header="Bundled mentor service package"
                              @remove="onDeleteClick(item, false)"
                              :pack="item.servicePack"
                              orientated
                              non-clickable
                              category="featured" />
                          </div>
                        </div>
                      </div>
                    </q-expansion-item>
                  </template>
                </div>
              </q-card-section>
            </q-card>
          </template>
          <q-card
            v-if="
              (servicePackage.serviceRoles === 'mentoring' && servicePackage.mentoringType !== 'academic' && !servicePackage.contentOrientatedEnable) ||
              (servicePackage.serviceRoles === 'consultant' && servicePackage.consultant?.type === 'interviewThesisDefense')
            "
            class="q-my-md rounded-borders-md">
            <q-card-section>
              <div class="row items-center">
                <div class="text-h6">* Associated Task</div>
                <q-btn
                  v-if="!servicePackage.associatedTask?._id"
                  :disable="servicePackage.status"
                  rounded
                  outline
                  no-caps
                  size="sm"
                  icon="add"
                  class="q-ml-md text-primary"
                  @click="handleAddAssociotedTask"
                  ><span class="text-body2" style="font-size: 12px">Add</span></q-btn
                >
                <q-btn v-else rounded outline no-caps size="sm" class="q-ml-md text-primary" style="cursor: default; pointer-events: none"
                  ><span class="text-body2" style="font-size: 12px">Added</span></q-btn
                >
              </div>
              <TaskCard
                v-if="servicePackage.associatedTask?._id && servicePackage.addedAssociatedTask"
                :isPublished="servicePackage.status"
                :isView="true"
                :deleteEnable="!isViewAction"
                :task="servicePackage.addedAssociatedTask" />
              <div v-else-if="isServiceLoading">
                <q-inner-loading :showing="visible" label="Please wait..." label-class="text-teal" label-style="font-size: 1.1em" />
              </div>
            </q-card-section>
          </q-card>
          <q-card id="error-carerPack" class="q-my-md rounded-borders-md" v-if="addCarerPackageEnable">
            <q-card-section>
              <div class="row items-center">
                <div class="text-h6" :class="{'text-negative': errorPositions.carerPack && servicePackage.bundledCarer && isPublishAction}">
                  <span v-if="servicePackage.bundledCarer">*</span>
                  Bundled carer service package
                </div>
                <q-toggle v-model="servicePackage.bundledCarer" v-if="!isViewAction" @update:modelValue="onBundledCarerUpdate" size="lg" color="primary" />
              </div>
              <q-btn
                v-if="servicePackage.bundledCarer && !servicePackage.carerPack?._id"
                rounded
                color="primary"
                label="Add package"
                icon="add"
                no-caps
                @click.stop="onAddPackageClick('carer')"></q-btn>
            </q-card-section>
            <template v-if="servicePackage.carerPack?._id && servicePackage.bundledCarer">
              <q-card-section class="row">
                <div class="col">
                  <PackageCard
                    :is-educator="false"
                    :remove="!isViewAction"
                    @remove="onRemoveClick('carer')"
                    :pack="servicePackage.carerPack"
                    :serviceNo="servicePackage.totalTimes"
                    orientated
                    non-clickable
                    category="featured" />
                </div>
              </q-card-section>
            </template>
          </q-card>
          <q-card class="q-my-md rounded-borders-md" id="error-price">
            <q-card-section>
              <template v-if="servicePackage.contentOrientatedEnable">
                <PremiumContent
                  :isServicePackApply="isServicePackApply"
                  :isViewAction="isViewAction"
                  :readonly="readonly"
                  :error="errorPositions.price && isPublishAction"
                  @update="onPremiumContentUpdate" />
              </template>
              <template v-else-if="isSubstitute">
                <template v-if="servicePackage.isOnCampus">
                  <OnCampusPrice
                    :isViewAction="isViewAction"
                    :readonly="readonly"
                    @update="substituteUpdate"
                    :currentSubjectList="currentSubjectList"
                    :error="errorPositions.price && isPublishAction" />
                </template>
                <template v-else>
                  <OnlineSubs :isViewAction="isViewAction" @update="substituteUpdate" :error="errorPositions.price && isPublishAction" />
                </template>
              </template>
              <template v-else>
                <div class="row items-center">
                  <div class="text-subtitle1 text-weight-medium col" :class="{'text-negative': errorPositions.price && isPublishAction}">* Price setting</div>
                  <template v-if="!isViewAction">
                    <q-btn flat icon="o_edit" @click="onPriceSettingClick"></q-btn>
                  </template>
                </div>
                <div class="row items-center q-mt-md">
                  <div class="text-bold q-mr-md">Sold to</div>
                  <div class="col">
                    {{ servicePackage.salesTarget?.includes('personal') && 'Individual user' }}
                  </div>
                </div>
                <div v-if="servicePackage.discountConfig.enable" class="q-mt-md">
                  <span class="text-primary">Discount active </span>
                  <span class="q-ml-sm">{{ servicePackage.discountConfig.discount }} %</span>
                  <template v-if="servicePackage.discountConfig.end">
                    <span class="text-primary q-ml-md">Discount ends</span>
                    <span class="q-ml-sm">{{ servicePackage.discountConfig.end }}</span>
                  </template>
                </div>

                <div class="text-bold row items-center q-mt-md">
                  <span class="text-h3">$</span>
                  <span class="q-ml-sm">{{ displpayPrice }}</span>
                  <span class="q-ml-md"> Per Session</span>
                </div>
                <div class="bg-teal-2 q-pa-md q-mt-md rounded-borders-sm row items-center">
                  <div class="text-bold">
                    Qualification:
                    {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}
                  </div>
                  <div class="col text-right text-grey-6 text-bold">
                    cost per session =
                    <span class="text-primary cursor-pointer">
                      ${{ ((servicePackage.hourRate * servicePackage.duration) / 60).toFixed(2) }}
                      <q-tooltip
                        >{{ servicePackage.hourRate }} * {{ servicePackage.duration }} / 60 =
                        {{ ((servicePackage.hourRate * servicePackage.duration) / 60).toFixed(2) }}
                      </q-tooltip>
                    </span>
                  </div>
                </div>
                <div v-if="displpayPrice > 0">
                  <q-card class="q-my-md q-py-md bg-teal-1" v-for="(discount, i) in servicePackage.discount" :key="i">
                    <q-item>
                      <q-item-section class="relative-position">
                        <span class="bg-white q-px-sm top_tips">No of session</span>
                        <q-item-label class="">{{ discount.count }}</q-item-label>
                      </q-item-section>
                      <q-item-section class="relative-position">
                        <span class="bg-white q-px-sm top_tips">Rate/Session</span>
                        <q-item-label class="">{{ ((displpayPrice * discount.discount) / 100).toFixed(2) }}</q-item-label>
                      </q-item-section>
                      <q-item-section class="relative-position">
                        <span class="bg-white q-px-sm top_tips">No of gifts</span>
                        <q-item-label class="" v-if="discount.gifts || discount.enableGift">{{ discount.gifts }}</q-item-label>
                      </q-item-section>
                      <q-item-section class="q-ml-md relative-position" style="width: 70px">
                        <span class="bg-white q-px-sm top_tips">Discount</span>
                        <q-item-label class="" v-if="servicePackage.discountConfig?.enable && servicePackage.discountConfig?.discount">
                          {{ servicePackage.discountConfig.discount }} %</q-item-label
                        >
                      </q-item-section>
                      <q-item-section side>
                        <q-item-label> ${{ ((displpayPrice * discount.discount * discount.count) / 100).toFixed(2) }}</q-item-label>
                        <q-item-label class="text-h5 text-teal">
                          ${{
                            (
                              (((displpayPrice * discount.discount * discount.count) / 100) *
                                (100 - (servicePackage.discountConfig.enable ? servicePackage.discountConfig?.discount || 0 : 0))) /
                              100
                            ).toFixed(2)
                          }}
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-card>
                </div>
                <q-card v-if="servicePackage.associatedTask?._id && servicePackage.addedAssociatedTask" class="q-my-md rounded-borders-md">
                  <q-card-section>
                    <div class="items-center">
                      <div class="text-weight-bold" style="font-size: 18px">* Associated Task</div>
                      <q-card class="bg-teal-2">
                        <div class="row justify-between full-width q-pa-sm" style="height: 100px">
                          <div class="row items-center">
                            <q-img
                              :src="hashToUrl(servicePackage.addedAssociatedTask.cover)"
                              style="width: 100px; height: 60px; border-radius: 8px"
                              class="q-mr-md" />
                            <div class="col">
                              <div class="text-body1 text-weight-bold">{{ servicePackage.addedAssociatedTask.name }}</div>
                              <div class="text-weight-medium" style="font-size: 13px">
                                Qualification: {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}
                              </div>
                            </div>
                          </div>
                          <div class="col" style="justify-items: end">
                            <div class="bg-white q-px-sm">No. of Unit</div>
                            <div class="q-px-sm">{{ servicePackage.addedAssociatedTask.sections.length ?? 0 }}</div>
                          </div>
                          <div class="col" style="justify-items: end">
                            <div class="bg-white q-px-sm">Discount</div>
                            <div class="q-px-sm">{{ servicePackage.discountConfig?.discount ?? 0 }} %</div>
                          </div>
                          <div class="col content-center" style="justify-items: end">
                            <div>
                              <s v-if="servicePackage.discountConfig.enable">${{ (servicePackage.addedAssociatedTask.price / 100).toFixed(2) }}</s>
                            </div>
                            <div class="text-teal text-h6">
                              ${{ ((servicePackage.addedAssociatedTask.price / 100) * (1 - servicePackage.discountConfig?.discount / 100)).toFixed(2) ?? 0 }}
                            </div>
                          </div>
                        </div>
                      </q-card>
                    </div>
                  </q-card-section>
                </q-card>
              </template>
            </q-card-section>
          </q-card>

          <q-card id="error-interviewPack" class="q-my-md rounded-borders-md" v-if="interviewOrAcademicCheckEnable">
            <q-card-section>
              <div class="row items-center">
                <div class="text-h6" :class="{'text-negative': errorPositions.interviewPack && servicePackage.bundledInterview && isPublishAction}">
                  <span v-if="servicePackage.bundledInterview">*</span>
                  Bundled interview for enrolment
                </div>
                <q-toggle
                  v-model="servicePackage.bundledInterview"
                  v-if="!isViewAction"
                  @update:modelValue="onBundledInterviewUpdate"
                  size="lg"
                  color="primary" />
              </div>
              <q-btn
                v-if="servicePackage.bundledInterview && !servicePackage.interviewPack?._id"
                rounded
                color="primary"
                label="Add package"
                icon="add"
                no-caps
                @click.stop="onAddPackageClick('interview')"></q-btn>
            </q-card-section>
            <q-card-section v-if="servicePackage.interviewPack?._id && servicePackage.bundledInterview" class="row">
              <div class="col">
                <PackageCard
                  :is-educator="false"
                  :remove="!isViewAction"
                  @remove="onRemoveClick('interview')"
                  :pack="servicePackage.interviewPack"
                  orientated
                  non-clickable
                  category="featured" />
              </div>
            </q-card-section>
          </q-card>
          <AcBgCheck v-if="interviewOrAcademicCheckEnable" :isViewAction="isViewAction" />
          <q-card class="q-my-md rounded-borders-md">
            <q-card-section id="error-attachments">
              <div class="text-h6" :class="{'text-negative': errorPositions.attachments && isPublishAction}">* Promotional material</div>
              <div>
                The materials inserted here will be displayed during the workshop when this service packages is added as an item to be promoted to the
                participants.
              </div>
              <div>
                <div>
                  <q-toolbar>
                    <div class="text-subtitle2 text-weight-medium">Academic value</div>
                    <q-space />
                    <q-btn v-if="!academicValueVideo" flat round icon="upload" class="text-grey" @click="uploadMaterial('AcademicValue')"></q-btn>
                  </q-toolbar>
                </div>
                <div class="row">
                  <div class="text-center" style="width: 300px" @click="showMaterialDetail(academicValueVideo)">
                    <div v-if="academicValueVideo?.mime.includes('image')">
                      <q-img :src="hashToUrl(academicValueVideo?.hash)" fit="fill"></q-img>
                    </div>
                    <div v-else-if="academicValueVideo?.mime.includes('video')">
                      <video class="full-width vertical-middle" id="record-audio" muted preload="metadata" :src="hashToUrl(academicValueVideo?.hash)"></video>
                    </div>
                    <div v-else-if="academicValueVideo?.mime.includes('audio')">
                      <q-icon class="bg-teal" color="white" name="mic" size="5rem" />
                    </div>
                    <div v-else-if="academicValueVideo?.mime.includes('pdf')">
                      <q-icon color="teal" name="picture_as_pdf" size="5em" />
                    </div>
                  </div>
                  <div v-if="academicValueVideo && editable && !isViewAction" class="q-mx-md column reverse">
                    <q-icon color="red cursor-pointer" size="1.5rem" name="delete" @click="deleteMaterial(academicValueVideo)"></q-icon>
                  </div>
                </div>
              </div>

              <div v-if="servicePackage.contentOrientatedEnable">
                <div>
                  <q-toolbar>
                    <div class="text-subtitle2 text-weight-medium">Features</div>
                    <q-space />
                    <q-btn v-if="!featuresVideo" flat round icon="upload" class="text-grey" @click="uploadMaterial('Features')"></q-btn>
                  </q-toolbar>
                </div>
                <div class="row">
                  <div class="text-center" style="width: 300px" @click="showMaterialDetail(featuresVideo)">
                    <div v-if="featuresVideo?.mime.includes('image')">
                      <q-img :src="hashToUrl(featuresVideo?.hash)" fit="fill"></q-img>
                    </div>
                    <div v-else-if="featuresVideo?.mime.includes('video')">
                      <video class="full-width vertical-middle" id="record-audio" muted preload="metadata" :src="hashToUrl(featuresVideo?.hash)"></video>
                    </div>
                    <div v-else-if="featuresVideo?.mime.includes('audio')">
                      <q-icon class="bg-teal" color="white" name="mic" size="5rem" />
                    </div>
                    <div v-else-if="featuresVideo?.mime.includes('pdf')">
                      <q-icon color="teal" name="picture_as_pdf" size="5em" />
                    </div>
                  </div>
                  <div v-if="featuresVideo && editable && !isViewAction" class="q-mx-md column reverse">
                    <q-icon color="red cursor-pointer" size="1.5rem" name="delete" @click="deleteMaterial(featuresVideo)"></q-icon>
                  </div>
                </div>
              </div>

              <div>
                <div>
                  <q-toolbar>
                    <div class="text-subtitle2 text-weight-medium">Q&A</div>
                    <q-space />
                    <q-btn v-if="!QAVideo" flat round icon="upload" class="text-grey" @click="uploadMaterial('QA')"></q-btn>
                  </q-toolbar>
                </div>
                <div class="row">
                  <div class="text-center" style="width: 300px" @click="showMaterialDetail(QAVideo)">
                    <div v-if="QAVideo?.mime.includes('image')">
                      <q-img :src="hashToUrl(QAVideo?.hash)" fit="fill"></q-img>
                    </div>
                    <div v-else-if="QAVideo?.mime.includes('video')">
                      <video class="full-width vertical-middle" id="record-audio" muted preload="metadata" :src="hashToUrl(QAVideo?.hash)"></video>
                    </div>
                    <div v-else-if="QAVideo?.mime.includes('audio')">
                      <q-icon class="bg-teal" color="white" name="mic" size="5rem" />
                    </div>
                    <div v-else-if="QAVideo?.mime.includes('pdf')">
                      <q-icon color="teal" name="picture_as_pdf" size="5em" />
                    </div>
                  </div>
                  <div v-if="QAVideo && editable && !isViewAction" class="q-mx-md column reverse">
                    <q-icon color="red cursor-pointer" size="1.5rem" name="delete" @click="deleteMaterial(QAVideo)"></q-icon>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
        <q-toolbar v-if="servicePackage.type !== 'serviceTask'" id="bottom-publish">
          <q-space />
          <div v-if="isPublishAction && !servicePackage.filled" class="text-negative">
            There are sections needed to be completed before publishing the package
          </div>
          <template v-if="isViewAction">
            <Auth :action="'delete'">
              <q-btn class="bg-teal text-white q-mt-sm" rounded :disable="disabledDelete" outline no-caps label="Delete" @click="delPack()" />
            </Auth>
            <q-btn class="q-mx-sm text-teal" outline rounded no-caps label="Back" @click="cancelClick()"></q-btn>
          </template>
          <template v-else>
            <q-btn
              class="q-mx-sm"
              color="primary"
              icon="check"
              rounded
              no-caps
              :label="isPublishAction ? 'Publish' : servicePackage?._id ? 'Save' : 'Create'"
              :loading="loading"
              @click="createClick()"></q-btn>
            <q-btn class="q-mx-sm text-teal" outline rounded no-caps label="Cancel" @click="cancelClick()"></q-btn>
          </template>
        </q-toolbar>
        <br />
      </q-page>
      <q-dialog v-model="setBlock">
        <q-card>
          <q-card-section>
            <span class="text-h5"> Set time</span>
            <!-- <q-btn flat icon="arrow_left"></q-btn> -->
          </q-card-section>
          <q-card-section>
            <q-toolbar>
              <q-item-label class="text-h6">Length of each service</q-item-label>
              <q-space />
              <q-item-label v-if="tempDuration || servicePackage.duration" class="text-subtitle2 text-teal">{{
                `${tempDuration ? tempDuration : servicePackage.duration} mins`
              }}</q-item-label>
            </q-toolbar>
          </q-card-section>
          <q-card-section class="col row">
            <q-btn
              class="col-3 q-ma-sm"
              :class="tempDuration == 30 ? 'bg-teal-4 text-white' : ''"
              outline
              no-caps
              label="30 mins"
              @click="durationClick(30)"></q-btn>
            <q-btn
              class="col-3 q-ma-sm"
              :class="tempDuration == 60 ? 'bg-teal-4 text-white' : ''"
              outline
              no-caps
              label="60 mins"
              @click="durationClick(60)"></q-btn>
            <q-btn
              class="col-3 q-ma-sm"
              :class="tempDuration == 90 ? 'bg-teal-4 text-white' : ''"
              outline
              no-caps
              label="90 mins"
              @click="durationClick(90)"></q-btn>
            <q-btn
              class="col-3 q-ma-sm"
              :class="tempDuration == 120 ? 'bg-teal-4 text-white' : ''"
              outline
              no-caps
              label="120 mins"
              @click="durationClick(120)"></q-btn>
            <q-btn class="col-3 q-ma-sm text-teal" flat no-caps label="Customize" @click="customizeDuration()"></q-btn>
          </q-card-section>
          <q-card-section>
            <q-toolbar>
              <q-item-label class="text-h6">Length of break</q-item-label>
              <q-space />
              <q-item-label v-if="tempBreak || servicePackage.break" class="text-subtitle2 text-teal">{{
                `${tempBreak ? tempBreak : servicePackage.break} mins`
              }}</q-item-label>
            </q-toolbar>
          </q-card-section>
          <q-card-section class="col row">
            <q-btn class="col-3 q-ma-sm" :class="tempBreak == 5 ? 'bg-teal-4 text-white' : ''" outline no-caps label="5 mins" @click="breakClick(5)"></q-btn>
            <q-btn class="col-3 q-ma-sm" :class="tempBreak == 10 ? 'bg-teal-4 text-white' : ''" outline no-caps label="10 mins" @click="breakClick(10)"></q-btn>
            <q-btn class="col-3 q-ma-sm" :class="tempBreak == 15 ? 'bg-teal-4 text-white' : ''" outline no-caps label="15 mins" @click="breakClick(15)"></q-btn>
            <q-btn class="col-3 q-ma-sm" :class="tempBreak == 20 ? 'bg-teal-4 text-white' : ''" outline no-caps label="20 mins" @click="breakClick(20)"></q-btn>
            <q-btn class="col-3 q-ma-sm text-teal" flat no-caps label="Customize" @click="customizeBreak()"></q-btn>
          </q-card-section>
          <q-card-section>
            <q-btn class="full-width bg-teal text-white" rounded no-caps label="Confirm" @click="saveDurationBreak()"></q-btn>
          </q-card-section>
        </q-card>
      </q-dialog>
      <q-dialog v-model="viewBlock" class="full-height">
        <div class="q-pa-md full-width bg-white">
          <q-toolbar class="full-width">
            <q-toolbar-title>Service blocks</q-toolbar-title>
          </q-toolbar>
          <div class="full-width" style="height: 500px">
            <BlockList preview :block="block" />
          </div>
          <q-separator inset></q-separator>
          <div>
            <q-item class="col row q-mt-md">
              <q-item-section class="col" v-if="!readonly">
                <q-btn class="q-mx-md text-teal" rounded outline no-caps label="Edit" @click="editBlockClick()"></q-btn>
              </q-item-section>
              <q-item-section class="col">
                <q-btn class="q-mx-md bg-teal text-white" rounded outline no-caps label="Close" @click="viewBlock = false"></q-btn>
              </q-item-section>
            </q-item>
          </div>
        </div>
      </q-dialog>

      <q-dialog v-model="customizeDurationDialog">
        <q-card>
          <q-card-section>
            <q-item-label class="text-h6">Length of each service</q-item-label>
          </q-card-section>
          <q-card-section>
            <q-input v-model="tempDuration" outlined>
              <template v-slot:append> mins </template>
            </q-input>
          </q-card-section>
          <q-separator inset />
          <q-card-section>
            <q-btn class="full-width bg-teal text-white" rounded no-caps label="Confirm" @click="closeCustomize()"></q-btn>
          </q-card-section>
        </q-card>
      </q-dialog>

      <q-dialog v-model="customizeBreakDialog">
        <q-card>
          <q-card-section>
            <q-item-label class="text-h6">Length of break</q-item-label>
          </q-card-section>
          <q-card-section>
            <q-input v-model="tempBreak" outlined>
              <template v-slot:append> mins </template>
            </q-input>
          </q-card-section>
          <q-separator inset />
          <q-card-section>
            <q-btn class="full-width bg-teal text-white" rounded no-caps label="Confirm" @click="closeCustomize()"></q-btn>
          </q-card-section>
        </q-card>
      </q-dialog>

      <q-dialog v-model="viewMaterialDialog">
        <q-card style="width: 500px">
          <q-card-section>
            <q-toolbar>
              <q-toolbar-title>{{ viewMaterialDetail.filename ? viewMaterialDetail.filename : 'Material Detail' }}</q-toolbar-title>
              <q-space />
              <q-btn flat round icon="close" @click="() => (viewMaterialDialog = false)"></q-btn>
            </q-toolbar>
            <q-separator></q-separator>
            <br />
            <div class="text-center">
              <div v-if="viewMaterialDetail.mime.includes('image')">
                <q-img :src="hashToUrl(viewMaterialDetail.hash)"></q-img>
              </div>
              <div v-else-if="viewMaterialDetail.mime.includes('video')">
                <video class="full-width vertical-middle" id="record-audio" controls muted controlslist="nodownload">
                  <source :src="hashToUrl(viewMaterialDetail.hash)" />
                </video>
              </div>
              <div v-else-if="viewMaterialDetail.mime.includes('audio')">
                <video
                  class="full-width vertical-middle"
                  id="record-audio"
                  controls
                  muted
                  controlslist="nodownload"
                  style="height: 3rem"
                  :src="hashToUrl(viewMaterialDetail.hash)"></video>
              </div>
              <div v-else-if="viewMaterialDetail.mime.includes('pdf')">
                <q-btn flat class="text-teal" icon="picture_as_pdf" size="4rem" @click="openPDF(viewMaterialDetail)">
                  <q-tooltip>Open PDF</q-tooltip>
                </q-btn>
              </div>
            </div>
            <br />
            <q-separator></q-separator>
            <q-toolbar>
              <q-space />
              <q-btn class="text-white bg-red" label="Delete Material" no-caps rounded outline @click="deleteMaterial(viewMaterialDetail)"></q-btn>
            </q-toolbar>
          </q-card-section>
        </q-card>
      </q-dialog>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import {ref, onMounted, watch, computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {servicePackageStore} from 'stores/service-package'
import {pubStore} from 'stores/pub'
import {serviceTaskStore} from 'stores/serviceTask'

import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import {GradeGroupMap, EducatorGrades} from 'src/boot/const'
import BlockList from 'components/BlockList.vue'
// import {OverseasStudyList} from 'src/pages/teacher-verification/utils'
import PremiumDialog from 'src/pages/sys/package/PremiumContentDialog/PremiumDialog.vue'
import RecommendDialog from 'src/components/RecommendDialog.vue'
import PackageDialog from 'components/PackageDialog.vue'
import PackageCard from 'src/components/PackageCard.vue'
import AuthUnitCard from 'src/pages/account/teacher-auth/AuthUnitCard.vue'
import AcBgCheck from './AcBgCheck.vue'
import PremiumContent from './PremiumContent.vue'
import OnCampusPrice from './OnCampusPrice.vue'
import OnlineSubs from './OnlineSubs.vue'
import PackagePublish from 'components/PackagePublish.vue'
import DetailDialog from './editWidget/DetailDialog.vue'
import {frequencyMap} from './const'
import FreqDialog from './editWidget/FreqDialog.vue'
import TagDialog from './editWidget/TagDialog.vue'
import GradeDialog from './editWidget/GradeDialog.vue'
import PriceDialog from './editWidget/priceDialog.vue'
import {getHourRate} from './const'
import useTeacherVerificationConfig from 'src/composables/account/teacher-verification/useTeacherVerificationConfig'
import useSubject from 'src/composables/account/academic/useSubject'
import AlertDialog from 'src/components/AlertDialog.vue'
import Auth from '../Auth.vue'
import DeleteDialog from './editWidget/DeleteDialog.vue'
import ProductDetailEdit from './service-task/ProductDetailEdit.vue'
import NoData from 'components/pub/NoData.vue'
import Sections from 'components/ServiceTask/Sections.vue'
import SelectAssociatedTask from './service-task/SelectAssociatedTask.vue'
import TaskCard from 'src/components/ServiceTask/TaskCard.vue'

/*
  consts
*/
const servicePackage = servicePackageStore()
const serviceTask = serviceTaskStore()
const pub = pubStore()
const route = useRoute()
const router = useRouter()

const {curriculumList, userCurriculumList, sysCurriculumMap} = useAcademicSetting()
const setBlock = ref(false)
const viewBlock = ref(false)
const customizeDurationDialog = ref(false)
const customizeBreakDialog = ref(false)
const tempDuration = ref(0)
const tempBreak = ref(0)
const essayOrTeacherTrainingSubjectLists = ref([])
const viewMaterialDialog = ref()
const viewMaterialDetail = ref()
const errorPositions = ref({})
const isPublishAction = ref(route.query.action == 'publish')
const isViewAction = ref(route.query.action == 'view')
const premiumContentError = ref(true)
const subsPriceError = ref(true)
const loading = ref(false)
const packageBannerStatus = ref(false)
const lectureBannerStatus = ref(false)
const isServicePackApply = ref(false)
const unPublishStatus = ref(false)
const editProductDetail = ref(false)
const isServiceLoading = ref(false)

const {sysEssayData, sysTeacherTrainingSubjectData, sysOverseasStudyData} = useSubject()
const {allTeacherVerificationConfig, getAllTeacherVerificationConfig} = useTeacherVerificationConfig()
// getAllTeacherVerificationConfig(true) refresh config

const reasonStatus = ['contentOrientated.servicePack unPublish', 'consultantPack unPublish', 'carerPack unPublish', 'interviewPack unPublish']

/*
computeds
*/

const isPublishActive = computed(
  () => servicePackage.sections.length > 0 && servicePackage.points?.[0].trim() && servicePackage.name && servicePackage.description && servicePackage.cover
)

const overseasStudyList = computed(() => {
  return sysOverseasStudyData.value?.topic?.map((e) => ({label: e.name, value: e?._id}))
})

const disabledDelete = computed(() => {
  const pack = servicePackage
  return pack.status || (pack.count?.valid !== undefined && pack.count?.valid !== 0) || (pack.count?.ticket !== undefined && pack.count?.ticket !== 0)
})

const selectedSubjectsOrCountry = computed(() => {
  return selectedSubjects.value
})

const isMultipleContents = computed(() => {
  return ['essay', 'foundation', 'bachelor', 'diploma', 'master'].includes(servicePackage.mentoringType) // === 'university'
})

//https://docs.google.com/spreadsheets/d/1BsKOa4C6ren0zoOAedhoZcBKCnFjmeRsb7DyaFfBieQ/edit?pli=1&gid=456157691#gid=456157691
const isMultipleSubjectChoice = computed(() => {
  //personalStatement: 'Application and visa assistance'
  //https://github.com/zran-nz/bug/issues/5132#issuecomment-2378289953
  //https://github.com/zran-nz/bug/issues/5396#issue-2535721501
  const isSubstituteAcademic = servicePackage.serviceRoles === 'substitute' && servicePackage.mentoringType === 'academic'
  return (
    servicePackage.serviceRoles === 'consultant' || ['steam', 'essay', 'teacherTrainingSubject'].includes(servicePackage.mentoringType) || isSubstituteAcademic
  )
})

const isMultipleGradeChoice = computed(() => {
  return (servicePackage.serviceRoles === 'consultant' || servicePackage.isEducatorServiceType) && !servicePackage.contentOrientatedEnable
})

const addCarerPackageEnable = computed(() => {
  return (
    servicePackage.serviceRoles == 'mentoring' &&
    servicePackage.contentOrientatedEnable &&
    servicePackage.contentOrientated?.length &&
    !servicePackage.isEducatorServiceType
  )
})

const interviewOrAcademicCheckEnable = computed(() => {
  //https://github.com/zran-nz/bug/issues/4967#issuecomment-2242243613
  //https://github.com/zran-nz/bug/issues/4616#issue-2286748959 Academic check学术审核
  //https://github.com/zran-nz/bug/issues/5416#issue-2544170851
  return (
    servicePackage.serviceRoles == 'mentoring' &&
    servicePackage.contentOrientatedEnable &&
    servicePackage.contentOrientated?.length &&
    (servicePackage.mentoringType == 'academic' || servicePackage.mentoringTypeList.some((e) => e.value == servicePackage.mentoringType && e.educator))
  )
})

const block = computed(() => {
  return {duration: servicePackage.duration, break: servicePackage.break}
})

const editable = computed(() => {
  return !(servicePackage.serviceRoles == 'consultant' && servicePackage.lastPublished)
})

const readonly = computed(() => {
  return !!servicePackage.count?.sold
})

const addPremiumContentDisable = computed(() => {
  return (!selectedSubjects.value?.length && servicePackage.mentoringType !== 'overseasStudy') || !servicePackage.gradeGroup.grades?.length
})

const tagSettingDisable = computed(() => {
  return servicePackage.contentOrientatedEnable && !!servicePackage.contentOrientated?.length
})

const displpayPrice = computed(() => servicePackage.price / 100)

const subjectBlockTitle = computed(() => {
  let title = 'Subject'
  if (servicePackage.isEducatorServiceType) {
    title = 'Teaching area'
  }
  if (servicePackage.mentoringType === 'essay') {
    title = 'University researching field'
  }
  if (servicePackage.serviceRoles === 'consultant') {
    title = 'Service areas'
  }
  return title
})

const sellingPointAddShow = computed(() => {
  let AddPoints = true
  if (servicePackage.points.length === 1) {
    if (servicePackage.points[0] === '') {
      AddPoints = false
    }
  } else {
    const lastPoint = servicePackage.points[servicePackage.points.length - 1]
    if (lastPoint === '') {
      AddPoints = false
    }
  }
  return AddPoints
})

const userCurriculumCodes = computed(() => userCurriculumList.value.map((e) => e.code))

const currentSubjectList = computed(() => {
  let subjects = servicePackage.subjectList?.filter((subject) => subject.curriculum[0] === servicePackage.curriculum?.value)

  const subjectsDetail = servicePackage.serviceSubjects[servicePackage.mentoringType]
  if (servicePackage.mentoringType !== 'academic' && subjectsDetail?.topic) {
    if (servicePackage.topicWithChilds) {
      subjects = []
      subjectsDetail.topic.map((topic) => {
        subjects.push({
          label: topic.name,
          value: topic.child,
        })
      })
    } else {
      subjects = subjectsDetail.topic
    }
  }

  return subjects
})

const selectedSubjects = computed(() => {
  let selected
  if (servicePackage.topicWithChilds) {
    const valueList = essayOrTeacherTrainingSubjectLists.value.map((list) => list.value)?.flat()
    selected = valueList?.filter((e) => e.selected)
  } else {
    selected = currentSubjectList.value?.filter((e) => e.selected)
  }
  return selected
})

const gradeList = computed(() => {
  let gradeMapNeeded = Acan.clone(GradeGroupMap)
  if (servicePackage.mentoringType === 'academic') {
    delete gradeMapNeeded.tertiary
  } else if (servicePackage.isEducatorServiceType) {
    gradeMapNeeded = Acan.clone(EducatorGrades)
  } else if (servicePackage.mentoringType === 'essay') {
    gradeMapNeeded = {
      lowerHighSchool: gradeMapNeeded['lowerHighSchool'],
      upperHighSchool: gradeMapNeeded['upperHighSchool'],
      tertiary: gradeMapNeeded['tertiary'],
    }
  }
  return gradeMapNeeded
})

const academicValueVideo = computed(() => {
  let video = {}
  video = servicePackage.attachments.find((a) => a.videoType === 'AcademicValue')
  return video
})

const featuresVideo = computed(() => {
  let video = {}
  video = servicePackage.attachments.find((a) => a.videoType === 'Features')
  return video
})

const QAVideo = computed(() => {
  let video = {}
  video = servicePackage.attachments.find((a) => a.videoType === 'QA')
  return video
})

const isSubstitute = computed(() => {
  return servicePackage.serviceRoles === 'substitute'
})

const hourRateList = computed(() => {
  let list = []
  const commonConfig = {
    allTeacherVerificationConfig: allTeacherVerificationConfig.value,
    sysEssayData: sysEssayData.value,
    sysTeacherTrainingSubjectData: sysTeacherTrainingSubjectData.value,
    mentoringType: servicePackage.mentoringType,
    qualification: servicePackage.qualification,
  }

  const isContentOrientated = servicePackage.contentOrientatedEnable

  if (['mentoring', 'substitute', 'consultant'].includes(servicePackage.serviceRoles)) {
    if (servicePackage.mentoringType === 'academic') {
      if (isContentOrientated) {
        servicePackage.contentOrientated.forEach((item) => {
          const rate = getHourRate({
            ...commonConfig,
            curriculum: servicePackage.curriculum?.value,
            subject: item?.premium?.subject,
          })
          list.push(rate)
        })
      } else {
        // only substitute multiple subject
        selectedSubjects.value.forEach((subject) => {
          const rate = getHourRate({
            ...commonConfig,
            curriculum: servicePackage.curriculum?.value,
            subject: subject?._id,
          })
          list.push(rate)
        })
      }
    } else if (['overseasStudy', 'teacherTraining', 'steam', 'academicPlanning', 'personalStatement', 'interest'].includes(servicePackage.mentoringType)) {
      if (isContentOrientated) {
        servicePackage.contentOrientated.forEach((item) => {
          let topicRate = []
          item?.premium?.topic?.forEach((topic) => {
            const rate = getHourRate({
              ...commonConfig,
              topicId: topic?._id,
            })
            topicRate.push(rate)
          })
          list.push(Math.max(...topicRate))
        })
      } else {
        selectedSubjects.value.forEach((subject) => {
          const rate = getHourRate({
            ...commonConfig,
            topicId: subject?._id,
          })
          list.push(rate)
        })
      }
    } else if (['essay', 'teacherTrainingSubject'].includes(servicePackage.mentoringType)) {
      if (isContentOrientated) {
        servicePackage.contentOrientated.forEach((item) => {
          let topicRate = []
          item?.premium?.topic?.forEach((topic) => {
            const rate = getHourRate({
              ...commonConfig,
              topicName: showEssayOrTeacherTrainingSubjectLabel(topic),
              topicId: topic?._id,
            })
            topicRate.push(rate)
          })
          list.push(Math.max(...topicRate))
        })
      } else {
        essayOrTeacherTrainingSubjectLists.value?.forEach((item) => {
          item.value
            ?.filter((v) => v.selected)
            ?.forEach((subject) => {
              const rate = getHourRate({
                ...commonConfig,
                topicName: item?.label,
                topicId: subject?._id,
              })
              list.push(rate)
            })
        })
      }
    }
  }

  return list
})

// ---------------functions-------------------------

// --------------create functions -------------------
/*
  methods
*/

const setPackageBannerStatus = () => {
  const isShow = route.query.action == 'edit' || route.query.action == 'publish'
  packageBannerStatus.value = servicePackage?.contentOrientatedEnable && isShow
}

const setLectureBannerStatus = (list) => {
  const isShow = route.query.action == 'edit' || route.query.action == 'publish'
  lectureBannerStatus.value = servicePackage?.contentOrientatedEnable && isShow && list?.some((e) => e.status < 1)
}

const showEssayOrTeacherTrainingSubjectLabel = (subject) => {
  const result = essayOrTeacherTrainingSubjectLists.value?.find((list) => list.value.some((v) => v._id === subject._id))?.label
  return result
}

const addContentEnable = (subject) => {
  return (
    (isMultipleContents.value && !servicePackage.contentOrientated.some((e) => e.subject == subject._id)) ||
    (!isMultipleContents.value && servicePackage.contentOrientated.length === 0)
  )
}

// --------------create functions end-------------------

function onPremiumContentUpdate(invalid) {
  premiumContentError.value = invalid
  checkDataFinished(false)
}

function substituteUpdate(invalid) {
  subsPriceError.value = invalid
  checkDataFinished(false)
}

const isUploading = ref(false)

function subjectClick(subject) {
  if (!isMultipleSubjectChoice.value) {
    if (servicePackage.topicWithChilds) {
      essayOrTeacherTrainingSubjectLists.value?.map((s) => {
        s.value.map((v) => {
          if (v.selected) v.selected = !v.selected
        })
      })
    } else {
      currentSubjectList.value.map((s) => {
        if (s.selected) s.selected = !s.selected
      })
    }
  }

  subject.selected = !subject.selected

  checkDataFinished(false)
}

function gradeClick(label, key) {
  if (isMultipleGradeChoice.value) {
    if (servicePackage.gradeGroup.grades.some((e) => e === (servicePackage.isEducatorServiceType ? label : key))) {
      servicePackage.gradeGroup.grades = servicePackage.gradeGroup.grades.filter((e) => e !== (servicePackage.isEducatorServiceType ? label : key))
    } else {
      servicePackage.gradeGroup.grades.push(servicePackage.isEducatorServiceType ? label : key)
    }
  } else {
    servicePackage.gradeGroup.label = label.label
    servicePackage.gradeGroup.grades = [key]
  }

  checkDataFinished(false)
}

function editBlockClick() {
  viewBlock.value = false
  setBlock.value = true
}

function onPremiumContentClick(item) {
  const path = `/detail/content/limit/${item.premium.unit._id}`
  const query = {isSysView: 1, authId: item.premium._id}
  window.open(
    router.resolve({
      path,
      query,
    }).href,
    '_blank'
  )
}

function onUploadClick(subject) {
  const existingPremiumContent = servicePackage.contentOrientated.map((e) => e.premium?._id)
  const _subject = isMultipleContents.value ? subject._id : null
  $q.dialog({
    component: PremiumDialog,
    componentProps: {
      label: servicePackage.mentoringTypeList.find((e) => e.value == servicePackage.mentoringType)?.label,
      serviceType: servicePackage.mentoringType,
      curriculum: servicePackage.curriculum.value,
      countryCode: servicePackage.countryCode,
      subject: _subject ? [_subject] : selectedSubjects.value.map((e) => e._id),
      gradeGroup: servicePackage.gradeGroup.grades,
      exceptions: existingPremiumContent,
    },
  }).onOk(async (obj) => {
    obj.subject = _subject
    servicePackage.contentOrientated.push(obj)
    delete errorPositions.value.content
  })
}

function onTimesClick(item) {
  $q.dialog({
    title: item.premium?.name,
    message: 'No. of sessions required to completed this course',
    prompt: {
      model: item.times,
      isValid: (val) => val > 0,
      type: 'number', // optional
    },
    cancel: true,
    persistent: true,
  }).onOk((data) => {
    item.times = data
  })
}

function onRemoveClick(type) {
  $q.dialog({
    title: 'Are you sure to remove it?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    if (type == 'carer') {
      servicePackage.carerPack = null
    } else {
      servicePackage.interviewPack = null
    }
    checkDataFinished(false)
  })
}

function onDeleteClick(item, all) {
  $q.dialog({
    title: 'Are you sure to remove it?',
    message: all ? 'Please notice that the bundled 1v1 mentor service package will also be deleted if the premium content is removed' : '',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    if (all) {
      servicePackage.contentOrientated = servicePackage.contentOrientated.filter((e) => e.premium._id !== item.premium._id)
      if (servicePackage.contentOrientated?.length == 0) {
        servicePackage.carerPack = null
        servicePackage.bundledCarer = false
        servicePackage.interviewPack = null
        servicePackage.bundledInterview = false
        checkDataFinished(false)
      }
    } else {
      delete item.servicePack
    }
  })
}

function onBundledCarerUpdate() {
  checkDataFinished(false)
}

function onBundledInterviewUpdate() {
  checkDataFinished(false)
}

function onAddPackageClick(type) {
  $q.dialog({
    component: RecommendDialog,
    componentProps: {
      consultantType: type,
      educators: servicePackage.isEducatorServiceType,
      curriculum: servicePackage.curriculum?.value,
      serviceType: servicePackage.mentoringType,
      subject: selectedSubjects.value.map((e) => e._id),
      grades: servicePackage.gradeGroup.grades,
      qualification: servicePackage.qualification,
      contentOrientatedEnable: true,
    },
  }).onOk((obj) => {
    if (obj?.package) {
      if (type == 'carer') {
        servicePackage.carerPack = obj.package
      } else {
        servicePackage.interviewPack = obj.package
      }
    }
    checkDataFinished(false)
  })
}

async function uploadCoverImage() {
  if (!editable.value) return
  isUploading.value = true
  const rs = await Fn.fileUpLoadUiX('image/*')
  if (!rs) {
    return
  }
  if (rs.message) {
    pageLoading.value = false
    return $q.notify({type: 'negative', message: rs.message})
  }
  servicePackage.coverName = rs.title?.[pub.user._id]
  servicePackage.cover = rs._id
  checkDataFinished(false)
}

function deleteCover() {
  servicePackage.cover = ''
  servicePackage.coverName = ''
  checkDataFinished(false)
}

function addSellingPoint() {
  servicePackage.points.push('')
  checkDataFinished(false)
}

function deleteSellingPointClick(index) {
  servicePackage.points.splice(index, 1)
  checkDataFinished(false)
}

function handleAddAssociotedTask() {
  $q.dialog({
    component: SelectAssociatedTask,
    componentProps: {},
  })
    .onOk(async () => {
      $q.notify({
        type: 'positive',
        message: 'Associated task successfully',
      })
      isServiceLoading.value = true
      servicePackage.addedAssociatedTask = await servicePackage.getOnePackage(servicePackage.associatedTask._id)
      isServiceLoading.value = false
    })
    .onCancel(() => {
      servicePackage.associatedTask = {}
    })
}

async function handlePublishTask() {
  $q.loading.show()
  servicePackage.status = !servicePackage.status
  await servicePackage.publishPackage(route.query.id, servicePackage.status)
  $q.notify({type: 'positive', message: `${servicePackage.status ? 'Published' : 'Unpublished'} successfully`})
  $q.loading.hide()
}

async function handleDeleteTask() {
  loading.value = true
  try {
    await servicePackage.deletePackage(route.query.id)
  } catch (e) {
    console.log(e)
  }
  loading.value = false
  goBack()
}

async function isSaveActive() {
  const rs = await servicePackage.packageSubmit(gradeList.value)
  editProductDetail.value = false
  return (
    servicePackage.cover &&
    servicePackage.name?.trim() &&
    servicePackage.description?.trim() &&
    Array.isArray(servicePackage.points) &&
    servicePackage.points.some((p) => p.trim() !== '')
  )
}

let productName = ''
let productDesc = ''
let productPoints = []

function openEditProductDetail() {
  editProductDetail.value = true
  productName = servicePackage.name
  productDesc = servicePackage.description
  productPoints = [...servicePackage.points]
}

function onAddClick(item) {
  $q.dialog({
    component: RecommendDialog,
    componentProps: {
      curriculum: item.curriculum,
      orientated: true,
      educators: servicePackage.isEducatorServiceType,
      subject: item.premium?.curriculum == 'pd' ? item.premium.topic.map((e) => e._id) : item.premium.subject,
      serviceType: servicePackage.mentoringType,
      grades: servicePackage.gradeGroup.grades,
      qualification: servicePackage.qualification,
      contentOrientatedEnable: true,
    },
  }).onOk((obj) => {
    item.servicePack = obj.package
  })
}

function openBlockClick() {
  if (servicePackage.duration && servicePackage.break) {
    viewBlock.value = true
  } else {
    setBlock.value = true
  }
}

function durationClick(duration) {
  tempDuration.value = duration
}

function breakClick(breakTime) {
  tempBreak.value = breakTime
}

function customizeDuration() {
  customizeDurationDialog.value = true
}

function customizeBreak() {
  customizeBreakDialog.value = true
}

function closeCustomize() {
  customizeDurationDialog.value = false
  customizeBreakDialog.value = false
}

function saveDurationBreak() {
  servicePackage.duration = parseInt(tempDuration.value)
  servicePackage.break = parseInt(tempBreak.value)
  setBlock.value = false
  checkDataFinished(false)
}

async function uploadMaterial(type) {
  //const rs = await Fn.fileUpLoadUiX('image/*,.pdf,.mp4,.webm,audio/*')
  const rs = await Fn.fileUpLoadUiX('.mp4,.webm')
  if (!rs) {
    return
  }
  if (rs.message) {
    return $q.notify({type: 'negative', message: rs.message})
  }
  if (!servicePackage.attachments) servicePackage.attachments = []
  servicePackage.attachments.push({
    filename: rs.title?.[pub.user._id],
    mime: rs.mime,
    hash: rs._id,
    videoType: type,
  })
  checkDataFinished(false)
}

function openPDF(data) {
  window.open(Fn.hashToUrl(data.hash), '_blank')
}

function deleteMaterial(data) {
  let deleteIndex
  deleteIndex = servicePackage.attachments.findIndex((a) => a.videoType === data.videoType)
  servicePackage.attachments.splice(deleteIndex, 1)
  viewMaterialDialog.value = false
}

function showMaterialDetail(data) {
  viewMaterialDialog.value = true
  viewMaterialDetail.value = data
}

function saveSubjectsData() {
  const subjectList = []
  selectedSubjects.value?.map((subject) => {
    subjectList.push({
      _id: subject?._id,
      label: subject?.name,
    })
  })

  if (servicePackage.mentoringType == 'academic') {
    servicePackage.subject = subjectList?.map((item) => item?._id)
    servicePackage.topic = []
  } else {
    servicePackage.subject = []
    if (servicePackage.mentoringType !== 'overseasStudy') {
      servicePackage.topic = subjectList
    }
  }
}

function savePriceData() {
  servicePackage.price = displpayPrice.value * 100
}

function saveKeywords() {
  servicePackage.keywords = selectedSubjects.value?.map((e) => e.name)
}

const isEqualHourRateArray = (arr1, arr2) => {
  let changeIndex = -1
  if (arr1.length !== arr2.length) return false
  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) {
      changeIndex = i
      break
    }
  }
  return {isDiff: changeIndex > -1, diffIndex: changeIndex}
}
const goBack = () => {
  if (editProductDetail.value) {
    editProductDetail.value = false
    servicePackage.name = productName
    servicePackage.description = productDesc
    servicePackage.points = productPoints
  } else {
    router.replace(route.query.back || `/sys/package?tab=${route.query.tab}`)
  }
}

const checkHourRate = async () => {
  let isChange = false
  let title = 'The hourly rate has been changed.'
  const preHourRate = servicePackage.hourRate
  const preHourRateList = Acan.clone(servicePackage.hourRateList)

  if (isSubstitute.value && servicePackage.isOnCampus) {
    await servicePackage.getCampusCityList()
    const {isDiff, diffIndex} = isEqualHourRateArray(preHourRateList, servicePackage.hourRateList)
    if (isDiff) {
      const changeName = servicePackage?.onCampusPrice?.[diffIndex]?.city
      isChange = true
      title = `The hourly rate of ${changeName} has been changed.`
    }
  } else {
    await getAllTeacherVerificationConfig(true)
    if (servicePackage.contentOrientatedEnable && isMultipleContents.value) {
      const {isDiff, diffIndex} = isEqualHourRateArray(preHourRateList, servicePackage.hourRateList)
      if (isDiff) {
        const changeName = servicePackage?.contentOrientated?.[diffIndex]?.premium?.unit?.name
        isChange = true
        title = `The hourly rate of ${changeName} has been changed.`
      }
    } else {
      if (preHourRate !== servicePackage.hourRate) {
        isChange = true
      }
    }
  }

  if (isChange) {
    $q.dialog({
      component: AlertDialog,
      componentProps: {
        title,
      },
    })
  }
  return isChange
}

async function createClick() {
  loading.value = true
  saveSubjectsData()
  savePriceData()
  saveKeywords()

  checkDataFinished(true)

  if (isPublishAction.value) {
    const isHourRateChanged = await checkHourRate()
    if (isHourRateChanged) {
      loading.value = false
      return
    }
  }

  if (!(isPublishAction.value && !servicePackage.filled)) {
    const rs = await servicePackage.packageSubmit(gradeList.value)
    if (rs) {
      if (isPublishAction.value) {
        if (servicePackage.isConsultantInterview) {
          const query = {
            status: true,
            'consultant.type': servicePackage.consultant.type,
            mentoringType: servicePackage.mentoringType,
            qualification: servicePackage.qualification,
            curriculum: servicePackage.curriculum.value,
          }
          if (servicePackage.countryCodeRequired) {
            query.countryCode = {$in: servicePackage.countryCode}
          }

          const res = await App.service('service-pack').find({query})

          if (res?.data?.length) {
            loading.value = false
            $q.dialog({
              title: 'Alert',
              message: 'You can not publish this service package due to the existence of same-type product under the published status. ',
              ok: {label: 'I got it', noCaps: true},
            })
            return
          }
        }
        loading.value = false
        $q.dialog({
          component: PackagePublish,
          componentProps: {pack: rs},
        }).onOk(() => {
          router.back()
        })
      } else {
        router.back()
      }
    }
  }
}

function scrollIntoViewWithOffset(id, offset) {
  window.scrollTo({
    behavior: 'smooth',
    top: document.getElementById(id)?.getBoundingClientRect().top - document.body.getBoundingClientRect().top - offset,
  })
}

function checkDataFinished(scroll) {
  errorPositions.value = {}
  if (!servicePackage.cover) {
    errorPositions.value.cover = true
  } else {
    delete errorPositions.value.cover
  }

  if (!servicePackage.name) {
    errorPositions.value.name = true
  } else {
    delete errorPositions.value.name
  }

  if (servicePackage.points.length === 1 && !servicePackage.points[0]) {
    errorPositions.value.points = true
  } else {
    delete errorPositions.value.points
  }

  if (!servicePackage.type) {
    errorPositions.value.type = true
  }

  if (!servicePackage.mentoringType) {
    errorPositions.value.mentoringType = true
  }

  if (!servicePackage.curriculum.value) {
    errorPositions.value.curriculum = true
  }

  if (servicePackage.countryCodeRequired) {
    if (!servicePackage.countryCode?.length) {
      errorPositions.value.countryCode = true
    }
  }

  if (servicePackage.mentoringType == 'academic') {
    if (!servicePackage.subject.length || !selectedSubjects.value?.length) {
      errorPositions.value.subject = true
    } else {
      delete errorPositions.value.subject
    }
  } else if (servicePackage.mentoringType !== 'overseasStudy') {
    if (!servicePackage.topic.length || !selectedSubjects.value?.length) {
      errorPositions.value.subject = true
    } else {
      delete errorPositions.value.subject
    }
  }

  if (!servicePackage.gradeGroup?.grades?.length) {
    errorPositions.value.grades = true
  }

  if (['mentoring', 'consultant'].includes(servicePackage.serviceRoles) && (servicePackage.duration == 0 || servicePackage.break == 0)) {
    errorPositions.value.duration = true
  } else {
    delete errorPositions.value.duration
  }

  if (
    servicePackage.contentOrientatedEnable &&
    (!servicePackage.contentOrientated?.length || servicePackage.contentOrientated.some((e) => !e.premium?.status))
  ) {
    errorPositions.value.content = true
  } else {
    delete errorPositions.value.content
  }

  if (servicePackage.contentOrientatedEnable) {
    if (premiumContentError.value) {
      errorPositions.value.price = true
    }
  } else if (!servicePackage.price) {
    errorPositions.value.price = true
  } else {
    delete errorPositions.value.price
  }

  if (!servicePackage.freq) {
    errorPositions.value.freq = true
  }
  if (!servicePackage.attachments.find((video) => video.videoType === 'AcademicValue')) {
    errorPositions.value.attachments = true
  }
  if (servicePackage.contentOrientatedEnable && !servicePackage.attachments.find((video) => video.videoType === 'Features')) {
    errorPositions.value.attachments = true
  }
  if (!servicePackage.attachments.find((video) => video.videoType === 'QA')) {
    errorPositions.value.attachments = true
  }

  if (servicePackage.bundledCarer && !servicePackage.carerPack?._id) {
    errorPositions.value.carerPack = true
  } else {
    delete errorPositions.value.carerPack
  }

  if (servicePackage.bundledInterview && !servicePackage.interviewPack?._id) {
    errorPositions.value.interviewPack = true
  } else {
    delete errorPositions.value.interviewPack
  }

  if (servicePackage.serviceRoles === 'substitute') {
    console.log('subsPriceError', subsPriceError.value)
    if (subsPriceError.value) {
      errorPositions.value.price = true
    } else {
      console.log('delete errorPositions.value.price')
      delete errorPositions.value.price
    }
  }

  if (Object.keys(errorPositions.value)?.length) {
    servicePackage.filled = false
    loading.value = false
  } else {
    servicePackage.filled = true
  }

  if (scroll && isPublishAction.value) {
    if (!servicePackage.filled) {
      scrollIntoViewWithOffset(`error-${Object.keys(errorPositions.value)[0]}`, 100)
    } else {
      scrollIntoViewWithOffset('bottom-publish', 100)
    }
  }
}

function cancelClick() {
  router.back()
}

async function selectAllServiceAreasAndGrades() {
  if (servicePackage.serviceRoles == 'consultant' && servicePackage.consultant?.type !== 'interview') {
    await sleep(200)
    servicePackage.gradeGroup = {label: '', grades: []}
    if (Array.isArray(gradeList.value)) {
      gradeList.value.forEach((e) => gradeClick(e, e))
    } else {
      for (const key in gradeList.value) {
        if (gradeList.value[key]) {
          gradeClick(gradeList.value[key], key)
        }
      }
    }

    let subjectList = currentSubjectList.value

    for (let i = 0; i < subjectList.length; i++) {
      subjectClick(subjectList[i], i)
    }
  }
}

const getPremiumApply = async () => {
  const res = await App.service('service-pack-apply').find({query: {servicePack: route.query?.id}})
  console.log('servicePack', res)
  if (res?.total > 0) {
    console.log('isServicePackApply....')
    isServicePackApply.value = true
  }
}

async function getBundledDetail() {
  const premiumIds = servicePackage.contentOrientated.map((e) => e.premium)
  const packageIds = servicePackage.contentOrientated.map((e) => e.servicePack)

  if (servicePackage.interviewPack?._id) {
    packageIds.push(servicePackage.interviewPack._id)
  }

  if (servicePackage.carerPack?._id) {
    packageIds.push(servicePackage.carerPack._id)
  }

  if (premiumIds?.length) {
    const serviceAuths = await App.service('service-auth').get('unit', {query: {_id: {$in: premiumIds}}})
    servicePackage.contentOrientated.map((e) => {
      e.premium = serviceAuths.data?.find((f) => f._id == e.premium)
    })
    setLectureBannerStatus(serviceAuths?.data ?? [])
  }

  if (packageIds?.length) {
    const servicePacks = await App.service('service-pack').find({query: {_id: {$in: packageIds}}})
    servicePackage.contentOrientated.map((e) => {
      e.servicePack = servicePacks.data?.find((f) => f.status && f._id == e.servicePack)
      if (!e.servicePack?.status) {
        e.servicePack = null
        setPackageBannerStatus()
      }
    })

    if (servicePackage.interviewPack?._id) {
      servicePackage.interviewPack = {...servicePackage.interviewPack, ...servicePacks.data?.find((f) => f.status && f._id == servicePackage.interviewPack._id)}
      if (!servicePackage.interviewPack.status) {
        servicePackage.interviewPack = {}
        servicePackage.bundledInterview = false
        setPackageBannerStatus()
      }
    }

    if (servicePackage.carerPack?._id) {
      servicePackage.carerPack = {...servicePackage.carerPack, ...servicePacks.data?.find((f) => f.status && f._id == servicePackage.carerPack._id)}
      if (!servicePackage.carerPack.status) {
        servicePackage.carerPack = {}
        servicePackage.bundledCarer = false
        setPackageBannerStatus()
      }
    }
  }
}

const unPublishReason = () => {
  console.log('servicePackage.reason', servicePackage.reason)
  if (servicePackage.contentOrientatedEnable && servicePackage.reason?.length > 0) {
    console.log('servicePackage.contentOrientated')
    unPublishStatus.value = true
  }
}

async function main() {
  $q.loading.show()
  if (route.query?.id) {
    const packageData = await servicePackage.getOnePackage(route.query?.id)
    if (packageData) {
      const packCurriculum = sysCurriculumMap.value[packageData.curriculum]
      let gradeGroup = {
        label: '',
        grades: packageData.gradeGroup,
      }
      servicePackage.loadData(packageData, packCurriculum, gradeGroup)
      if (servicePackage.contentOrientatedEnable) {
        await getBundledDetail()
        await getPremiumApply()
      }
    }
  }

  unPublishReason()

  const topicIds = servicePackage.topic?.map((item) => item?._id)

  console.log('topicIds', topicIds)

  if (topicIds?.length > 0 && servicePackage?.contentOrientatedEnable && servicePackage.contentOrientated.length > 0) {
    servicePackage.contentOrientated = servicePackage.contentOrientated?.filter((e) => topicIds?.includes(e.subject))
  }

  if (servicePackage.mentoringType == 'academic') {
    currentSubjectList.value.map((s) => {
      if (servicePackage.subject?.includes(s._id)) {
        s.selected = true
      } else {
        delete s.selected
      }
    })
  } else if (servicePackage.topicWithChilds) {
    console.log('currentSubjectList', currentSubjectList.value)

    currentSubjectList.value.map((s) => {
      s.value.map((v) => {
        if (topicIds?.includes(v?._id)) {
          v.selected = true
          console.log('s', s)
          // essayOrTeacherTrainingSubjectLists.value = s
          if (!essayOrTeacherTrainingSubjectLists.value.some((list) => list.label === s.label)) {
            essayOrTeacherTrainingSubjectLists.value.push(s)
          }
        } else {
          delete s.selected
        }
      })
    })
  } else {
    currentSubjectList.value?.map((s) => {
      if (topicIds?.includes(s._id)) {
        s.selected = true
      } else {
        delete s.selected
      }
    })
  }

  if (servicePackage.gradeGroup.label && !servicePackage.gradeGroup.grades.length) {
    const gradeKey = Object.keys(GradeGroupMap).find((key) => GradeGroupMap[key].label === servicePackage.gradeGroup.label)
    servicePackage.gradeGroup.grades = GradeGroupMap[gradeKey].grades[servicePackage.curriculum]
    if (!servicePackage.gradeGroup?.length) {
      servicePackage.gradeGroup.grades = GradeGroupMap[gradeKey].grades.classcipe
    }
  }

  if (servicePackage.duration && servicePackage.break) {
    tempDuration.value = servicePackage.duration
    tempBreak.value = servicePackage.break
  }

  if (!route.query?.id && servicePackage.serviceRoles == 'consultant') {
    selectAllServiceAreasAndGrades()
  }

  if (servicePackage.contentOrientatedEnable) {
    curriculumList.value.push({label: 'Others', value: 'others'})
  }
  $q.loading.hide()
}

const editDetail = () => {
  $q.dialog({
    component: DetailDialog,
    componentProps: {
      editable: editable.value,
    },
  }).onOk(() => {
    checkDataFinished(false)
  })
}

const editGrade = () => {
  console.log('gradeList.value', gradeList.value)
  $q.dialog({
    component: GradeDialog,
    componentProps: {
      errorPositions: errorPositions.value,
      isPublishAction: isPublishAction.value,
      editable: editable.value,
      gradeList: gradeList.value,
      isMultipleGradeChoice: isMultipleGradeChoice.value,
    },
  }).onOk((res) => {
    console.log(res)
  })
}

const editFreq = () => {
  $q.dialog({
    component: FreqDialog,
    componentProps: {},
  }).onOk((res) => {
    console.log(res)
  })
}

const editTagSetting = () => {
  $q.dialog({
    component: TagDialog,
    componentProps: {
      tagSettingDisable: tagSettingDisable.value,
      subjectBlockTitle: subjectBlockTitle.value,
      isMultipleSubjectChoice: isMultipleSubjectChoice.value,
      propsEssayOrTeacherTrainingSubjectLists: essayOrTeacherTrainingSubjectLists.value,
      curriculumList: curriculumList.value,
      userCurriculumCodes: userCurriculumCodes.value,
      isMultipleContents: isMultipleContents.value,
    },
  }).onOk((data) => {
    console.log('data', data)
    if (servicePackage.topicWithChilds) {
      data.essayOrTeacherTrainingSubjectLists?.map((s) => {
        s.value?.map((v) => {
          v.selected = data.subjectSelected.includes(v._id)
        })
      })

      essayOrTeacherTrainingSubjectLists.value = data.essayOrTeacherTrainingSubjectLists
    } else {
      if (servicePackage.curriculum?.value !== data.curriculum?.value) {
        servicePackage.gradeGroup.grades = []
      }
      servicePackage.curriculum = data.curriculum
      currentSubjectList.value.map((s) => {
        s.selected = data.subjectSelected.includes(s._id)
      })
    }

    if (data?.isContentChanged) {
      servicePackage.carerPack = null
      if (isMultipleContents.value) {
        const subjectIds = selectedSubjectsOrCountry.value?.map((s) => s._id)
        servicePackage.contentOrientated = servicePackage.contentOrientated.filter((s) => {
          return subjectIds.includes(s.subject)
        })
      } else {
        servicePackage.contentOrientated = []
      }
    }

    checkDataFinished(false)
  })
}

const onPriceSettingClick = () => {
  $q.dialog({
    component: PriceDialog,
    componentProps: {},
  }).onOk((res) => {
    console.log(res)
  })
}

const delPack = () => {
  $q.dialog({
    component: DeleteDialog,
    componentProps: {
      pack: servicePackage,
    },
  }).onOk(async () => {
    $q.loading.show()
    const rs = await servicePackage.deletePackage(servicePackage._id)
    $q.loading.hide()
    $q.notify({type: 'positive', message: 'Delete package successfully'})
    if (rs) {
      router.back()
    }
  })
}
watch(
  () => servicePackage.contentOrientated,
  (val) => {
    if (val.length <= 1) {
      servicePackage.splitSale = false
    }
  },
  {deep: true}
)

watch(
  () => servicePackage.salesTarget,
  (val) => {
    if (!val?.includes('school')) {
      servicePackage.splitSale = false
    }
  },
  {deep: true}
)

watch(hourRateList, (newRate) => {
  servicePackage.hourRate = Math.max(...newRate) || 0
  if (servicePackage.contentOrientatedEnable && servicePackage.mentoringType === 'teacherTrainingSubject') {
    servicePackage.hourRateList = newRate.sort((a, b) => b - a)
  } else {
    servicePackage.hourRateList = newRate
  }
})

onMounted(async () => {
  $q.loading.show()
  servicePackage.serviceRoles = route.query.tab
  await servicePackage.init()
  if ((servicePackage.serviceRoles && servicePackage.mentoringType) || route.query?.id) {
    await main()
    await sleep(500)
    checkDataFinished(true)
  } else {
    $q.loading.hide()
    $q.dialog({
      component: PackageDialog,
      componentProps: {},
    }).onOk(async () => {
      await main()
    })
  }
})
</script>
<style lang="scss" scoped>
.top_tips {
  position: absolute;
  top: -20px;
  left: 0;
  height: 20px;
}
</style>
