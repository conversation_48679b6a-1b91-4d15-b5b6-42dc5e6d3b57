<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" @before-show="beforeDialogShow" :persistent="persistent" maximized>
    <q-card style="width: 1000px; height: 700px; max-width: 90vw; max-height: 100vh" class="bg-teal-1">
      <q-card-section>
        <q-item class="row justify-between">
          <q-item-section>
            <GeneralFilters v-model="filters" :pagination="pagination" :options="filterOptions" @update:modelValue="onFilterUpdate" />
          </q-item-section>
          <q-item-section style="width: 200px" class="col-6">
            <q-btn class="text-teal" outline rounded label="Go to template setting" no-caps @click="onTeamplateClick"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
      <q-card-section style="overflow: auto; height: calc(100% - 180px)">
        <TaskList :isSelect="true" :listData="listData" />
      </q-card-section>
      <q-card-section>
        <q-item class="row justify-end">
          <q-item-section style="width: 200px" class="col-6" :class="{invisible: persistent}">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps @click="onDialogCancel"></q-btn>
          </q-item-section>
          <q-item-section style="width: 200px" class="col-6">
            <q-btn class="text-white bg-teal" :disabled="confirmDisabled" rounded label="Confirm" no-caps @click="onOKClick"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import {OverseasStudyList} from 'src/pages/teacher-verification/utils'
import {getActionAuthMap} from 'src/pages/sys/Manager/const.js'
import {countryOptions} from 'src/pages/account/OnCampusVerify/consts'
import GeneralFilters from '../../../../components/GeneralFilters.vue'
import TaskList from 'src/components/ServiceTask/TaskList.vue'

const servicePackage = servicePackageStore()
const router = useRouter()
const route = useRoute()
const pub = pubStore()
const loading = ref(false)
const filters = ref()
const listData = ref([])
const list = ref({})
const virtualListIndex = ref(1)

const props = defineProps({persistent: Boolean, mentoringType: String, serviceRoles: String})

// Auth
// authList:['add', 'add_mentor', 'add_other', 'view_mentor', 'view_other', 'edit', 'publish', 'view', 'delete', 'sales_statistic', 'sales_history']
const actionAuthMap = getActionAuthMap(route.path, pub.user?.roles, pub.user?.managerRoles)

defineEmits([
  // REQUIRED; need to specify some events that your
  // component will emit through useDialogPluginComponent()
  ...useDialogPluginComponent.emits,
])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
    computeds
  */
const pagination = computed(() => {
  return {start: virtualListIndex.value + 1, end: listData.value.length, total: list.value.total}
})

const filterOptions = computed(() => {
  const options = []
  return options
})

const confirmDisabled = computed(() => {
  return !servicePackage.associatedTask._id
})

/*
    methods
  */

const find = async ($skip = 0) => {
  const query = {del: false, $skip}
  query.type = 'serviceTask'
  query.$sort = {createdAt: -1}

  if (filters.value?.search) {
    query.name = {$regex: filters.value.search, $options: 'i'}
  }

  if (servicePackage.serviceRoles == 'consultant') {
    query.mentoringType = 'thesis-defense'
    query.qualification = 'seniorConsultant'
  } else if (servicePackage.serviceRoles == 'mentoring') {
    query.mentoringType = servicePackage.mentoringType
    if (servicePackage.qualification === 'studentTutor') {
      query.qualification = 'consultant'
    } else {
      query.qualification = 'seniorConsultant'
    }
  }

  query.contentOrientatedEnable = false
  query.status = true

  console.log('iooof', query)

  const rs = (list.value = await App.service('service-pack').find({query}))
  loading.value = false
  listData.value.push(...rs.data)

  //   if ($skip == 0) {
  //     router.replace({query: {...route.query, tab: route.query.tab}})
  //   }
  return rs.data.length
}

const resetFn = async () => {
  listData.value.length = 0
  list.value = {}
  loading.value = true
  await find()
}

function onFilterUpdate() {
  resetFn()
}

const onTeamplateClick = () => {
  const url = router.resolve({
    path: '/sys/package',
    query: {
      tab: 'service-task',
    },
  }).href

  window.open(url, '_blank')
}

const onOKClick = async () => {
  await servicePackage.setAssociatedTask()
  onDialogOK()
}
</script>

<style scoped></style>
