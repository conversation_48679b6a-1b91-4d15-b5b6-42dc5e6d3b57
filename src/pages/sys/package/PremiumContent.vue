<template>
  <div class="row items-center">
    <div class="text-subtitle1 text-weight-medium col" :class="{'text-negative': error}">*Price setting & Target sales group</div>
    <q-btn flat icon="o_edit" v-if="!isViewAction && !isServicePackApply" @click="onPriceSettingClick"></q-btn>
  </div>
  <div class="row items-center q-mt-md">
    <div class="text-bold q-mr-md">Sold to</div>
    <div class="col">
      <q-option-group disable color="primary" :options="targetSoldOptions" type="checkbox" inline v-model="servicePackage.salesTarget" />
    </div>
  </div>
  <div class="row items-center" v-if="servicePackage.discountConfig.enable">
    <div class="q-mr-md">
      <div>
        <span class="text-primary">Discount active</span>
        <span class="q-ml-sm">{{ totalDiscount }} %</span>
      </div>
    </div>
    <div class="col" v-if="servicePackage.discountConfig.end">
      <span class="text-primary">Discount ends</span>
      <span class="q-ml-sm">{{ servicePackage.discountConfig.end }}</span>
    </div>
  </div>
  <div class="q-mt-md" v-if="servicePackage.splitSale">
    <span class="text-primary text-bold">Allow premium content selection at the time of purchase</span>
    <q-icon name="check" size="xs" class="q-ml-sm" />
  </div>
  <q-card class="rounded-borders-md q-mt-md bg-white" v-for="(item, index) in servicePackage?.contentOrientated || []" :key="index">
    <q-card-section>
      <div class="bg-teal-2 q-pa-md rounded-borders-md row items-center">
        <div class="q-mr-md" style="width: 96px; height: 50px">
          <q-img
            :ratio="16 / 7"
            class="fit rounded-borders-md"
            fit="cover"
            spinner-color="white"
            :src="hashToUrl(item?.premium?.cover || item?.premium?.image || item?.premium?.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
        </div>
        <div class="text-bold ellipsis-2-lines" style="width: 250px">{{ item?.premium?.unit?.name }}</div>
        <div class="col text-right q-pr-md">
          <div class="text-bold">
            Qualification:
            {{ servicePackage.qualificationList.find((e) => e.value == servicePackage.qualification)?.label }}
          </div>
          <div class="text-grey-6 text-bold q-my-md">
            Cost per session
            <span class="text-primary cursor-pointer">
              ${{ ((servicePackage?.hourRateList?.[index] * servicePackage.duration) / 60).toFixed(2) }}
              <q-tooltip
                >{{ servicePackage?.hourRateList?.[index] }} * {{ servicePackage.duration }} / 60 =
                {{ ((servicePackage?.hourRateList?.[index] * servicePackage.duration) / 60).toFixed(2) }}
              </q-tooltip>
            </span>
          </div>
        </div>
      </div>
      <div class="row items-center" v-if="servicePackage?.salesTarget?.includes('school')">
        <div class="text-bold sold_left">Organization</div>
        <div class="bg-teal-1 row col q-pa-md rounded-borders-md row items-center q-mt-md">
          <q-item class="col-12">
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">Rate/service</span>
              <q-item-label class="">{{ item.schoolPrice }}</q-item-label>
            </q-item-section>
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">No of service</span>
              <q-item-label class="">{{ item.times }}</q-item-label>
            </q-item-section>
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">No of Discount</span>
              <q-item-label class=""> {{ totalDiscount }} % </q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-item-label class="">
                <div class="text-grey-6 text-body2 text-strike">${{ (schoolPrice(item)?.price / 100).toFixed(2) }}</div>
                <div class="text-bold text-primary">${{ (schoolPrice(item)?.discountPrice / 100).toFixed(2) }}</div>
              </q-item-label>
            </q-item-section>
          </q-item>
        </div>
      </div>
      <div class="row items-center" v-if="servicePackage?.salesTarget?.includes('personal') && servicePackage?.salesTarget?.length === 1">
        <div class="text-bold sold_left">Individual</div>
        <div class="col row bg-teal-1 q-pa-md rounded-borders-md row items-center q-mt-md">
          <q-item class="col-12">
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">Rate/service</span>
              <q-item-label class="">{{ item.price?.toFixed(2) }}</q-item-label>
            </q-item-section>
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">No of service</span>
              <q-item-label class="">{{ item.times }}</q-item-label>
            </q-item-section>
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">No of Discount</span>
              <q-item-label class=""> {{ totalDiscount }} % </q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-item-label class="">
                <div class="text-grey-6 text-body2 text-strike">${{ (personalPrice(item)?.price / 100).toFixed(2) }}</div>
                <div class="text-bold text-primary">${{ (personalPrice(item)?.discountPrice / 100).toFixed(2) }}</div>
              </q-item-label>
            </q-item-section>
          </q-item>
        </div>
      </div>
      <div class="row items-center" v-if="servicePackage?.salesTarget?.includes('personal') && servicePackage?.salesTarget?.includes('school')">
        <div class="text-bold sold_left">Individual</div>
        <div class="col bg-teal-1 row q-pa-md rounded-borders-md row items-center q-mt-md">
          <q-item class="col-12">
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">Rate/service</span>
              <q-item-label class="">{{ item.price?.toFixed(2) }}</q-item-label>
            </q-item-section>
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">
                Percent
                <q-tooltip> Percentage of organization rate </q-tooltip>
              </span>
              <q-item-label class="">{{ item.percentPrice }}%</q-item-label>
            </q-item-section>
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">No of service</span>
              <q-item-label class="">{{ item.times }}</q-item-label>
            </q-item-section>
            <q-item-section class="relative-position">
              <span class="bg-white q-px-sm top_tips">No of Discount</span>
              <q-item-label class=""> {{ totalDiscount }} % </q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-item-label class="">
                <div class="text-grey-6 text-body2 text-strike">${{ (personalPrice(item)?.price / 100).toFixed(2) }}</div>
                <div class="text-bold text-primary">${{ (personalPrice(item)?.discountPrice / 100).toFixed(2) }}</div>
              </q-item-label>
            </q-item-section>
          </q-item>
        </div>
      </div>
      <div class="bg-teal-2 q-pa-md rounded-borders-md row items-center q-mt-md" v-if="item?.servicePack">
        <q-item class="col-12">
          <q-item-section class="relative-position">
            <div class="q-mr-md" style="width: 96px; height: 50px">
              <q-img
                :ratio="16 / 7"
                class="fit rounded-borders-md"
                fit="cover"
                spinner-color="white"
                :src="hashToUrl(item?.servicePack?.cover || item?.servicePack?.image || item?.servicePack?.unitSnapshot?.cover) || '/v2/img/no-img.png'" />
            </div>
          </q-item-section>
          <q-item-section class="relative-position">
            <q-item-label class="">
              <div class="text-bold">
                <div class="">
                  {{ item?.servicePack?.name || item?.servicePack?.desc }}
                </div>
                <div class="text-pink-10">
                  Qualification:
                  {{ servicePackage.qualificationList.find((e) => e.value == item?.servicePack?.qualification)?.label }}
                </div>
              </div>
            </q-item-label>
          </q-item-section>
          <q-item-section class="relative-position">
            <div class="bg-white top_tips q-px-sm">Rate/service</div>
            <div class="">${{ (servicePrice(item)?.rate / 100)?.toFixed(2) }}</div>
          </q-item-section>
          <q-item-section class="relative-position">
            <span class="bg-white q-px-sm top_tips">No of service</span>
            <q-item-label class="">
              {{ servicePrice(item)?.totalCount }}
              <div class="text-yellow-9" style="font-size: 12px" v-if="servicePrice(item).gifts">
                <q-icon name="redeem" size="xs"></q-icon>
                {{ ` Additional ${servicePrice(item).gifts} free sessions as gifts` }}
              </div>
            </q-item-label>
          </q-item-section>
          <q-item-section class="relative-position">
            <span class="bg-white q-px-sm top_tips">No of Discount</span>
            <q-item-label class=""> {{ totalDiscount }} % </q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-item-label class="">
              <div class="text-grey-6 text-body2 text-strike">${{ (servicePrice(item)?.oriPrice / 100).toFixed(2) }}</div>
              <div class="text-bold text-primary">${{ (servicePrice(item)?.price / 100).toFixed(2) }}</div>
            </q-item-label>
          </q-item-section>
        </q-item>
      </div>
      <div class="q-mt-md row items-center justify-end">
        <q-card class="q-ml-md" v-if="servicePackage?.salesTarget?.includes('school')">
          <q-card-section class="row items-center">
            <div class="text-right text-bold">Sub total for organisation</div>
            <div class="q-ml-md">
              <div class="text-grey-6 text-body2 text-strike">${{ (subTotal(item, 'school')?.price / 100).toFixed(2) }}</div>
              <div class="text-bold text-primary">${{ (subTotal(item, 'school')?.discountPrice / 100).toFixed(2) }}</div>
            </div>
          </q-card-section>
        </q-card>
        <q-card class="q-ml-md">
          <q-card-section class="row items-center" v-if="servicePackage?.salesTarget?.includes('personal')">
            <div class="text-right text-bold">Sub total for individual</div>
            <div class="q-ml-md">
              <div class="text-grey-6 text-body2 text-strike">${{ (subTotal(item, 'personal')?.price / 100).toFixed(2) }}</div>
              <div class="text-bold text-primary">${{ (subTotal(item, 'personal')?.discountPrice / 100).toFixed(2) }}</div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </q-card-section>
  </q-card>
</template>
<script setup>
/*
  imports
*/
import {computed, onMounted, ref, watch} from 'vue'
import {servicePackageStore} from 'stores/service-package'
import {calPremiumMentor} from 'src/pages/order/consts'
import {date} from 'quasar'
import PremiumPriceDialog from './editWidget/PremiumPriceDialog.vue'
import {calServiceDiscount} from './const'

const props = defineProps({
  error: Boolean,
  isViewAction: Boolean,
  isServicePackApply: Boolean,
})

const readonly = ref(false)
const emit = defineEmits(['update'])
const servicePackage = servicePackageStore()

const targetSoldOptions = [
  {
    label: 'Organization',
    value: 'school',
  },
  {
    label: 'Individual user',
    value: 'personal',
  },
]

const totalDiscount = computed(() => {
  return calServiceDiscount(servicePackage?.discountConfig)
})

const soldOptions = computed(() => {
  let result = [...targetSoldOptions]
  if (servicePackage.salesTarget.length === 1) {
    result = [
      {
        label: 'Organization',
        value: 'school',
        disable: servicePackage.salesTarget?.includes('school'),
      },
      {
        label: 'Individual user',
        value: 'personal',
        disable: servicePackage.salesTarget?.includes('personal'),
      },
    ]
  }

  return result
})

const servicePrice = (item) => {
  return calPremiumMentor({
    count: +item?.times,
    servicePack: item?.servicePack,
    discount: totalDiscount.value,
  })
}

const subTotal = (item, type) => {
  let inPrice = type === 'school' ? item?.schoolPrice : item?.price
  let price = 0
  let discountPrice = 0
  price += calPrice(inPrice, item.times)?.price
  discountPrice += calPrice(inPrice, item.times)?.discountPrice

  if (item?.servicePack) {
    let serPrice = calPremiumMentor({
      count: +item?.times,
      servicePack: item?.servicePack,
      discount: totalDiscount.value,
    })
    price += serPrice?.oriPrice
    discountPrice += serPrice?.price
  }

  return {
    price,
    discountPrice,
  }
}

const schoolPrice = (item) => {
  return calPrice(item?.schoolPrice, item.times)
}

const personalPrice = (item) => {
  return calPrice(item?.price, item.times)
}

const personalPercentPrice = (item) => {
  const price = (item?.schoolPrice * item.percentPrice) / 100
  return calPrice(price, item.times)
}

const calPrice = (itemPrice, times) => {
  let price = itemPrice * 100 * times
  let discountPrice =
    servicePackage?.discountConfig?.enable && servicePackage?.discountConfig?.discount
      ? itemPrice * 100 * ((100 - servicePackage?.discountConfig?.discount) / 100) * times
      : price

  return {
    price: isNaN(price) ? 0 : price,
    discountPrice: isNaN(discountPrice) ? 0 : discountPrice,
  }
}

const percentUp = (o) => {
  if (+o.percentPrice <= 100) {
    o.percentPrice = undefined
  }
  servicePackage.contentOrientated = [...servicePackage.contentOrientated].map((item) => {
    let price = item?.price
    if (item?._id === o?._id) {
      price = (o?.schoolPrice * o?.percentPrice) / 100
    }
    return {
      ...item,
      price,
    }
  })
  onValidateUpdate()
}

const salesChange = () => {
  if (servicePackage.salesTarget?.length === 2) {
    servicePackage.contentOrientated?.map((item) => {
      item.price = undefined
      item.percentPrice = undefined
    })
  }
  onValidateUpdate()
}

const onValidateUpdate = () => {
  let isError = false
  if (servicePackage.salesTarget?.length === 0 || servicePackage?.contentOrientated?.length === 0) {
    isError = true
  } else {
    servicePackage?.contentOrientated?.forEach((item) => {
      let {price, schoolPrice, percentPrice} = item
      if (servicePackage?.salesTarget?.includes('personal') && !(price >= 0)) {
        isError = true
      }
      if (servicePackage?.salesTarget?.includes('school') && !(schoolPrice >= 0)) {
        isError = true
      }
      if (servicePackage.salesTarget?.length === 2) {
        if (percentPrice < 100) {
          isError = true
        }
      }
    })
  }

  console.log('premiumErrorValidate', isError)
  emit('update', isError)
}

const onPriceSettingClick = () => {
  $q.dialog({
    component: PremiumPriceDialog,
    props: {},
  })
}

watch(
  () => servicePackage.contentOrientated,
  (val) => {
    onValidateUpdate()
  },
  {deep: true}
)

onMounted(async () => {
  servicePackage.salesTarget = ['school', 'personal']
  onValidateUpdate()
})
</script>

<style lang="scss" scoped>
.sold_left {
  width: 90px;
}
.top_tips {
  position: absolute;
  top: -20px;
  left: 0;
}
</style>
