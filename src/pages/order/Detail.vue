<template>
  <q-layout class="bg-grey-3" view="hHh LpR fFf">
    <q-header class="bg-teal-1 text-black" elevated>
      <q-toolbar class="">
        <q-btn dense flat icon="navigate_before" round @click="goBack"></q-btn>
        <q-toolbar-title class="text-h6">
          {{ detail?.label }}
          order
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container class="pc-sm q-pb-xl">
      <q-page>
        <div v-if="loading" class="text-center q-pa-xl text-grey">
          <q-spinner-ball class="full-width" color="primary" size="2em" />
        </div>
        <div v-if="detail">
          <div class="q-px-md q-py-md">
            <q-btn v-if="resultText && resultPath" class="q-mb-sm" color="primary" :label="resultText" :to="resultPath" no-caps rounded size="" />
            <div class="row q-col-gutter-md">
              <div class="col-sm-8 col-xs-12">
                <div v-if="detail.status !== 100" class="rounded-borders-md q-mt-sm q-border-1 q-pa-md bg-white shadow-1">
                  <div class="q-pa-md bg-grey-2">
                    {{ detail.tips }}
                  </div>
                  <q-btn
                    v-if="detail.refund?.length"
                    class="q-mt-lg full-width"
                    color="primary"
                    label="Refund status"
                    no-caps
                    rounded
                    size=""
                    @click="toRefund" />
                </div>
                <div v-for="item in detail.links" :key="item._id" class="q-my-sm">
                  <div class="relative-position" v-if="item.sessionBoardType === 'isPublicOrder' && item.goods" @click="toShopDetail(item)">
                    <SessionBoard :isPointMode="detail?.isPoint" :session="item.goods" isPublicOrder no-remove :onLinkItemClick="onLinkItemClick" />
                    <q-img
                      v-if="item.removed && detail.status === 200"
                      src="~assets/img/order_failed.png"
                      spinner-color="white"
                      style="width: 59px; height: 49px"
                      class="absolute-top-right" />
                  </div>
                  <div class="relative-position" v-if="item.sessionBoardType === 'isCourseOrder' && item.goods" @click="toShopDetail(item)">
                    <SessionBoard :isPointMode="detail?.isPoint" :session="item.goods" isCourseOrder no-remove :onLinkItemClick="onLinkItemClick" />
                    <q-img
                      v-if="item.removed && detail.status === 200"
                      src="~assets/img/order_failed.png"
                      spinner-color="white"
                      style="width: 59px; height: 49px"
                      class="absolute-top-right" />
                  </div>
                  <div class="relative-position" v-if="item.sessionBoardType === 'isPackageOrder' && item.goods" @click="toShopDetail(item)">
                    <TaskCard
                      v-if="item.goods.type === 'serviceTask'"
                      :isView="true"
                      :isBuy="true"
                      :noElevation="true"
                      :task="item.goods"
                      :clickAllowed="true"
                      :orderPrice="item.price" />
                    <PackageCard
                      v-else
                      :isPointMode="detail?.isPoint"
                      :serviceNo="item.count + item.giftCount"
                      isPackageOrder
                      :pack="item.goods"
                      :is-promotion="item?.goods?.promotion"
                      :onItemClick="onLinkItemClick" />
                    <q-img
                      v-if="item.removed && detail.status === 200"
                      src="~assets/img/order_failed.png"
                      spinner-color="white"
                      style="width: 59px; height: 49px"
                      class="absolute-top-right" />
                  </div>
                  <div v-if="item.isBundled" class="text-primary bg-white q-my-md q-pa-md rounded-borders-md q-border-1 shadow-1">
                    Bundled Mentoring service package
                  </div>
                  <LecturePackage :item="item" v-if="item?.style === 'service_premium'" @click="toShopDetail(item)" />
                  <SubPackage :item="item?.goods" :count="item?.count" readOnly v-if="item?.style === 'service_substitute'" @click="toShopDetail(item)" />
                  <ImportBoard v-if="item?.style === 'premium_cloud'" :item="item?.goods" :price="item?.price" />
                  <PromptBoard v-if="item?.style === 'prompt'" :item="item?.goods" :price="item?.price" @click="toShopDetail(item)" />
                </div>
                <div class="rounded-borders-md q-mt-md q-border-1 q-pa-md bg-white shadow-1">
                  <div class="text-h6">Confirmation code</div>
                  <div class="text-grey-6">{{ detail.no }}</div>
                </div>
                <div class="rounded-borders-md q-mt-md q-border-1 q-pa-md bg-white shadow-1">
                  <div class="text-h6 q-mb-md">Cancellation policy</div>
                  <CancellationPolicy :type="policyType" />
                  <q-btn class="q-pa-xs" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/cancellation" />
                </div>
              </div>
              <div class="col-sm-4 col-xs-12 q-mt-sm">
                <div class="rounded-borders-md q-border-1 q-pa-md bg-white shadow-1">
                  <div v-if="detail.status === 100 || detail.status === 400">
                    <div class="text-h6">Order summary</div>
                    <div class="row justify-between q-py-sm text-subtitle1">
                      <div class="items-center flex">
                        <span class="">
                          Subtotal
                          <span v-if="detail?.links?.length > 1"> ({{ detail?.links?.length }} items) </span>
                        </span>
                      </div>
                      <div class="">${{ (detail?.price / 100).toFixed(2) }}</div>
                    </div>
                    <div class="row justify-between q-py-sm text-subtitle1 text-weight-bold">
                      <div class="items-center flex">
                        <span class=""> Total </span>
                      </div>
                      <div class="">${{ (detail?.price / 100).toFixed(2) }}</div>
                    </div>
                  </div>
                  <div v-else>
                    <div class="text-h6">Payment detail</div>
                    <div class="row justify-between q-py-sm" v-if="detail?.paidAt">
                      <div class="items-center flex text-subtitle1">
                        <span class="">Purchased on </span>
                      </div>
                      <div class="q-ml-xs text-primary">
                        {{ date.formatDate(detail?.paidAt, 'DD/MM/YYYY') }}
                      </div>
                    </div>
                    <div class="row justify-between q-py-sm text-subtitle1 text-weight-bold">
                      <div class="items-center flex">
                        <span class=""> Total </span>
                      </div>
                      <div v-if="detail?.isPoint" class="q-ml-xs">
                        <IconPoint :size="1" class="q-ml-xs" :num="totalPrice" />
                      </div>
                      <div v-else class="">${{ (totalPrice / 100).toFixed(2) }}</div>
                    </div>
                    <div v-if="detail.status !== 600" class="text-primary text-center cursor-pointer" @click="viewReceipts">Receipts</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row justify-between btn-box q-pa-sm bg-white full-width fixed-bottom" v-if="!isReadonly">
            <div v-if="detail.status === 100" class="pc-sm text-right">
              <span v-if="$q.screen.gt.sm" class="text-negative text-bold">
                Payment needs to be made within
                <CountDown :deadTime="detail.expiration" @end="end" />
              </span>
              <span class="text-primary text-bold q-mr-md"> ${{ (detail?.price / 100).toFixed(2) }} </span>
              <q-btn class="q-mr-sm" color="primary" label="Confirm and pay" no-caps rounded size="" @click="toPay"></q-btn>
              <q-btn class="q-mr-sm" :loading="btnLoading" color="primary" label="Cancel this order" no-caps rounded size="" @click="cancelUnPaidOrder"></q-btn>
            </div>
            <div v-if="isOrderCancel" class="pc-sm text-right">
              <q-btn class="q-mr-sm" :loading="btnLoading" color="primary" label="Cancel this order" no-caps rounded size="" @click="cancel"></q-btn>
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {inject, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import SessionBoard from 'components/SessionBoard.vue'
import PackageCard from 'components/PackageCard.vue'
import LecturePackage from './components/LecturePackage.vue'
import CountDown from './CountDown.vue'
import CancellationPolicy from './CancellationPolicy.vue'
import IconPoint from 'src/pages/point/components/IconPoint.vue'
import {ORDER_STATUS, orderCalPrice, isTeacherRole} from './consts.js'
import {pubStore} from 'stores/pub'
import {PATH_PREFIX} from 'src/boot/const'
import {date} from 'quasar'
import SubPackage from './components/SubPackage.vue'
import ImportBoard from 'src/pages/order/components/ImportBoard.vue'
import PromptBoard from 'src/pages/order/components/PromptBoard.vue'

const pub = pubStore()
const contentsType = inject('ContentsType')
const route = useRoute()
const router = useRouter()
const id = ref(route.params.id)

const detail = ref(null)

const loading = ref(false)
const isOrderCancel = ref(false)

const policyType = ref('')
const totalPrice = ref(0)

const isBundleSession = ref(false)

const isTeacher = ref(isTeacherRole(pub?.user?.roles))

const btnLoading = ref(false)

const resultText = ref(null)
const resultPath = ref(null)
const unitList = ref([])

const isReadonly = ref(!!route?.query?.isReadonly)

const schoolId = ref(pub?.user?.school || undefined)

const servicePackUser = ref([])

console.log('schoolId', schoolId.value)

import {pointStore} from 'stores/point'
import OrderDetailDialog from './OrderDetailDialog.vue'
import CancelTicketDialog from './components/CancelTicketDialog.vue'
import TaskCard from 'src/components/ServiceTask/TaskCard.vue'

const pStore = pointStore()

const goBack = () => {
  if (route.query?.backPre) {
    router.go(-1)
    return
  }
  if (route.query?.back) {
    router.push(route.query?.back)
    return
  }

  let query = ''

  if (route.query?.from === 'result') {
    query = '?type=order&title=Order'
  }

  query = '?type=order&title=Order'

  if (isTeacher.value) {
    router.push(`/home/<USER>
  } else {
    router.push(`/study/orders${query}`)
  }
}

const toPay = () => {
  router.push(`/order/payconfirm/${id.value}`)
}

const cancelUnPaidOrder = () => {
  console.log('cancelUnPaidOrder')
  $q.dialog({
    component: OrderDetailDialog,
  }).onOk(async (res) => {
    console.log('res', res)
    btnLoading.value = true
    await App.service('order')
      .get('cancelBeforePay', {query: {id: id.value}})
      .then(() => {
        $q.notify({type: 'positive', message: 'cancel successfully'})
        goBack()
      })
      .catch(() => {
        $q.notify({type: 'negative', message: 'Cancelation failed'})
      })
    btnLoading.value = false
  })
}

const getPointSetting = async () => {
  await pStore.getClaimSetting()
  pStore.getPoint()
}

const cancelClickApi = async (tickets, cb) => {
  $q.loading.show()
  await App.service('order')
    .get('cancelTicket', {query: {tickets}})
    .then(() => {
      $q.notify({type: 'positive', message: 'Ticket refund successfully'})
      if (cb) {
        cb()
      }
    })
    .catch((err) => {
      $q.notify({type: 'negative', message: err?.message})
    })
    .finally(() => {
      $q.loading.hide()
    })
}

const cancelTicket = async () => {
  $q.loading.show()
  const res = await App.service('service-pack-ticket').find({
    query: {
      $limit: 100,
      $sort: {createdAt: -1},
      school: schoolId.value,
      servicePremium: detail.value?.servicePremium,
      order: id.value,
      refund: false,
    },
  })
  $q.loading.hide()
  if (res?.total === 1) {
    console.log('res', res)
    cancelClickApi([res?.data?.[0]?._id], () => {
      goBack()
    })
  } else if (res?.total > 1) {
    $q.dialog({
      component: CancelTicketDialog,
      componentProps: {
        data: res?.data || [],
      },
    }).onOk(async (res) => {
      cancelClickApi(res, () => {
        main()
      })
    })
  }
}

const cancel = () => {
  if (schoolId.value && detail.value?.persons > 1) {
    cancelTicket()
  } else {
    $q.dialog({
      component: OrderDetailDialog,
    }).onOk(async (res) => {
      console.log('res', res)
      btnLoading.value = true
      await App.service('order')
        .get('cancel', {query: {id: id.value, status: 500}})
        .then(() => {
          $q.notify({type: 'positive', message: 'cancel successfully'})
          goBack()
        })
        .catch(() => {
          $q.notify({type: 'negative', message: 'Cancelation failed'})
        })
      btnLoading.value = false
    })
  }
}

const toRefund = () => {
  router.push(`/order/refundDetail/${id.value}`)
}

const onLinkItemClick = () => {
  console.log('linking')
}

const toShopDetail = (item) => {
  if (detail.value?.type === 'service_premium') {
    let path = `/service/pack/${detail.value?.servicePremium}`
    if (detail.value?.status === 200) {
      const pack_user_id = servicePackUser.value?.find((v) => v?.snapshot?._id === item?.goods?._id)?._id
      path = `/service/pack-user/${pack_user_id}`
    }
    router.push({
      path,
      query: {back: route.fullPath},
    })
    return
  }
  let path = null
  if (item.style === 'unit') {
    // const myContentId = unitList.value?.find((unit) => unit?.source === item?.id)?._id
    path = `/detail/lib/${item?.id}`
  } else if (item.style === 'service') {
    path = `/service/pack/${item?.id}`

    if (detail.value?.status === 200) {
      const pack_user_id = servicePackUser.value?.find((v) => v?.snapshot?._id === item?.goods?._id)?._id
      path = `/service/pack-user/${pack_user_id}`
    }
  } else if (item.style === 'service_substitute') {
    //path = `/detail/booking/${item?.id}`
    path = `/service/pack/${item?.id}`
    if (detail.value?.status === 200) {
      const pack_user_id = servicePackUser.value?.find((v) => v?.snapshot?._id === item?.goods?._id)?._id
      path = `/service/pack-user/${pack_user_id}`
    }
  } else if (item.style === 'prompt') {
    path = `/detail/prompts/${item?.id}`
  } else {
    path = `/detail/session/${item?.id}`
  }
  router.push({
    path,
    query: {back: route.fullPath},
  })
}

const viewResult = async () => {
  const {type, sharedSchool} = detail.value
  const linkItem = detail.value?.links?.[0]
  const isMultiplePro = detail.value?.links?.length > 1
  const goods = linkItem.goods
  let path = null

  if (type === 'unit') {
    if (isMultiplePro) {
      path = `/home/<USER>
    } else {
      const myContentId = unitList.value?.find((unit) => unit?.source === goods?._id)?._id
      path = `/detail/content/me/${myContentId}?back=/home/<USER>
    }
  } else if (type === 'service_pack') {
    const pack_user_res = await App.service('service-pack-user').find({
      query: {
        uid: pub?.user?._id,
        'snapshot._id': linkItem?.id,
      },
    })
    if (pack_user_res?.total > 0) {
      const back = goods.mentoringType === 'teacherTraining' ? '/home/<USER>' : '/study/booking?tab=purchased'
      //path = `/detail/booking/my/${pack_user_res?.data?.[0]?._id}?back=${back}`
      path = `/service/pack-user/${pack_user_res?.data?.[0]?._id}?back=${back}`
    }
  } else if (type === 'service_premium') {
    let query = {
      uid: pub?.user?._id,
      'snapshot._id': detail.value?.servicePremium,
    }
    if (schoolId.value) {
      query.uid = schoolId.value
      query.$isSchool = true
    }
    const pack_user_res = await App.service('service-pack-user').find({query})
    console.log('pack_user_res', pack_user_res)
    if (pack_user_res?.total > 0) {
      const back = isTeacher.value ? '/home/<USER>' : '/study/booking?tab=purchased'
      const school = schoolId.value ? `&school=${schoolId.value}` : ''
      // path = `/detail/booking/my/${pack_user_res?.data?.[0]?._id}?back=${back}${school}`
      path = `/service/pack-user/${pack_user_res?.data?.[0]?._id}?back=${back}${school}`
    }

    if (sharedSchool) {
      path = goods.mentoringType === 'teacherTraining' ? '/home/<USER>' : '/study/booking?tab=purchased'
    }
  } else if (type === 'session_public' || type === 'session_service_pack') {
    const status = getItemStatus(goods)
    const back = isTeacher.value ? `/home/<USER>/study/purchased?tab=workshop&subtab=${status}&type=`
    path = `/detail/session/${goods?._id}?back=${back}`
  } else if (type === 'session_self_study') {
    path = isMultiplePro ? `/study/purchased?tab=self&subtab=enrolled` : `/detail/session/${goods?._id}?back=/study/purchased?tab=self%26subtab=enrolled`
  } else if (type === 'service_substitute') {
    console.log(' schoolId.value', schoolId.value)
    let query = {
      uid: schoolId.value,
      'snapshot._id': linkItem?.id,
      $isSchool: true,
    }
    console.log('query', query)
    const pack_user_res = await App.service('service-pack-user').find({query})
    console.log('pack_user_res', pack_user_res)
    if (pack_user_res?.total > 0) {
      const back = `/home/<USER>
      // path = `/detail/booking/my/${pack_user_res?.data?.[0]?._id}?back=${back}`
      path = `/service/pack-user/${pack_user_res?.data?.[0]?._id}?back=${back}`
    }
  }

  console.log('resultPath', path)
  resultPath.value = path
}

const resultTextType = {
  unit: 'View in my content',
  service_pack: 'View in booking',
  service_premium: 'View in booking',
  service_substitute: 'View in booking',
  session_public: 'View in workshop',
  session_service_pack: 'View in workshop',
  session_self_study: 'View in Self-study',
}
const viewResultTxt = () => {
  const {type} = detail.value
  if (route.query?.from !== 'result') {
    return
  }
  resultText.value = resultTextType[type] || ''
  viewResult()
}

const getItemStatus = (item) => {
  const now = new Date()
  let _status = ''
  if (item.status == 'close') {
    _status = 'ended'
  } else if (item.start && new Date(item.start) >= now) {
    _status = 'scheduled'
  } else if (item.start && new Date(item.start) < now) {
    _status = 'ongoing'
  }
  return _status
}

const main = async () => {
  loading.value = true
  const data = await App.service('order').get(id.value)
  console.log('data', data)

  if (data?.isPoint) {
    getPointSetting()
  }

  const bundleSession =
    data?.links?.filter((item) => {
      if (item.style === 'session' && item.goods?.servicePack) {
        isBundleSession.value = true
        return item
      }
    })?.[0] || null

  data.links.map((item) => {
    if (item.style === 'unit') {
      item.sessionBoardType = 'isCourseOrder'
      policyType.value = 'unit'
    } else if (item.style === 'session') {
      const goods = item.goods
      const sessionDetail = contentsType[goods.type]
      item.sessionBoardType = sessionDetail.public ? 'isPublicOrder' : 'isCourseOrder'
      policyType.value = sessionDetail.public ? 'workshops' : 'selfStudy'
      if (goods?.servicePack && data?.links?.length > 1) {
        item.isBundled = true
      }
    } else if (item.style === 'service') {
      item.sessionBoardType = 'isPackageOrder'
      policyType.value = 'service'
      if (bundleSession) {
        item.goods = {
          ...item.goods,
          servicePack: bundleSession?.goods?.servicePack,
          promotion: bundleSession?.goods?.promotion,
        }
        policyType.value = 'pubService'
      }
    } else if (item.style === 'service_substitute') {
      policyType.value = 'service'
    } else if (item.style === 'service_premium') {
      policyType.value = 'service'
    }
  })

  totalPrice.value = orderCalPrice(data)
  const tips = data.status === 200 && data.refund?.length ? 'We have processed a partial refund' : ORDER_STATUS[data.status].tips
  detail.value = {
    ...data,
    label: ORDER_STATUS[data.status].label,
    tips,
    cover: data.links[0].cover,
    name: data.links[0].name,
  }
  loading.value = false

  calCancel(data)
  if (data?.type === 'unit') {
    const unitRes = await App.service('unit').find({
      query: {
        orderId: id.value,
        del: false,
        tab: 'me',
      },
    })
    console.log('unitRes', unitRes.data)
    unitList.value = unitRes?.data || []
  }
  viewResultTxt()

  getServicePackUser(data)
}

/**
 * demand: 4094
 * @param data
 */
const calCancel = async (data) => {
  const hasPromotion = data?.links?.every((item) => item.promotion)
  if (hasPromotion && data?.links?.length === 1) {
    return
  }
  if (data.status === 200) {
    await App.service('order')
      .get('orderRefundCheck', {query: {id: id.value}})
      .then((res) => {
        if (res?.refundAllowed) {
          isOrderCancel.value = true
        }
      })
  }
}

const getServicePackUser = async (data) => {
  const serviceTypeList = ['service_premium', 'service_pack', 'service_substitute']
  if (data.status === 200 && serviceTypeList.includes(data.type)) {
    let query = {
      uid: pub?.user?._id,
      'snapshot._id': detail.value?.servicePremium,
      $limit: 100,
    }
    if (schoolId.value) {
      query.uid = schoolId.value
      query.$isSchool = true
    }
    const pack_user_res = await App.service('service-pack-user').find({query})
    console.log('pack_user_res', pack_user_res)
    servicePackUser.value = pack_user_res?.data || []
  }
}

onMounted(main)

const end = () => {
  main()
}

const viewReceipts = () => {
  if (detail.value?.refund?.length) {
    router.push(`/order/payHistory/${id.value}`)
  } else {
    window.open(`${PATH_PREFIX}/order/receipt/${id.value}`, '_blank')
  }
}
</script>
<style lang="sass" scope></style>
