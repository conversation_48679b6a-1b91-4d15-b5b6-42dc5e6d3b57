<template>
  <div class="q-px-md q-mb-md" v-if="detail">
    <div class="row q-col-gutter-md">
      <div class="col-sm-8 col-xs-12">
        <div>
          <q-card>
            <PackageCard :isPointMode="isPointMode" isPackageOrder :pack="detail" :is-promotion="detail.promotion" :onItemClick="toPack" />
            <div v-if="associatedTask" class="q-mb-md">
              <TaskCard
                :isView="true"
                :isBuy="true"
                :task="associatedTask"
                :associateTaskDiscount="isDiscount ? detail?.discountConfig?.discount : 0"
                noElevation
                :clickAllowed="true" />
            </div>
          </q-card>
        </div>
        <q-item v-if="associatedTask" class="q-my-md q-pa-sm">
          <q-item-section class="col">
            <q-radio v-model="servicePackageAssociatedTask" label="Package without associated task" :val="null" color="teal" class="text-weight-medium" />
            <q-radio
              v-model="servicePackageAssociatedTask"
              label="Package with associated task"
              :val="associatedTask._id"
              color="teal"
              class="text-weight-medium" />
          </q-item-section>
        </q-item>
        <q-card class="q-mt-md rounded-borders-md">
          <q-card-section>
            <div class="row">
              <div :class="$q.screen.gt.sm ? 'col' : 'col-12'">Choice of package size</div>
              <div class="text-info text-bold" v-if="detail?.discountConfig?.enable && detail?.discountConfig?.end && !isFreeService">
                Discount ends in
                <CountDown :deadTime="detail?.discountConfig?.end" @end="end" />
              </div>
            </div>
            <q-list>
              <q-item v-for="item in servicePackageOptions" :key="item._id" tag="label" v-ripple class="row">
                <q-item-section avatar>
                  <q-radio v-model="servicePackageChoice" @update:modelValue="onEndOptionUpdate" :val="item._id" color="teal" />
                </q-item-section>
                <q-item-section class="col" :class="$q.screen.gt.sm ? 'q-ml-md ' : ''">
                  <q-item-label>
                    <span class="text-h6">
                      <template v-if="item._id === 'customize_point'">
                        <q-input
                          :rules="[(val) => (val && val >= 0) || 'Value must be non-negative']"
                          style="width: 120px; display: inline-block"
                          v-model="serviceCustomizePoint"
                          @update:modelValue="onServicePackageCustomizeUpdate"
                          outlined
                          dense
                          type="number" />
                      </template>
                      <template v-else> {{ item.count + (item.gifts || 0) }} </template>
                      sessions
                    </span>
                    <span v-if="item.hot" class="text-red text-bold q-ml-sm" style="position: relative; top: -5px">hot</span>
                    <div v-if="item.gifts" class="flex items-center text-yellow-9 text-body2">
                      <q-icon name="redeem" class="q-pr-xs" size="xs"></q-icon>
                      {{ ` Additional ${item.gifts} free sessions as gifts` }}
                    </div>
                  </q-item-label>
                </q-item-section>
                <q-item-section side v-if="isPointMode">
                  <div>
                    <ShowPoint :price="item?.price" :type="claimCategoryMap[detail?.serviceRoles]" />
                  </div>
                </q-item-section>
                <q-item-section side v-else>
                  <q-item-label>
                    <span class="text-primary text-h6 text-weight-medium"> ${{ (item?.price / 100).toFixed(2) }} </span>
                    <div class="text-strike text-grey" v-if="isDiscount">${{ (item?.oriPrice / 100).toFixed(2) }}</div>
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-card>

        <q-card class="rounded-borders-md q-mt-md bg-white">
          <q-card-section>
            <template v-if="isPointMode">
              <div class="text-h6 q-mb-md">Points redemption policy</div>
              <q-btn class="q-pa-xs" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/points_redemption" />
            </template>
            <template v-else>
              <div class="text-h6 q-mb-md">Cancellation policy</div>
              <CancellationPolicy type="service" />
              <q-btn class="q-pa-xs" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/cancellation" />
            </template>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-sm-4 col-xs-12">
        <q-card class="rounded-borders-md">
          <q-card-section>
            <div class="text-h6">Order summary</div>
            <div class="row justify-between q-py-sm text-subtitle1">
              <div class="items-center flex">
                <span class=""> Subtotal ({{ servicePackageAssociatedTask ? '2 items' : '1 item' }}) </span>
              </div>
              <div v-if="isPointMode" class="row items-center">
                <IconPoint class="q-ml-xs" :num="totalPoint" />
              </div>
              <div v-else class="">${{ (totalPrice / 100).toFixed(2) }}</div>
            </div>
            <div class="row justify-between q-py-sm text-subtitle2">
              <div class="col ellipsis">
                <span class="text-capitalize">
                  <template> Mentoring service: </template>
                  {{ detail?.name }}
                  <q-tooltip>
                    {{ detail?.name }}
                  </q-tooltip>
                </span>
              </div>
              <div>${{ (mentorPrice / 100).toFixed(2) }}</div>
            </div>
            <div v-if="servicePackageAssociatedTask" class="row justify-between q-py-sm text-subtitle2">
              <div class="col ellipsis">
                <span class="text-capitalize">
                  <template> Mentoring service: </template>
                  {{ associatedTask?.name }}
                  <q-tooltip>
                    {{ associatedTask?.name }}
                  </q-tooltip>
                </span>
              </div>
              <div>${{ (associatedTaskPrice / 100).toFixed(2) }}</div>
            </div>
            <div class="row justify-between q-py-sm text-subtitle1 text-weight-bold">
              <div class="items-center flex">
                <span class=""> Total </span>
              </div>
              <div v-if="isPointMode" class="row items-center">
                <IconPoint class="q-ml-xs" :num="totalPoint" />
              </div>
              <div v-else class="">${{ (totalPrice / 100).toFixed(2) }}</div>
            </div>
            <div v-if="isPointMode" class="row justify-between q-py-sm text-subtitle1">
              <div class="items-center flex">
                <span class=""> My point balance </span>
              </div>
              <div class="row items-center">
                <IconPoint :num="pStore.myPoint - totalPoint" />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>
<script setup>
/*
  imports
*/
import {computed, inject, nextTick, onMounted, ref, watchEffect, watch} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'

const pub = pubStore()

import SessionBoard from 'components/SessionBoard.vue'
import PackageCard from 'components/PackageCard.vue'
import ShowPoint from 'src/pages/point/components/ShowPoint.vue'
import IconPoint from 'src/pages/point/components/IconPoint.vue'
import CancellationPolicy from 'src/pages/order/CancellationPolicy.vue'
import CountDown from 'src/pages/order/CountDown.vue'
import {CalPoint} from 'src/pages/point/consts.js'
import {isTeacherRole, claimCategoryMap} from 'src/pages/order/consts.js'
import TaskCard from '../../../components/ServiceTask/TaskCard.vue'

const route = useRoute()
const router = useRouter()
const ids = ref(route.params.ids)
const dialogShow = ref(false)
const list = ref([])
const totalPrice = ref(0)
const totalPoint = ref(0)

import {pointStore} from 'stores/point'

const pStore = pointStore()

const serviceCustomizePoint = ref(0)
const servicePackageChoice = ref(null)

const servicePackageOptions = ref([])

const detail = ref(null)
const city = ref(null)

const isTeacher = ref(isTeacherRole(pub?.user?.roles))

const isDiscount = ref(null)

const isPointMode = ref(!!route?.query?.isPointMode)
const isFreeService = ref(!!route?.query?.freeServiceClaimed)

const mentorPrice = ref(0)
const associatedTask = ref(null)
const servicePackageAssociatedTask = ref(null)
const associatedTaskPrice = ref(0)
const originalAssociatePrice = ref(0)

console.log('pub', pub.user)

const emit = defineEmits(['updateData'])

const isOnCampus = computed(() => {
  return detail.value?.isOnCampus
})

watch(servicePackageAssociatedTask, async (newVal) => {
  associatedTaskPrice.value = newVal ? originalAssociatePrice.value * ((100 - (detail.value?.discountConfig?.discount || 0)) / 100) : 0
  onEndOptionUpdate(servicePackageChoice.value)
})

const goBack = (hash) => {
  router.go(-1)
}

const onEndOptionUpdate = (id) => {
  const item = servicePackageOptions.value.find((o) => o._id === id)
  totalPrice.value = item?.price
  if (isPointMode.value) {
    totalPoint.value = CalPoint(item?.price, 'service', pStore.claimSetting)
  }
  mentorPrice.value = totalPrice.value
  totalPrice.value += Number(associatedTaskPrice.value)
}

const end = () => {
  initData()
}

const checkoutData = async () => {
  let promotion = undefined
  let freeServiceClaimed = undefined

  const isAdmin = pub?.user?.schoolUser?.role?.includes('admin')

  let school = (isAdmin && pub?.user?.school) || undefined
  let isSchool = isAdmin && !!pub?.user?.school
  let serviceItem = servicePackageOptions.value.find((o) => o._id === servicePackageChoice.value)
  if (isFreeService.value) {
    promotion = true
    freeServiceClaimed = true
  }
  console.log('serviceItem', serviceItem)
  // return
  console.log('ordering')
  return {
    linksQuery: {
      links: [
        {
          id: ids.value,
          style: 'service',
        },
        ...(servicePackageAssociatedTask.value ? [{id: servicePackageAssociatedTask.value, style: 'service'}] : []),
      ],
    },
    orderQuery: {
      link: [
        {
          id: ids.value,
          style: 'service',
          count: serviceItem?.count,
        },
        ...(servicePackageAssociatedTask.value ? [{id: servicePackageAssociatedTask.value, style: 'service', count: 1, mentorPackId: ids.value}] : []),
      ],
      promotion,
      freeServiceClaimed,
      isPoint: isPointMode.value || undefined,
      inviter: route?.query?.inviteCode,
      servicePackApply: route?.query?.servicePackApply,
      school,
      isSchool,
      inviteSource: route?.query?.inviteSource,
      inviteSourceId: route?.query?.inviteSourceId,
      schoolInviter: route?.query?.schoolInviter,
    },
  }
}

const toPack = (item) => {
  router.push({
    // path: `/detail/booking/limit/${item?._id}`,
    path: `/service/pack/${item?._id}`,
    query: {
      type: 'view',
    },
  })
}

const onServicePackageCustomizeUpdate = () => {
  const choiceCount = serviceCustomizePoint.value
  if (choiceCount >= 0) {
    const itemList = servicePackageOptions.value.filter((o) => o._id !== 'customize_point').sort((a, b) => a.count - b.count)
    const price = isDiscount.value ? (detail.value?.price * (100 - detail.value?.discountConfig?.discount)) / 100 : detail.value?.price || 0
    let choiceItem = {}
    if (choiceCount < itemList[0].count) {
      choiceItem = {
        count: +choiceCount,
        discount: 0,
      }
    } else {
      choiceItem = itemList.findLast((val) => val.count <= choiceCount) ?? itemList[itemList.length - 1]
    }

    const findIndex = servicePackageOptions.value.findIndex((o) => o._id === 'customize_point')

    servicePackageOptions.value[findIndex] = {
      ...[...servicePackageOptions.value][findIndex],
      count: +choiceCount,
      price: (choiceCount * (100 - choiceItem.discount) * price) / 100,
    }

    console.log('servicePackageOptions', servicePackageOptions.value)
    if (servicePackageChoice.value === 'customize_point') {
      onEndOptionUpdate('customize_point')
    }
  } else {
    serviceCustomizePoint.value = 0
  }
}

const getPointSetting = async () => {
  if (!isPointMode.value) {
    return
  }

  await pStore.getClaimSetting()
  pStore.getPoint()
}

const main = async () => {
  await getPointSetting()
  const data = await App.service('service-pack').get(ids.value)
  let serviceOptions = []
  if (isFreeService.value) {
    data.price = 0
  }
  console.log('data', data)

  const nowDate = new Date()
  // if no end date then always  valid
  const endDate = new Date(data?.discountConfig?.end || nowDate.getTime() + 200)
  const diff = endDate.getTime() - nowDate.getTime()

  const hasDiscount = data?.discountConfig?.enable && diff > 0
  isDiscount.value = hasDiscount
  console.log('hasDiscount', hasDiscount)

  if (data) {
    if (!isPointMode.value && data.associatedTask?._id) {
      associatedTask.value = await App.service('service-pack').get(data.associatedTask._id)
      servicePackageAssociatedTask.value = data.associatedTask._id
      originalAssociatePrice.value = associatedTask.value?.price
      associatedTaskPrice.value = hasDiscount
        ? originalAssociatePrice.value * ((100 - (data?.discountConfig?.discount || 0)) / 100)
        : originalAssociatePrice.value
    }
    // get max count
    const maxCountObject = data?.discount?.reduce(
      (maxObject, currentObject) => {
        return currentObject.count > maxObject.count ? currentObject : maxObject
      },
      {count: -Infinity}
    )

    maxCountObject.hot = true

    data?.discount?.forEach((item) => {
      const oriPrice = (item.count * data?.price * (100 - item.discount)) / 100
      serviceOptions.push({
        ...item,
        oriPrice,
        price: hasDiscount ? (oriPrice * (100 - data?.discountConfig?.discount)) / 100 : oriPrice,
        hasDiscount,
      })
    })

    const showPrice = serviceOptions.find((o) => o._id === maxCountObject?._id)?.price
    totalPrice.value = showPrice
    const ChoiceSessionCount = +route?.query?.ChoiceSession || 0
    if (isPointMode.value) {
      serviceOptions.push({
        count: ChoiceSessionCount,
        oriPrice: 0,
        price: 0,
        hasDiscount,
        gifts: 0,
        _id: 'customize_point',
      })
      totalPoint.value = CalPoint(showPrice, claimCategoryMap[data?.serviceRoles], pStore.claimSetting)
    }

    if (isPointMode.value && ChoiceSessionCount > 0) {
      servicePackageChoice.value = 'customize_point'
      serviceCustomizePoint.value = ChoiceSessionCount
      nextTick(() => {
        onServicePackageCustomizeUpdate()
      })
    } else {
      servicePackageChoice.value = maxCountObject?._id
    }

    if (isFreeService.value) {
      serviceOptions = [
        {
          count: 1,
          oriPrice: 0,
          price: 0,
          hasDiscount: false,
          gifts: 0,
          _id: 'freeServiceClaimed_service',
        },
      ]
      servicePackageChoice.value = 'freeServiceClaimed_service'
    }

    servicePackageOptions.value = serviceOptions
  }
  detail.value = data
}

const initData = () => {
  main().catch((err) => {
    dialogShow.value = true
    console.log('err', err)
  })
}

onMounted(initData)

watchEffect(() => {
  const data = {
    totalPrice: totalPrice.value,
    totalPoint: totalPoint.value,
    dialogShow: dialogShow.value,
  }
  emit('updateData', data)
})

defineExpose({checkoutData, initData})
</script>
<style lang="scss" scoped>
.text-underline {
  text-decoration: underline;
}
</style>
