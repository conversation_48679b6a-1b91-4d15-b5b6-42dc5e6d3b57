<template>
  <div class="pdf">
    <div class="row">
      <div class="col text-h6 q-py-lg">Receipt ID: {{ detail.no }}</div>
    </div>
    <div class="row justify-between">
      <div class="col-6">
        <div class="q-pa-md q-border-1 rounded-borders-sm">
          <div class="row items-center q-mb-lg" v-for="item in detail.links" :key="item.id">
            <q-img
              :src="item.base64"
              spinner-color="white"
              style="width: 96px; height: 70px; border-radius: 12px"
              img-class="my-custom-image"
              class="rounded-borders"></q-img>
            <div class="q-ml-md text-subtitle1 col-grow" style="flex-basis: 120px">
              <div>{{ item.name }}</div>
            </div>
            <div class="q-ml-auto text-subtitle1 text-bold text-red-10 text-right" style="flex-basis: 120px">${{ (item.price / 100).toFixed(2) }} USD</div>
          </div>
          <q-separator />
          <div class="text-h6 q-my-md">Cancellation policy</div>
          <template v-if="detail.type == 'unit'">
            <div class="text-grey-6 q-mb-md">No cancellation for teaching resources purchased from Library.</div>
          </template>
          <template v-if="detail.type == 'session_self_study'">
            <div class="text-grey-6 q-mb-md">No cancellation for self-study contents enrolled from Classcipe Center.</div>
          </template>
          <template v-if="detail.type == 'session_public'">
            <div class="text-grey-6">Workshops</div>
            <div class="text-grey-6 q-mb-md">Free cancellation within 24 hours before the start of the workshop.</div>
          </template>
          <template v-if="detail.type == 'session_service_pack'">
            <div class="text-grey-6 q-mb-md">
              Free cancellation within 24 hours before the start of the workshop. All bonded service package will be automatically canceled. Boned service
              package can not be canceled separately as an essential part of the workshop.
            </div>
          </template>
          <template v-if="detail.type == 'service_pack'">
            <div class="text-grey-6">Mentoring Service Package:</div>
            <div class="text-grey-6 q-mb-md">Free cancellation of the remaining unused sessions by the time you have used two single-session services.</div>
          </template>
        </div>
        <div class="q-mt-md q-pa-md">
          <div class="text-h5">Have a question?</div>
          <div class="text-subtitle2 text-grey-6 q-mt-md">Find details about payments and refunds in yourpayments or try the Help Centre.</div>
        </div>
      </div>
      <div class="col-5">
        <div class="q-border-1 q-pa-md rounded-borders-sm">
          <div class="text-h6">Order summary</div>
          <div class="row justify-between q-mt-md">
            <div class="col text-subtitle1">Subtotal</div>
            <div class="col text-right text-subtitle1">${{ detail.price / 100 }}</div>
          </div>
          <div class="row justify-between q-mt-md">
            <div class="col text-h6">Total</div>
            <div class="col text-right text-subtitle1 text-bold">${{ detail.price / 100 }}</div>
          </div>
        </div>
        <div class="q-border-1 q-mt-md q-pa-md rounded-borders-sm">
          <div class="text-h6">Order summary</div>
          <div class="row justify-between q-mt-md">
            <div class="col text-subtitle1" v-if="detail?.payMethod?.indexOf('braintree') > -1">
              <div>{{ detail?.paymentInfo.paymentInstrumentType || '' }} {{ detail?.paymentInfo.cardType || '' }} {{ detail?.paymentInfo.last4 || '' }}</div>
              <div class="text-grey-6 text-subtitle2">{{ new Date(detail.paidAt).toLocaleString() }}</div>
            </div>
            <div class="col text-right text-h5">${{ (detail.price / 100).toFixed(2) }}</div>
          </div>
          <q-separator class="q-my-lg" />
          <div class="row justify-between">
            <div class="text-h5 text-bold col-grow">Amount paid(USD)</div>
            <div class="text-h5 q-ml-auto text-right" style="flex-basis: 120px">${{ (detail.price / 100).toFixed(2) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
/*
  imports
*/
import {onMounted, ref, nextTick} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {date} from 'quasar'

let props = defineProps({
  detail: {
    type: Object,
    default: {},
  },
})
</script>
<style lang="scss" scope>
.pdf {
  position: absolute;
  top: 0;
  right: 10000px;
  width: 1040px;
  padding: 20px;
  margin: 0 auto;
  background-color: #fff;
}
</style>
