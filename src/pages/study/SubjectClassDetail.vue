<template>
  <q-layout view="hHh LpR fFf">
    <PubHeader />
    <q-page-container class="pc-sm explicit-page-container bg-white">
      <q-page class="column overflow-hidden" v-if="loading">
        <div class="col text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
      </q-page>
      <q-page v-else>
        <div class="q-pa-md q-mb-md">
          <div class="row items-center justify-between">
            <div class="text-weight-medium col row items-center text-h5">
              <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
              {{ classDetail.name }}
            </div>
          </div>
          <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-md" v-if="isShowBanner">
            {{ bannerText }}
            <template v-slot:action>
              <q-btn flat icon="close" class="q-pa-sm" @click="isShowBanner = false" />
            </template>
          </q-banner>
          <div class="q-my-md">
            <q-img
              spinner-color="white"
              fit="cover"
              class="fit rounded-borders-md"
              :ratio="16 / 5"
              :src="hashToUrl(classDetail.attachmentsCover?.hash) || '/v2/img/avatar.png'">
            </q-img>
          </div>
          <div class="row">
            <div class="text-red" v-if="!isApply">Enrolment ends in: <CountDown :deadTime="classDetail?.deadline" /></div>
            <div class="text-red" v-else>{{ statusText }}</div>
            <div class="q-ml-md row items-center">
              <q-icon name="group" size="18px" />
              <span class="q-ml-sm">Enrolled</span>
              <span class="q-ml-sm"> {{ classDetail.count?.student }}{{ classDetail.maxParticipants ? `/${classDetail.maxParticipants}` : '' }} </span>
            </div>
          </div>
          <div class="q-mt-sm">
            Term: {{ schoolTerm.title }} {{ moment(schoolTerm.start).format('MMMM YYYY') }} - {{ moment(schoolTerm.end).format('MMMM YYYY') }}
          </div>
          <q-btn
            outline
            rounded
            :color="enrollDisabled ? 'grey' : 'primary'"
            label="Enrol this class now"
            class="q-mt-lg full-width"
            icon="school"
            @click="enroll"
            :disable="enrollDisabled" />
        </div>
        <div v-for="block in classDetail.block" :key="block._id" class="text-subtitle2">
          <span>{{ moment().day(block.week).format('dddd') }}: {{ block.start }}-{{ block.end }}</span>
        </div>
        <div class="text-subtitle2 q-mt-md" v-if="classDetail.block?.length > 0">
          <span>Repeats: every {{ classDetail.every }} {{ classDetail.repeat }}</span>
        </div>
        <CalendarWeekHoursNoTool class="q-mt-md" v-model="classDetail.block" :editable="false"></CalendarWeekHoursNoTool>
      </q-page>
    </q-page-container>
    <ModalQuestion
      :isVisible="isQuestionModalVisible"
      :setIsVisible="setQuestionModalVisible"
      :questions="classDetail.questions || []"
      @save="submitQuestion" />
  </q-layout>
</template>

<script setup>
import {ref, onMounted, inject, watch, computed, onUnmounted, openBlock} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import CountDown from 'src/pages/order/CountDown.vue'
import PromptPage from 'components/PromptPage.vue'
import moment from 'moment'
import useSchool from 'src/composables/common/useSchool'
import ModalQuestion from './ModalQuestion.vue'
import CalendarWeekHoursNoTool from 'components/CalendarWeekHoursNoTool.vue'

/*
  consts
*/
const isQuestionModalVisible = ref(false)
const setQuestionModalVisible = (bool) => (isQuestionModalVisible.value = bool)

const pub = pubStore()
const route = useRoute()
const router = useRouter()

const {isLogin, isAdmin, isStudent, isSchool, schoolId} = useSchool()

const loading = ref(false)
const isShowBanner = ref(false)
const bannerText = ref('')
const isApply = ref(false)
const statusText = ref('')

const enrollDisabled = ref(false)
const classId = ref(route.params.id)
const classDetail = ref({})
const schoolTerm = ref({})
const student = ref({})
/*
  exposes
*/

/*
  computeds
*/

/*
  watches
*/

/*
  methods
*/
const getData = async () => {
  loading.value = true

  const classData = await App.service('classes').get(classId.value)
  classDetail.value = classData

  let termData = await App.service('school-term').get(classData.term)
  schoolTerm.value = termData

  let studentData = await App.service('students').find({query: {school: schoolId.value, uid: pub.user.uid}})
  student.value = studentData.data[0]

  // 无权限学生判定
  if (student.value.del || student.value.school != schoolId.value) {
    $q.dialog({
      component: PromptPage,
      componentProps: {
        fullscreen: true, //是否全屏
        type: 'unauthorized',
        title: 'You have no access to the page',
      },
    })
  }

  let applyData = await App.service('class-apply').find({query: {class: classId.value, student: student.value._id}})

  if (applyData.data.length > 0) {
    // 已报名
    isShowBanner.value = true
    isApply.value = true
    enrollDisabled.value = true
    if (applyData.data[0].status == 0) {
      bannerText.value = "You have enrolled, please wait patiently for your teacher's approval."
      statusText.value = 'Enrolment pending'
    } else if (applyData.data[0].status == 1) {
      bannerText.value = 'You have enrolled successfully.'
      statusText.value = 'Enrolment approved'
    } else if (applyData.data[0].status == -1) {
      bannerText.value = 'Your enrollment has been rejected.'
      statusText.value = 'Enrolment rejected'
    }
  } else if (student.value.subjectClass && student.value.subjectClass.includes(classId.value)) {
    // 已import加入
    isShowBanner.value = true
    bannerText.value = 'You have enrolled successfully.'
    isApply.value = true
    statusText.value = 'Imported'
    enrollDisabled.value = true
  } else if (classData.maxParticipants && classData.count.student >= classData.maxParticipants) {
    // 人数到上限
    isShowBanner.value = true
    bannerText.value = 'Max participants No has reached'
    enrollDisabled.value = true
  } else if (classData.deadline && moment(classData.deadline).isBefore(moment())) {
    // 过截止日期
    isShowBanner.value = true
    bannerText.value = 'Enrolment has ended'
    enrollDisabled.value = true
  } else if (!classData.enroll.enable) {
    // 未开启自主报名
    isShowBanner.value = true
    bannerText.value = 'The self-enrolment for this class isdisabled.'
    enrollDisabled.value = true
    $q.dialog({
      title: 'The self-enrolment for this class isdisabled.',
      ok: {
        label: 'I got it',
        noCaps: true,
        rounded: true,
      },
    }).onOk(() => {})
  }

  loading.value = false
}

const goBack = () => {
  if (route.query.back) {
    router.replace(route.query.back)
  } else {
    router.go(-1)
  }
}
const enroll = async () => {
  console.log('🚀 ~ enroll ~ classDetail.value:', classDetail.value)
  if (classDetail.value.approvalEnable) {
    setQuestionModalVisible(true)
  } else {
    createApply()
  }
}
const submitQuestion = async (answers) => {
  createApply(answers)
}

const createApply = async (answers = []) => {
  $q.loading.show()
  try {
    await App.service('class-apply').create({
      student: student.value._id,
      school: schoolId.value,
      class: classId.value,
      answers: answers,
    })
    $q.notify({type: 'positive', message: 'Apply successfully'})
    await getData()
  } catch (error) {
    console.log('🚀 ~ createApply ~ error:', error)
    $q.notify({type: 'negative', message: error.message})
  } finally {
    $q.loading.hide()
  }
}

onMounted(async () => {
  await getData()
})
</script>
