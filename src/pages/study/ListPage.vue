<template>
  <HeaderDrawer @change="onDrawerLoad" @update="onTabUpdate"></HeaderDrawer>
  <q-page-container>
    <q-page class="q-pa-md pc-body">
      <div class="flex justify-end">
        <q-btn
          rounded
          no-caps
          class="q-mb-md"
          color="primary"
          label="Subject class enrollment"
          @click="onSubjectClassEnrollment"
          v-if="classDetail.type == 'standard'" />
      </div>
      <div class="rounded-borders-md shadow-3 bg-white overflow-hidden">
        <template v-if="isMyPurchased && tab == 'workshop' && indexPromotions?.length">
          <CardList title="" recommend category="promotion" :list="indexPromotions" />
        </template>
        <div class="q-pa-md text-h5 text-weight-medium flex justify-between">
          <span>{{ title }}</span>
          <q-btn rounded color="red" label="Withdraw" no-caps @click="onWithdraw" v-if="classDetail.type == 'subject'" />
        </div>
        <div class="row items-center q-px-md">
          <div class="col-xs-10 col-sm-7 col-md-7 q-pb-md justify-between">
            <q-tabs
              dense
              align="left"
              :breakpoint="0"
              indicator-color="primary"
              active-color="primary"
              v-model="subtab"
              inline-label
              mobile-arrows
              shrink
              @update:model-value="find(0)">
              <q-tab v-for="(item, index) in subtabs" :name="item.name" :label="item.label" :key="index" no-caps></q-tab>
            </q-tabs>
          </div>
          <div v-if="isMyClass && tab !== 'lecture'" class="text-right col-xs-2 col-sm-5 col-md-5">
            <q-btn flat round size="0.7rem" @click.prevent icon="more_vert">
              <q-menu>
                <q-list>
                  <q-item clickable v-close-popup @click="onAnnouncementClick">
                    <q-item-section>Announcement</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </div>
        </div>
        <div class="q-px-md" v-if="mounted && drawerLoaded">
          <GeneralFilters
            v-model="filters"
            :appendage="appendage"
            :pagination="pagination"
            :options="filterOptions"
            @update:modelValue="onFilterUpdate"></GeneralFilters>
        </div>
        <div class="column" style="height: 100vh">
          <div class="col q-pa-md" v-if="isCourseOnly && filters.courseView == 'gantt'">
            <div class="full-width flex justify-center" v-if="isEmpty(listData)">
              <q-spinner-ball color="primary" size="2em" class="full-width" v-if="loading" />
              <NoData size="9rem" v-else message="no live session" />
            </div>
            <div class="q-mb-md border-top-05 fit" v-if="!isEmpty(listData)">
              <q-scroll-area visible class="fit">
                <div class="row">
                  <div class="col-3 border-left-05 border-bottom-05">
                    <div class="text-center border-05 q-pa-sm ellipsis" style="height: 40px">Course Name</div>
                    <div
                      v-for="(course, index) in listData"
                      :key="index"
                      style="height: 55px"
                      class="ellipsis col-3 text-left border-05 q-pa-md cursor-pointer"
                      @click="onCourseClick(course)">
                      <q-img spinner-color="white" fit="cover" style="max-width: 30px" :ratio="3 / 2" :src="course.image"> </q-img>
                      <span class="q-pl-xs text-weight-medium gt-xs">
                        {{ course.name }}
                      </span>
                    </div>
                  </div>
                  <div class="col border-right-05 border-bottom-05" :style="`max-width: ${150 * months.length}px`">
                    <q-scroll-area ref="scrollAreaRef" visible class="fit">
                      <div class="row full-width no-wrap">
                        <div v-for="(month, index) in months" :key="index" style="width: 150px; height: 40px" class="text-center q-py-sm border-05">
                          {{ month }}
                        </div>
                      </div>
                      <div class="row full-width" v-for="(course, index) in listData" :key="index">
                        <div v-for="(month, index) in months" :key="index" style="width: 150px; height: 55px" class="text-center q-py-sm border-05"></div>
                        <div class="absolute bg-primary q-pa-sm rounded-borders q-my-md" :style="barStyle(course)"></div>
                      </div>
                    </q-scroll-area>
                  </div>
                </div>
              </q-scroll-area>
            </div>
          </div>
          <template v-else>
            <template v-if="isEmpty(listData)">
              <NoData v-if="!loading" />
              <div v-else class="text-center q-pa-xl text-grey">
                <q-spinner-ball messageColor="primary" size="2em" class="full-width" />
              </div>
            </template>
            <q-virtual-scroll v-else separator class="col q-px-sm full-width overflow-auto" :items="listData" @virtual-scroll="scrollFn">
              <template v-slot:after>
                <div v-if="list.data.length === list.limit" class="row justify-center q-my-md">
                  <q-spinner-ball color="primary" size="2em" />
                </div>
                <div v-else class="q-pa-md text-grey text-center hidden">It's over</div>
              </template>
              <template v-slot="{item: o, index: i}">
                <div :key="i" class="q-pa-sm">
                  <TaskCard
                    v-if="tab == 'myAssociatedTask'"
                    isView
                    isMyAssociatedTask
                    :creditDeficit="o.sectionCreditDeficit?.points"
                    :task="o.snapshot"
                    :clickAllowed="true"
                    :associatePackUserId="o._id"
                    :orderPrice="o.price" />
                  <BookingBoard v-else-if="tab == 'mentoring' && subtab == 'booked'" :booking="o"> </BookingBoard>
                  <SessionBoard
                    :isPointMode="isPointMode"
                    v-else-if="!o.hidden"
                    :isMyPurchased="isMyPurchased"
                    :isMyWorkshop="isMyWorkshop"
                    :isMyClass="isMyClass"
                    :isLectureRoom="tab == 'lecture'"
                    :isSelfStudy="tab == 'self'"
                    :subtab="subtab"
                    :categories="categoryOptions"
                    :session="o"
                    isListing
                    @enrolled="onEnrolled"
                    @change="resetFn()"></SessionBoard>
                </div>
              </template>
            </q-virtual-scroll>
          </template>
        </div>
      </div>
    </q-page>
  </q-page-container>
</template>
<script setup>
/*
imports
*/
import {ref, onMounted, onBeforeUnmount, watch, computed, inject} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import {LocalStorage, date} from 'quasar'
import {subjectsStore} from 'stores/subjects'
import {curriculumStore} from 'stores/curriculum'
import {pointStore} from 'stores/point'

import AccountMenu from 'components/AccountMenu.vue'
import NoticePage from 'components/NoticePage.vue'
import SessionBoard from 'components/SessionBoard.vue'
import BookingBoard from 'components/BookingBoard.vue'
import CardList from 'components/CardList.vue'
import GeneralFilters from 'components/GeneralFilters.vue'
import ImportDialog from 'components/ImportDialog.vue'
import PersonAvatar from 'src/pages/account/assessment-tool/components/PersonAvatar.vue'
import PromptPage from 'components/PromptPage.vue'
import useSchool from 'src/composables/common/useSchool'
import HeaderDrawer from 'components/HeaderDrawer.vue'
import TaskCard from 'components/ServiceTask/TaskCard.vue'

/*
consts
*/
const pStore = pointStore()
const virtualListIndex = ref(1)
const indexPromotions = ref([])
const title = ref(null)
const route = useRoute()
const router = useRouter()
const pub = pubStore()
const subjects = subjectsStore()
const curriculum = curriculumStore()
const curriculumOptions = ref([])
const filters = ref({})
const tab = ref(null)
const subtab = ref(null)
const loading = ref(false)
const {schoolId} = useSchool()
const contentsType = inject('ContentsType')
const gradeGroupMap = inject('GradeGroupMap')
const taskSessionTypes = inject('TaskSessionTypes')
const injectSubjects = inject('SubjectOptions')
const list = ref({})
const listData = ref([])
const mounted = ref(false)
const drawerLoaded = ref(false)
const isMyWorkshop = ref(false)
const isMyServices = ref(false)
const isMyPurchased = ref(false)
const isMyClass = ref(false)
const isWorkshop = ref(false)
const months = ref([])
const scrollAreaRef = ref(null)
const roomStatistics = ref({})
const launchBy = ref('me')
const classDetail = ref({})

const categoryOptions = ref([])
const subjectOptions = ref([])
const dateFormat = inject('DATE_FORMAT')

const groupGradeOptions = ref(
  Object.entries(gradeGroupMap).map(([value, {label}]) => ({
    value,
    label,
  }))
)

const gradeId = ref(null)
const isPointMode = ref(!!route.query.isPointMode)

/*
computed
*/
const pagination = computed(() => {
  return {start: virtualListIndex.value + 1, end: listData.value.length, total: list.value.total}
})

const appendage = computed(() => {
  return isMyPurchased.value && tab.value == 'workshop'
})

const filterOptions = computed(() => {
  const options = []
  const _range = {
    key: 'range',
    label: 'Period',
    type: 'calendar',
    default: [],
  }
  const _singleCategory = {
    key: 'singleCategory',
    label: 'Category',
    type: 'radio',
    default: 'academic',
    options: categoryOptions.value,
  }

  const _multiCategory = {
    key: 'multiCategory',
    label: 'Category',
    type: 'checkbox',
    default: [],
    options: categoryOptions.value,
  }

  const _workshopType = {
    key: 'workshopType',
    label: 'Workshop type',
    type: 'checkbox',
    default: [],
    options: [
      {label: 'Premium', value: 'premium'},
      {label: 'Standard', value: 'standard'},
    ],
  }

  const _curriculums = {
    key: 'curriculums',
    label: 'Curriculum(s)',
    type: 'checkbox',
    default: [],
    collapsed: isMyPurchased.value && tab.value == 'workshop',
    options: curriculumOptions.value,
  }

  const _subjects = {
    key: 'subjects',
    label: 'Subject(s)',
    type: 'checkbox',
    default: [],
    collapsed: isMyPurchased.value && tab.value == 'workshop',
    options: subjectOptions.value,
  }

  const _grades = {
    key: 'grades',
    label: 'Grade(s)',
    type: 'checkbox',
    default: [],
    collapsed: isMyPurchased.value && tab.value == 'workshop',
    options: groupGradeOptions.value,
  }

  const _sessionType = {
    key: 'sessionType',
    label: 'Session type',
    type: 'radio',
    default: 'live',
    options: taskSessionTypes.map(({label, title}) => ({label: title, value: label})),
  }

  if (isMyClass.value) {
    _sessionType.type = 'checkbox'
    _sessionType.default = []
  }

  if (isMyPurchased.value) {
    if (tab.value == 'workshop') {
      if (subtab.value == 'featured') {
        options.push(_workshopType, _multiCategory)
        if (filters.value.multiCategory?.length == 1 && filters.value.multiCategory[0] == 'academic') {
          options.push(_curriculums, _subjects, _grades)
        }
      } else {
        options.push(_multiCategory)
      }
    } else if (tab.value == 'mentoring') {
      options.push(_multiCategory)
    } else if (tab.value == 'self' && subtab.value == 'featured') {
      options.push(_curriculums, _subjects, _grades)
    }
  } else if (isMyClass.value) {
    if (tab.value !== 'lecture') {
      options.push(_sessionType)
    }
  }

  if (subtab.value == 'ended') {
    options.push(_range)
  }
  return options
})

const isAdmin = computed(() => {
  return pub.user?.schoolUser?.role?.includes('admin')
})

const isEducator = computed(() => {
  return !pub.user?.roles?.includes('student')
})

const isPersonal = computed(() => {
  const schoolId = pub.user?.schoolInfo?._id
  if (isMyPurchased.value && schoolId) {
    router.replace('/study/index')
  }
  return !schoolId
})

const isCourseOnly = computed(() => {
  return filters.value.type?.length == 1 && filters.value.type[0] == 'courses'
})

const subtabs = computed(() => {
  const _tabs = []

  if (tab.value === 'myAssociatedTask') {
    _tabs.push(
      {name: 'ongoing', _label: 'Ongoing', label: 'Ongoing'},
      {name: 'completed', _label: 'Completed', label: 'Completed'},
      {name: 'unassigned', _label: 'Unassigned', label: 'Unassigned'}
    )
  } else if (tab.value == 'self') {
    _tabs.push({name: 'featured', _label: 'Featured', label: 'Featured'}, {name: 'enrolled', _label: 'Enrolled', label: 'Enrolled'})
  } else {
    if (tab.value == 'workshop' || (isMyClass.value && tab.value == 'lecture')) {
      _tabs.push({name: 'featured', _label: 'Featured', label: 'Featured'})
    }
    if (tab.value == 'mentoring') {
      _tabs.unshift({name: 'booked', _label: 'Booked', label: 'Booked'})
    }
    _tabs.push(
      {name: 'scheduled', _label: 'Scheduled', label: 'Scheduled'},
      {name: 'ongoing', _label: 'Ongoing', label: 'Ongoing'},
      {name: 'ended', _label: 'Ended', label: 'Ended'}
    )
  }
  const total = list.value?.total ?? 0
  return _tabs.map((item) => {
    if (item.name === subtab.value) {
      return {...item, label: item.label + `(${total})`}
    }
    return item
  })
})

/*
methods
*/

const getIndexPromotion = async () => {
  await App.service('session')
    .get('indexPromotion')
    .then((res) => {
      if (res?.total) {
        indexPromotions.value = res.data
      }
    })
}

const getGradesByGroup = (groups) => {
  const grades = []
  groups.forEach((group) => {
    grades.push(...gradeGroupMap[group]['grades']['classcipe'])
  })

  return grades
}

const localstorageForFilter = (setting) => {
  let key = 'FILTER_OF_STUDENT_SERVICES_WORKSHOP'
  if (isMyPurchased.value) {
    key = 'FILTER_OF_STUDENT_PURCHASED'
  } else if (isMyClass.value) {
    key = 'FILTER_OF_STUDENT_CLASSES'
  }

  if (setting) {
    LocalStorage.set(key, {...filters.value, ...{subtab: subtab.value}})
  } else {
    const localFilters = LocalStorage.getItem(key)
    if (!Acan.isEmpty(localFilters)) {
      delete localFilters.search
      filters.value = localFilters
    }
    if (localFilters?.subtab) {
      subtab.value = localFilters.subtab
    } else if (isMyPurchased.value) {
      subtab.value = 'featured'
    } else {
      subtab.value = 'ongoing'
    }
  }
}

const onCourseClick = (course) => {
  //if (isLiveWorkshop.value && liveTab.value == 'featured') return
  //router.push(`/com/course/${course._id}?back=${route.path}`)
}

const viewIsActive = (view) => {
  return filters.value.courseView == view
}

const scrollFn = async ({index, to}) => {
  virtualListIndex.value = index
  if (index !== to) return // console.warn(index, to, direction)
  const {limit = 10, skip = 0, data} = list.value
  if (Acan.isEmpty(data)) return // has last page
  console.warn('need load more')
  find(skip + limit)
}
const onWithdraw = async () => {
  $q.dialog({
    title: 'Confirm to withdraw',
    cancel: true,
    ok: {
      label: 'Withdraw',
      noCaps: true,
      rounded: true,
    },
  }).onOk(async () => {
    await App.service('students').patch(pub.user.schoolUser._id, {
      $pull: {
        subjectClass: tab.value,
      },
    })
    $q.notify({type: 'positive', message: 'Withdraw successfully'})
    router.push('/study/index')
  })
}
const onSubjectClassEnrollment = () => {
  router.push(`/study/subjectClass?classId=${classDetail.value._id}`)
}

/*courses*/
const onEnrolled = (enrolled) => {
  if (enrolled) {
    if (tab.value == 'self') {
      subtab.value = 'enrolled'
    } else {
      subtab.value = 'scheduled'
    }
  }
  find(0)
}

const find = async ($skip) => {
  $q.loading.show()
  let start = null
  let end = null
  let isAllType = true
  let isCourses = false

  if (!Acan.isEmpty(filters.value?.type)) {
    if (filters.value.type.length == 1) {
      if (filters.value.type[0] == 'courses') {
        isCourses = true
      }
      isAllType = false
    }
  }

  if ($skip === 0) {
    resetData()
  }

  const query = {
    pid: {$exists: false},
    del: false,
    $sort: {createdAt: -1},
    $limit: 10,
    $skip,
  }

  const now = new Date()
  const zone = now.getTimezoneOffset()

  if (filters.value.range) {
    const selectedDateArr = filters.value.range.split('~')
    start = new Date(date.startOfDate(selectedDateArr[0], 'day'))
    end = new Date(date.endOfDate(selectedDateArr[1], 'day'))
  }

  if (isMyPurchased.value) {
    if (pub.user.schoolInfo?._id) {
      query.school = pub.user.schoolInfo?._id
    }

    if (tab.value == 'workshop') {
      const types = Object.values(contentsType).filter(
        (item) =>
          !item.content &&
          !item.educator &&
          !item.tool &&
          item.public &&
          (isPersonal.value ? !item.school : item.school) &&
          (isAllType ? true : isCourses ? item.course : item.session || item.tool)
      )
      query.type = {$in: types.map((item) => item.value)}
      const categories = filters.value.multiCategory

      if (subtab.value == 'featured') {
        query.regDate = {$gte: start && start > now ? start : now}
        query.isLib = true
        /*
        query.premium = true
        if (filters.value.singleCategory == 'academic') {
          query.type = {
            $in: query.type.$in.filter((item) => {
              return !contentsType[item]['service']
            }),
          }
        } else {
          query['task.service.type'] = filters.value.singleCategory
          query.type = {
            $in: query.type.$in.filter((item) => {
              return contentsType[item]['service']
            }),
          }
        }
        */

        if (filters.value.workshopType?.length == 1) {
          if (filters.value.workshopType[0] == 'premium') {
            query.premium = true
          } else {
            query.premium = false
          }
        }

        if (categories?.length == 1 && categories[0] == 'academic') {
          if (filters.value.curriculums?.length) {
            query['task.curriculum'] = {$in: filters.value.curriculums}
          }
          if (filters.value.subjects?.length) {
            query['subjects.value'] = {$in: filters.value.subjects}
          }
          if (filters.value.grades?.length) {
            query['task.grades.value'] = {$in: getGradesByGroup(filters.value.grades)}
          }
        }
      } else {
        query['reg._id'] = pub.user._id
      }

      if (categories?.length == 1 && categories[0] == 'academic') {
        query.type = {
          $in: query.type.$in.filter((item) => {
            return !contentsType[item]['service']
          }),
        }
      } else if (categories?.length > 1 && categories.includes('academic')) {
        query.$or = [
          {
            'task.service.type': {
              $in: categories.filter((item) => {
                return item !== 'academic'
              }),
            },
            type: {
              $in: query.type.$in.filter((item) => {
                return contentsType[item]['service']
              }),
            },
          },
          {
            type: {
              $in: query.type.$in.filter((item) => {
                return !contentsType[item]['service']
              }),
            },
          },
        ]
        delete query.type
      } else if (categories?.length) {
        query['task.service.type'] = {$in: categories}
        query.type = {
          $in: query.type.$in.filter((item) => {
            return contentsType[item]['service']
          }),
        }
      }
    } else if (tab.value == 'self') {
      query.type = 'selfStudy'
      if (subtab.value == 'featured') {
        //query.regDate = {$gte: start && start > now ? start : now}
        query.isLib = true
      } else {
        query['reg._id'] = pub.user?._id
      }
      if (filters.value.curriculums?.length) {
        query['task.curriculum'] = {$in: filters.value.curriculums}
      }
      if (filters.value.subjects?.length) {
        query['subjects.value'] = {$in: filters.value.subjects}
      }
      if (filters.value.grades?.length) {
        query['task.grades.value'] = {$in: getGradesByGroup(filters.value.grades)}
      }
    } else if (tab.value == 'mentoring') {
      const types = Object.values(contentsType).filter(
        (item) => !item.content && (isPersonal.value ? !item.school : item.school) && !item.educator && tab.value == 'mentoring' && item.booking
      )
      query.type = {$in: types.map((item) => item.value)}
    }
  } else {
    //isMyClass
    if (tab.value == 'lecture') {
      const types = Object.values(contentsType).filter(
        (item) =>
          !item.content &&
          item.public &&
          !item.educator &&
          item.school &&
          !item.tool &&
          (isAllType ? true : isCourses ? item.course : item.session || item.tool)
      )
      query.type = {$in: types.map((item) => item.value)}
      if (pub.user.schoolInfo?._id) {
        query.school = pub.user.schoolInfo?._id
      }
      if (subtab.value == 'featured') {
        query.regDate = {$gte: start && start > now ? start : now}
        query.grades = gradeId.value
        query.isLib = true
      } else {
        query['reg._id'] = pub.user?._id
      }
    } else {
      const types = Object.values(contentsType).filter(
        (item) => !item.content && item.class && !item.public && (isAllType ? true : isCourses ? item.course : item.session || item.tool)
      )
      query.type = {$in: types.map((item) => item.value)}
      if (filters.value.sessionType.length == 1) {
        query.sessionType = filters.value.sessionType[0]
      }
      query.students = pub.user?._id
      if (isPersonal.value) {
        query.personal = true
      } else {
        query.classId = tab.value
      }
    }
  }

  //query.$sort
  if (subtab.value === 'featured') {
    query.$sort = {_id: -1}
  } else if (subtab.value === 'scheduled') {
    query.$sort = {start: 1}
  } else if (subtab.value === 'ongoing') {
    query.$sort = {start: -1}
  } else if (subtab.value === 'ended') {
    query.$sort = {ended: -1}
  }

  //query.status
  if (subtab.value == 'featured') {
    if (tab.value == 'self') {
      //query.status = 'ongoing'
    } else {
      query.status = 'scheduled'
    }
  } else if (subtab.value == 'enrolled') {
    query.status = {$ne: 'close'}
  } else {
    query.status = subtab.value
  }

  // if (tab.value !== 'self' && !['featured', 'scheduled'].includes(subtab.value))
  if (tab.value !== 'self' && subtab.value == 'ended') {
    query.dateRange = [start, end, zone]
  }

  /*
  if (filters.value.subjects?.length) {
    if ((filters.value.participant == 'students' && isMyWorkshop.value) || isMyClass.value) {
      query['subjects.session'] = {$in: filters.value.subjects}
    } else {
      query['task.service.type'] = {$in: filters.value.subjects}
    }
  }*/

  if (filters.value.search) {
    query.$or = [{name: {$search: filters.value.search}}]
  }
  // Ref: #6097
  const categories = filters.value.multiCategory || []
  if (categories?.length) {
    if (categories.length == 1) {
      if (categories.some((e) => e == 'academic')) {
        query.curriculum = {$ne: 'pd'}
      } else {
        query.curriculum = 'pd'
        query['task.service.type'] = categories
      }
    } else if (categories.some((e) => e == 'academic')) {
      query.$or = [{curriculum: {$ne: 'pd'}}, {'task.service.type': {$in: categories}}]
    } else {
      query['task.service.type'] = {$in: categories}
    }
  }

  loading.value = true
  if (isWorkshop.value && launchBy.value == 'others') {
    resetData()
  } else {
    if (isMyPurchased.value && subtab.value == 'featured') {
      //query.uid = {$ne: pub.user._id}
    }
    if (tab.value == 'mentoring' && subtab.value == 'booked') {
      const serviceType = filters.value?.multiCategory || []
      const dto = {
        cancel: null,
        'servicePackUser.snapshot.subject': query['task.service.type'],
        $limit: 10,
        $skip,
        tab: 'booker',
        session: null,
      }
      if (serviceType?.length) {
        dto.mentoringType = {$in: serviceType}
      }
      list.value = await App.service('service-booking').find({query: dto})
    } else if (tab.value === 'myAssociatedTask') {
      const query = {}
      if (subtab.value === 'unassigned') {
        query.associatedTaskStatus = {$in: ['unassigned', 'cancelled', 'refunded']}
      } else {
        query.associatedTaskStatus = subtab.value
      }
      query.status = true
      list.value = await App.service('service-pack-user').find({query: {'snapshot.type': 'serviceTask', pid: {$exists: true}, ...query}})
    } else {
      if (tab.value == 'mentoring') {
        query.students = pub.user._id
      }
      list.value = await App.service('session').find({query})
    }
    listData.value.push(...list.value.data)

    getAllMonthes(start, end)
  }
  loading.value = false
  const _query = {tab: tab.value, subtab: subtab.value, range: filters.value.range}
  if (Array.isArray(filters.value.type)) {
    _query.type = filters.value.type.join()
  }
  if (isCourseOnly.value) {
    _query.view = filters.value.courseView ?? 'list'
  }
  if (tab.value == 'mentoring') {
    delete _query.stype
    delete _query.type
  }
  if (subtab.value !== 'ended') {
    delete _query.range
  }

  //localstorageForFilter(true)
  if ($skip === 0) {
    if (isPointMode.value) {
      _query.isPointMode = 1
    }
    router.replace({query: _query})
  }
  $q.loading.hide()
}

const resetData = () => {
  listData.value = []
  list.value = {}
}

const getAllMonthes = (start, end) => {
  months.value = []

  while (months.value.length < 12) {
    months.value.push(date.formatDate(new Date(start), 'MMM'))
    start = date.addToDate(new Date(start), {months: 1})
  }
}

const scrollToHead = () => {
  const sortedCourses = listData.value.toSorted((a, b) => {
    return new Date(a.start) - new Date(b.start)
  })

  if (sortedCourses?.length) {
    const position = barStyle(sortedCourses[0])
    scrollAreaRef.value?.setScrollPosition('horizontal', parseInt(position.left) - 150, 200)
  }
}

const barStyle = (course) => {
  let _start = course.start
  const _end = course.end
  let noEnd = false
  const range = filters.value.range.split('~')
  if (new Date(_start) < new Date(range[0])) {
    _start = range[0]
  }
  const startMonth = date.formatDate(new Date(_start), 'MMM')
  const endMonth = date.formatDate(new Date(_end), 'MMM')

  if (!_end || new Date(_end) > new Date(range[1])) {
    noEnd = true
  }

  let startIndex = 0
  let endIndex = 0

  months.value.forEach((month, index) => {
    if (month == startMonth) {
      startIndex = index
    }

    if (month == endMonth) {
      endIndex = index
    }
  })

  const leftInSeconds = date.getDateDiff(_start, range[0], 'seconds')
  const totalInseconds = date.getDateDiff(`${range[1]} 23:59:59`, range[0], 'seconds')
  const left = (leftInSeconds / totalInseconds) * 12 * 150
  const widthInSeconds = date.getDateDiff(_end, _start, 'seconds')

  let width = (widthInSeconds / totalInseconds) * 12 * 150

  if (noEnd) {
    width = 12 * 150 - left
  }

  return {left: left + 'px', width: width + 'px'}
}

const getStartOrEndOfAll = (startOrEnd) => {
  const sorted = listData.value.toSorted((a, b) => {
    if (startOrEnd == 'start') {
      return new Date(a.start) - new Date(b.start)
    } else {
      return new Date(b.end) - new Date(a.end)
    }
  })

  return sorted[0][startOrEnd]
}

const isClassRoom = (type) => {
  return ['session', 'workshop', 'taskWorkshop'].includes(type)
}

const findRooms = async (list) => {
  const arr = list.filter((v) => isClassRoom(v.type))
  const $in = arr.map((v) => v.sid)
  const rs = await App.service('rooms').get('countStatus', {query: {sid: {$in}}})
  roomStatistics.value = {...roomStatistics.value, ...rs}
}

const getCurriculum = async () => {
  curriculumOptions.value = Object.entries(curriculum.pubList).map(([value, label]) => ({value, label}))
}

const getSubjects = async () => {
  const pdOptions = await subjects.getPubPdOptions()
  categoryOptions.value.push(...pdOptions.filter((v) => v.participants === 'students'))
  categoryOptions.value.unshift({label: 'Academic', value: 'academic'})
  //subjectOptions.value = await subjects.getOptions(pub.user?.schoolInfo?._id || pub.user?._id)
  injectSubjects.forEach((item) => {
    subjectOptions.value.push({label: item, value: item})
  })
}

const onDrawerLoad = (activeTab, grade) => {
  classDetail.value = activeTab

  if (activeTab?.name) {
    title.value = activeTab.name
  }

  gradeId.value = grade
  drawerLoaded.value = true
}

const onTabUpdate = (val) => {
  tab.value = val._id
  title.value = val.name
  if (tab.value !== 'mentoring' && subtab.value == 'booked') {
    subtab.value = 'scheduled'
  } else if (tab.value == 'mentoring' && subtab.value == 'featured') {
    subtab.value = 'booked'
  } else if (tab.value !== 'self' && subtab.value == 'enrolled') {
    subtab.value = 'scheduled'
  }

  if (isMyWorkshop.value && tab.value == 'others') {
    list.value = {}
    listData.value = []
  } else {
    if (tab.value == 'self') {
      subtab.value = 'enrolled'
    } else if (tab.value == 'workshop' || (isMyClass.value && tab.value !== 'lecture')) {
      subtab.value = 'scheduled'
    }
    find(0)
  }
}

const onFilterUpdate = () => {
  find(0)
}

const defaultRange = () => {
  const today = new Date()
  const startDate = date.startOfDate(today, 'year')
  const endDate = date.endOfDate(today, 'year')
  return `${date.formatDate(new Date(startDate), dateFormat)}~${date.formatDate(new Date(endDate), dateFormat)}`
}

const onAnnouncementClick = () => {
  router.push({
    path: '/study/announcement',
    query: {
      classId: tab.value === 'private' ? undefined : tab.value,
      schoolId: pub.user?.schoolInfo?._id,
    },
  })
}

/*life Cycle*/
onMounted(async () => {
  if (route.path.indexOf('/study/purchased') != -1) {
    isMyPurchased.value = true
    tab.value = 'workshop'
    subtab.value = 'scheduled'
    title.value = 'My Purchased'
  }

  if (isMyWorkshop.value || isMyPurchased.value) {
    isWorkshop.value = true
  }

  if (route.path.indexOf('/study/class') != -1) {
    isMyClass.value = true
    tab.value = route.query.tab
    subtab.value = 'scheduled'
    title.value = 'My Classes'
  }

  //localstorageForFilter()

  if (['me', 'others'].includes(route.query.by)) {
    filters.value.taughtBy = route.query.by
  } else if (!filters.value.taughtBy) {
    filters.value.taughtBy = 'me'
  }

  if (['educators', 'students'].includes(route.query.part)) {
    filters.value.participant = route.query.part
  } else if (!filters.value.participant) {
    filters.value.participant = 'educators'
  }

  if (!isMyClass.value) {
    if (route.query.stype && ['live', 'student'].includes(route.query.stype)) {
      filters.value.sessionType = route.query.stype
    } else if (!filters.value.sessionType) {
      filters.value.sessionType = 'live'
    }
  }

  if (route.query.view && ['list', 'gantt'].includes(route.query.view)) {
    filters.value.courseView = route.query.view
  } else if (!filters.value.courseView) {
    filters.value.courseView = 'list'
  }

  if (route.query.tab && ['mentoring', 'workshop', 'self', 'myAssociatedTask'].includes(route.query.tab)) {
    tab.value = route.query.tab
  }

  if (route.query.subtab && ['featured', 'scheduled', 'ongoing', 'ended', 'enrolled', 'booked'].includes(route.query.subtab)) {
    subtab.value = route.query.subtab
  } else if (tab.value == 'self') {
    subtab.value = 'enrolled'
  } else if (tab.value == 'myAssociatedTask') {
    subtab.value = route.query.subtab || 'ongoing'
  }

  if (route.query.type && ['courses', 'sessions'].includes(route.query.type)) {
    filters.value.type = [route.query.type]
  } else if (Acan.isEmpty(filters.value.type)) {
    filters.value.type = []
  }

  if (route.query.range) {
    filters.value.range = route.query.range
  } else if (!filters.value.range) {
    filters.value.range = defaultRange()
  }

  //if (isMyPurchased.value && tab.value == 'workshop' && subtab.value == 'featured') {
  filters.value.singleCategory = 'academic'
  //}

  //find(0)

  if (isPointMode.value) {
    await pStore.getClaimSetting()
  }

  getSubjects()
  getCurriculum()
  if (isMyPurchased.value && tab.value == 'workshop') {
    getIndexPromotion()
  }
  mounted.value = true
  //initYearOptions()
})

watch(
  () => pub.user.schoolInfo,
  (newVal, oldVal) => {
    if (newVal) {
      router.replace('/study/index')
    }
  }
)
</script>
<style lang="sass" scope>
.border-1
  border: 1px solid #49bbbd4d
.border-05
  border: 0.5px solid #D7E3F1
.border-top-05
  border-top: 0.5px solid #D7E3F1
.border-left-05
  border-left: 0.5px solid #D7E3F1
.border-right-05
  border-right: 0.5px solid #D7E3F1
.border-bottom-05
  border-bottom: 0.5px solid #D7E3F1
</style>
