<template>
  <q-dialog :maximized="$q.screen.lt.sm" ref="dialogRef" @hide="onDialogHide" @before-show="beforeDialogShow">
    <q-card class="q-dialog-plugin criteria-dialog">
      <q-list class="q-ml-md">
        <q-item v-for="(co, ci) in data" :key="ci">
          <q-item-section class="bg-purple-3">
            <q-item-label>{{ co._id }}</q-item-label>
            <q-item-label>{{ co.name }}</q-item-label>
            <q-item-label>{{ co.task._id }}</q-item-label>
            <q-item-label>mode: {{ co.task.mode }}</q-item-label>
            <q-item-label>type: {{ co.task.type }}</q-item-label>
            <q-item-label>pages: {{ co.task.pages.length }}</q-item-label>
            <q-item-label>type: {{ co.type }}</q-item-label>
            <q-item-label>sessionType: {{ co.sessionType }}</q-item-label>
          </q-item-section>
          <q-item-section class="bg-lime-3" v-if="co.type === 'tool'">
            <q-item-label>学生数量: {{ o.regNum || o.students?.length }}</q-item-label>
            <q-item-label>评估项: {{ co.task?.toolCount }}</q-item-label>
            <q-item-label>评估统计: {{ co.toolStat }}</q-item-label>
            <q-item-label>
              <q-btn flat dense color="teal" :to="`/test/tooldata/${co.sid}`" label="toolData"></q-btn>
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-card>
  </q-dialog>
</template>
<script setup>
// import {ref, computed} from 'vue'
import {useDialogPluginComponent} from 'quasar'
defineEmits([...useDialogPluginComponent.emits])
const {dialogRef, onDialogHide} = useDialogPluginComponent()
defineProps(['data'])
</script>
