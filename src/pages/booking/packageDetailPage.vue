<template>
  <q-layout view="hHh LpR fFf">
    <PubHeader v-if="!servicePackUserId" :showShare="!purchased && !(isAdmin && isSchool) && pub?.user?._id" :link="shareLink" isShareDetail />
    <q-page-container class="pc-sm explicit-page-container bg-white">
      <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-md" v-if="isShowBanner">
        <q-icon name="info" size="medium" style="bottom: 1px" />
        <span class="text-weight-medium text-subtitle1">{{ bannerAssociatedTask }}</span>
        <template v-slot:action>
          <q-btn flat icon="close" class="q-pa-sm" @click="isShowBanner = false" />
        </template>
      </q-banner>
      <q-page class="column overflow-hidden" v-if="loading">
        <div class="col text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
      </q-page>
      <q-page v-else-if="unavailable">
        <NoData></NoData>
      </q-page>
      <q-page v-else>
        <div class="q-pa-md">
          <BreadCrumbs v-if="!isSysView && !servicePackUserId" title="Preview"></BreadCrumbs>
          <div class="row items-center justify-between">
            <div class="text-weight-medium col" :class="{'text-h5': !servicePackUserId}">
              <q-btn v-if="!servicePackUserId" flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
              {{ packageData.name }}
            </div>

            <div class="text-subtitle2 text-teal">
              <template v-if="purchased">
                <template v-if="!isContentOrientatedEnableAndPurchased">
                  <q-chip v-if="freePayMethods[id]" label="Free" color="primary" square outline :ripple="false" size="sm"></q-chip>
                  <q-icon v-else dense name="o_monetization_on" size="sm" style="color: #c06612"></q-icon>
                </template>
              </template>
              <template v-else-if="!allPricesAreHidden">
                <ShowPoint v-if="isPointMode" :price="lowestPrice * 100" :type="categoryMap[packageData.serviceRoles]" />
                <template v-else>
                  <span v-if="!packageData.contentOrientatedEnable">From</span>
                  <template v-if="packageData.serviceRoles === 'substitute'">{{ subLowestPrice }}/hour</template>
                  <template v-else> USD {{ lowestPrice }} </template>
                </template>
              </template>
            </div>
          </div>
          <template v-if="!isSysView">
            <q-banner inline-actions rounded class="bg-amber-2 text-black q-my-md" v-if="!bannerClosed && bannerMessage?.message">
              {{ bannerMessage.message }}
              <template v-slot:action>
                <q-btn flat rounded icon="close" class="q-pa-sm" @click="bannerClosed = true" />
              </template>
            </q-banner>
          </template>
          <div class="q-my-md">
            <q-img
              spinner-color="white"
              fit="cover"
              class="fit rounded-borders-md"
              :ratio="16 / 5"
              :src="hashToUrl(packageData?.cover) || '/v2/img/avatar.png'">
            </q-img>
          </div>
          <div class="text-negative" v-if="bannerMessage?.approved">Status: Approved</div>
          <template v-if="!isSysView">
            <div v-if="isContentOrientatedEnableAndFeatured">
              <template v-if="!readonly">
                <div class="row justify-between q-py-sm" v-if="!isAdmin && (userEnrollStatus || packageBought)">
                  <div class="text-negative" v-if="userEnrollStatus">Status: {{ userEnrollStatus }}</div>
                  <div class="text-negative hidden" v-if="packageBought">Purchased</div>
                </div>
                <!--
              <div class="row q-gutter-md" v-if="!(isForPersonal && packageBought)">
              -->
                <div class="row q-gutter-md">
                  <q-btn
                    v-for="(action, index) in computedActions"
                    :key="index"
                    @click="action.fn"
                    class="col"
                    color="primary"
                    :disable="disableAllActions"
                    rounded
                    no-caps
                    :label="action.label"></q-btn>
                  <q-btn
                    v-if="isAdmin || !applyActions"
                    @click="toApply"
                    class="col"
                    color="primary"
                    :disable="disableAllActions"
                    rounded
                    no-caps
                    :label="isAdmin ? 'Share to apply' : 'Apply'"></q-btn>
                </div>
                <template v-if="showCountdown">
                  <div class="text-center q-mt-md" v-if="deadline">
                    <CommonCountdown
                      v-if="!expired && new Date(deadline) > new Date()"
                      @expired="onExpired"
                      :deadline="deadline"
                      :prepend="applicationApproved || interviewIsInvited ? 'Enrollment close on' : 'Application close on'"
                      ticking></CommonCountdown>
                    <div v-else>{{ applicationApproved || interviewIsInvited ? 'Enrollment' : 'Application' }} expired</div>
                  </div>
                  <div class="text-center q-mt-md" v-else-if="interviewPurchaseExpired">Enrollment expired</div>
                </template>
              </template>
            </div>
            <!--
            <template v-if="packageData.serviceRoles === 'substitute'">
              <div class="text-negative q-mt-md">{{ packageData.isOnCampus ? 'On campus' : 'Online' }}</div>
              <div class="row items-center" v-if="packageData?.isOnCampus">
                <div class="text-weight-medium">Service location</div>
                <div class="text-primary q-ml-md">
                  {{ common.getCountryName(packageData?.country) }}
                </div>
                <div>
                  <q-chip
                    v-for="v in packageData.onCampusPrice"
                    :key="v?.city"
                    class="text-weight-medium"
                    :ripple="false"
                    size="12px"
                    color="teal-1"
                    text-color="primary"
                    :label="v?.city">
                  </q-chip>
                </div>
              </div>
            </template>
            -->

            <div v-if="!readonly && isContentOrientatedEnableAndPurchased && isAdmin">
              <div class="row q-gutter-md">
                <q-btn @click="onCheckVouchersClick" class="col" color="primary" rounded no-caps label="Check vouchers"></q-btn>
              </div>
            </div>
          </template>
          <div class="q-my-md">
            <div class="text-subtitle1">The current service package can provide service for the following subjects</div>
            <q-card class="q-mt-md q-py-md rounded-borders-md">
              <q-card-section class="q-py-none">
                <PackageChips :pack="packageData" :is-educator="!showBook"></PackageChips>
              </q-card-section>
            </q-card>
          </div>
          <PackagePacket
            v-if="!(isSubOnCampusAndPurchased || isSubAndFeatured)"
            :packageData="packageData"
            :duration-only="isContentOrientatedEnableAndPurchased"
            :userPackageData="userPackageData"></PackagePacket>
          <q-card class="rounded-borders-md q-my-md">
            <q-card-section class="text-subtitle1"> Value of the service package </q-card-section>
            <q-card-section class="q-pt-none">
              <div class="q-my-sm text-body2" v-for="(point, i) in packageData?.points" :key="i">
                {{ point }}
              </div>
            </q-card-section>
          </q-card>
          <q-card class="rounded-borders-md q-my-md" v-if="packageData?.attachments?.length">
            <q-card-section class="text-subtitle1"> Promotional material</q-card-section>
            <q-card-section class="q-pt-none">
              <PreviewLists :files="packageData.attachments"></PreviewLists>
            </q-card-section>
          </q-card>
          <template v-if="isContentOrientatedEnableAndFeatured">
            {{ console.log('fdkjfjfjfj', list) }}
            <div v-for="item in list" :key="item._id" class="q-mt-md">
              <template v-if="item?.type === 'lecture'">
                <div class="text-primary text-weight-medium q-pb-sm">
                  {{ sysMap[item.premium?.subject]?.name }}
                </div>
                <ServicePrePackage
                  @updateData="onLectureUpdate($event, item)"
                  :mentorPrice="!allPricesAreHidden"
                  :lecturePrice="!allPricesAreHidden"
                  nonumber
                  :item="item"
                  :discount="totalDiscount" />
              </template>
              <template v-if="item?.type === 'carer'">
                <CarerPackage @updateData="onCarerUpdate($event, item)" :carerPrice="!allPricesAreHidden" :item="item" :discount="totalDiscount" />
              </template>
            </div>
            <div class="row justify-end q-py-md" v-if="!discountEnded && packageData?.discountConfig?.end && !schoolPriceData._id">
              <div class="text-light-blue-6">
                Premium lecture discount ends in
                <CountDown :deadTime="packageData.discountConfig.end" @end="onDiscountEnd" />
              </div>
            </div>
            <div class="rounded-borders-md q-my-md">
              <q-card-section>
                <q-item dense class="q-pa-none">
                  <q-item-section>
                    <q-item-label>No. of service</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-item-label>
                      {{ contentOrientatedEnableNoOfService }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-card-section>
            </div>
            <div class="q-py-md">
              <q-btn class="fit" color="primary" flat rounded no-caps label="Terms and policy" @click="policyClick()"></q-btn>
            </div>
          </template>
          <template v-else-if="isContentOrientatedEnableAndPurchased">
            <q-list v-for="item in list" :key="item._id" class="q-mt-md">
              <template v-if="item.premium">
                <q-expansion-item default-opened>
                  <template v-slot:header>
                    <div class="row full-width">
                      <div class="col-11">
                        <PackageCard
                          :onItemClick="onPremiumContentClick"
                          shadow
                          detail
                          :payMethod="freePayMethods[item.premium._id] ? 'free' : 'cash'"
                          :service-no="item.times"
                          :is-educator="false"
                          tag="Premium lecture"
                          :pack="item.premium.snapshot"
                          :pack-user="item.premium" />
                      </div>
                    </div>
                  </template>
                  <div class="q-pt-md">
                    <PackagePacket :packageData="item.premium.snapshot" :userPackageData="item.premium"></PackagePacket>
                  </div>
                  <q-btn v-if="!isSysView" class="fit q-mt-md" color="primary" outline rounded no-caps @click="toBook(item)" label="Book extra session"></q-btn>
                  <!-- <q-btn
                  v-if="!isSysView"
                  @click="onScheduleClick(item)"
                  class="fit q-mt-md hidden"
                  color="primary"
                  outline
                  rounded
                  no-caps
                  label="Schedule in batches"></q-btn> -->
                  <q-btn @click="onPurchaseClick(item)" class="fit q-mt-md" color="primary" label="Purchase more" outline rounded no-caps />
                  <q-btn
                    v-if="!isSysView"
                    class="fit q-mt-md"
                    color="primary"
                    outline
                    rounded
                    no-caps
                    label="History"
                    :to="`/order/servicePack/${item.premium._id}`"></q-btn>
                  <q-btn
                    v-if="!isSysView && isAdmin"
                    class="fit q-mt-md"
                    color="primary"
                    outline
                    rounded
                    no-caps
                    label="Set participants"
                    @click="onSetParticipantsClick(item)"></q-btn>
                  <q-separator class="q-mt-md"></q-separator>
                </q-expansion-item>
              </template>
            </q-list>
            <template v-if="!isAdmin">
              <div v-for="item in list" :key="item._id" class="q-mt-md">
                <PackageCard
                  v-if="item.servicePack?._id"
                  shadow
                  :payMethod="freePayMethods[item.servicePack._id] ? 'free' : 'cash'"
                  :is-educator="false"
                  :pack="item.servicePack.snapshot"
                  :pack-user="item.servicePack"
                  :premium="item.premium?.snapshot?.unit?._id"
                  category="purchased" />
              </div>
              <div v-for="item in list" :key="item._id" class="q-mt-md">
                <PackageCard
                  v-if="item.type == 'carer'"
                  shadow
                  :is-educator="false"
                  :payMethod="freePayMethods[item] ? 'free' : 'cash'"
                  :pack="item.snapshot"
                  :pack-user="item"
                  category="purchased" />
              </div>
            </template>
          </template>
          <template v-else-if="isSubOnCampusAndPurchased">
            <div v-for="item in onCampusList" :key="item._id">
              <q-card class="rounded-borders-md q-my-md">
                <q-card class="rounded-borders-md q-my-md">
                  <q-card-section>
                    <div class="row q-col-gutter-md items-center">
                      <div class="col-xs-12 col-sm-2">
                        <q-img
                          class="full-width rounded-borders-md"
                          :ratio="$q.screen.gt.xs ? 16 / 12 : 16 / 9"
                          fit="cover"
                          :src="hashToUrl(item?.snapshot?.onCampusPrice?.find((v) => v.city === item.city)?.hash) || '/v2/img/no-img.png'" />
                      </div>

                      <div class="col-xs-12 col-sm-10">
                        <div class="text-primary text-bold">{{ item.city }}</div>
                        <div class="row q-mt-sm">
                          <div class="col-4">No of minutes</div>
                          <div class="col-8">{{ item.total - item.used }} / {{ item.total }} mins available</div>
                        </div>
                        <div class="row q-mt-sm">
                          <div class="col-4">Purchased on</div>
                          <div class="col-8">{{ date.formatDate(item.createdAt, 'DD/MM/YYYY') }}</div>
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </q-card>
              <div class="q-gutter-md q-pr-md q-mb-md" v-if="!servicePackUserId">
                <q-btn v-if="item.total - item.used > 0" class="fit" color="primary" outline rounded no-caps label="Book now" @click="subBook(item)" />
                <q-btn class="fit" color="primary" outline rounded no-caps label="Location setting" @click="subLocation(item)" />
                <q-btn
                  class="fit"
                  color="primary"
                  outline
                  rounded
                  no-caps
                  label="History"
                  :to="`/order/servicePack/${item._id}?type=${packageData.serviceRoles}`" />
              </div>
            </div>
          </template>
          <template v-else>
            <q-separator v-if="!purchased"></q-separator>
            <q-card class="rounded-borders-md q-my-md" v-if="!purchased">
              <template v-if="packageData.serviceRoles === 'substitute'">
                <template v-if="packageData?.isOnCampus">
                  <q-card-section class="q-pb-none row justify-between">
                    <q-tabs
                      v-model="onCampusTab"
                      stretch
                      @update:model-value="onCampusTabChange"
                      align="left"
                      indicator-color="primary"
                      active-color="primary"
                      inline-label
                      mobile-arrows
                      shrink
                      class="col-12 q-mb-md">
                      <q-tab v-for="o in packageData?.onCampusPrice" :key="o.city" :name="o.city" :label="o.city" no-caps />
                    </q-tabs>
                    <div>Choice of package size</div>
                  </q-card-section>
                  <q-card-section class="q-mb-lg">
                    <q-item v-for="(discount, i) in onCampusPrice?.discount" :key="i" class="col row">
                      <q-item-section class="">
                        <q-item-label class="text-subtitle1"> {{ discount.count }} hours </q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-item-label class="text-secondary text-h5 text-weight-bold">
                          USD {{ (discount.count * 100 * onCampusPrice?.price) / 10000 }}
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-card-section>
                </template>
                <template v-else>
                  <q-card-section class="q-pb-none row justify-between">
                    <div>Choice of package size</div>
                  </q-card-section>
                  <q-card-section class="q-mb-lg">
                    <q-item v-for="(discount, i) in packageDiscount" :key="i" class="col row">
                      <q-item-section class="">
                        <q-item-label class="text-subtitle1"> {{ discount.count }} hours </q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-item-label class="text-secondary text-h5 text-weight-bold">
                          USD {{ (discount.count * 100 * packageData?.price) / 10000 }}
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-card-section>
                </template>
              </template>
              <template v-else>
                <q-card-section class="q-pb-none row justify-between">
                  <div>Choice of package size</div>
                  <div class="text-light-blue-6" v-if="!discountEnded && packageData?.discountConfig?.end">
                    Discount ends in
                    <CountDown :deadTime="packageData.discountConfig.end" @end="onDiscountEnd" />
                  </div>
                </q-card-section>
                <q-card-section class="q-mb-lg">
                  <q-item v-for="(discount, i) in packageDiscount" :key="i" class="col row">
                    <template v-if="discount._id === 'customize_point'">
                      <q-item-section class="">
                        <q-item-label class="text-subtitle1">
                          <q-input
                            :rules="[(val) => (val && val >= 0) || 'Value must be non-negative']"
                            style="width: 120px; display: inline-block"
                            v-model="pointPackageDiscount.count"
                            @update:modelValue="onPointPackageDiscountUpdate"
                            outlined
                            dense
                            type="number" />
                          sessions
                        </q-item-label>
                      </q-item-section>
                      <q-item-section side>
                        <q-item-label><ShowPoint :price="discount?.price" :type="categoryMap[packageData.serviceRoles]" /></q-item-label>
                      </q-item-section>
                    </template>
                    <template v-else>
                      <q-item-section class="">
                        <q-item-label class="text-subtitle1"> {{ discount.gifts ? discount.gifts + discount.count : discount.count }} sessions </q-item-label>
                        <q-item-label v-if="discount.gifts" class="flex items-center text-yellow-9 text-body2">
                          <q-icon name="redeem" class="q-pr-xs" size="xs"></q-icon>
                          {{ ` Additional ${discount.gifts} free sessions as gifts` }}
                        </q-item-label>
                      </q-item-section>
                      <q-item-section side v-if="isPointMode">
                        <q-item-label v-if="!discountEnded && packageData.discountConfig.enable">
                          <ShowPoint
                            :price="
                              (((discount.count * (100 - discount.discount) * (100 - packageData.discountConfig.discount)) / 100) * packageData?.price) / 100
                            "
                            :type="categoryMap[packageData.serviceRoles]" />
                        </q-item-label>
                        <q-item-label v-else class="text-secondary text-h5 text-weight-bold">
                          <ShowPoint
                            :price="(discount.count * (100 - discount.discount) * packageData?.price) / 100"
                            :type="categoryMap[packageData.serviceRoles]" />
                        </q-item-label>
                      </q-item-section>
                      <q-item-section side v-else>
                        <q-item-label v-if="!discountEnded && packageData.discountConfig.enable">
                          <span class="text-secondary text-h5 text-weight-bold">
                            USD
                            {{ (discount.count * (100 - discount.discount) * packageData?.price * (100 - packageData.discountConfig.discount)) / 100 / 10000 }}
                          </span>
                          <del class="q-pt-sm">USD {{ (discount.count * (100 - discount.discount) * packageData?.price) / 10000 }}</del>
                        </q-item-label>
                        <q-item-label v-else class="text-secondary text-h5 text-weight-bold">
                          USD {{ (discount.count * (100 - discount.discount) * packageData?.price) / 10000 }}
                        </q-item-label>
                      </q-item-section>
                    </template>
                  </q-item>
                </q-card-section>
              </template>
            </q-card>

            <div v-if="route.query.tab === 'purchased' && associatedTask" class="q-mb-md">
              <div class="q-mb-md text-bold text-subtitle1">Associated Task</div>
              {{ console.log('associatedTask', associatedTask, associatePackUserId) }}
              <TaskCard :isView="true" :isBuy="true" :task="associatedTask" :clickAllowed="true" :associatePackUserId="associatePackUserId" />
            </div>
            <div v-else-if="associatedTask" class="q-mb-md">
              <div class="q-mb-md text-bold text-subtitle1">Associated Task</div>
              <TaskCard :isView="true" :isBuy="true" :task="associatedTask" :clickAllowed="true" />
            </div>
            <div class="q-gutter-md q-pr-md q-mb-md" v-if="showBook && !readonly && !servicePackUserId">
              <q-btn
                v-if="(!purchased || !available) && !isSysView"
                @click="toBuy"
                :loading="buying"
                class="fit"
                color="primary"
                outline
                rounded
                no-caps
                :label="isPointMode ? 'Claim now' : 'Buy'"></q-btn>
              <q-btn v-if="purchased && available && !isSysView" color="primary" class="fit" outline rounded no-caps label="Book now" @click="toBook()"></q-btn>
              <q-btn
                v-if="purchased && !isSysView"
                class="fit"
                color="primary"
                outline
                rounded
                no-caps
                label="History"
                :to="`/order/servicePack/${userPackageData._id}?type=${packageData.serviceRoles}`"></q-btn>
              <template v-if="isPointMode">
                <q-btn
                  class="fit"
                  color="primary"
                  flat
                  rounded
                  no-caps
                  label="Points redemption policy"
                  target="_blank"
                  href="/v2/com/agreement/all_users/points_redemption" />
              </template>
              <template v-else>
                <q-btn v-if="!session && !isSysView" class="fit" color="primary" flat rounded no-caps label="Terms and policy" @click="policyClick()"></q-btn>
              </template>
            </div>
            <div v-if="session && !route.query?.back?.includes('/detail/session')" class="q-my-md">
              <q-separator></q-separator>
              <div class="col q-my-md text-center text-subtitle1">Bundled premium workshop</div>
              <SessionBoard :session="session" isRelevant />
            </div>
          </template>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
  <q-dialog v-model="policyOpen">
    <q-card>
      <q-card-section>
        <q-toolbar>
          <q-toolbar-title class="col text-center text-h6">Cancellation policy</q-toolbar-title>
        </q-toolbar>
      </q-card-section>
      <q-card-section class="col q-mx-lg text-subtitle1">
        Free cancellation of the remaining unused sessions purchased (gift sessions excluded) within 2 weeks after the purchased time.
        <q-btn class="q-pa-xs" flat color="primary" no-caps label="More" target="_blank" href="/v2/com/agreement/all_users/cancellation" />
      </q-card-section>

      <q-separator></q-separator>
      <q-card-section inset>
        <q-btn class="full-width bg-teal text-white" no-caps rounded label="I got it" @click="closePolicy()"> </q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, onMounted, inject, watch, computed, onUnmounted, openBlock} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {date} from 'quasar'
import {pubStore} from 'stores/pub'
import {pointStore} from 'stores/point'
import {GradeGroupMap, EducatorGrades} from 'src/boot/const'
import {UserEnrollStatus} from 'src/pages/premCpack/consts'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import PackagePacket from 'components/PackagePacket.vue'
import PackageChips from 'components/PackageChips.vue'
import PackageCard from 'components/PackageCard.vue'
import useSubject from 'src/composables/account/academic/useSubject'
import SessionBoard from 'components/SessionBoard.vue'
import BreadCrumbs from 'components/BreadCrumbs.vue'
import PromptPage from 'components/PromptPage.vue'
import CountDown from 'src/pages/order/CountDown.vue'
import IconPoint from 'src/pages/point/components/IconPoint.vue'
import ShowPoint from 'src/pages/point/components/ShowPoint.vue'
import useSchool from 'src/composables/common/useSchool'
import CommonCountdown from 'components/CommonCountdown.vue'
import ServicePrePackage from './ServicePrePackage.vue'
import CarerPackage from 'src/pages/order/components/CarerPackage.vue'
import AlertDialog from 'src/components/AlertDialog.vue'
import PurchaseMore from './components/PurchaseMore.vue'
import PurMoreDialog from './components/PurMoreDialog.vue'

import PreviewLists from 'src/components/PreviewLists.vue'
import {VerificationMap} from 'src/pages/teacher-verification/utils'
import {commonStore} from 'src/stores/common'
import SubLocationDialog from 'src/pages/substitute/SubLocationDialog.vue'
import SubjectDialog from 'components/SubjectDialog.vue'
import {categoryMap} from 'src/boot/const.js'
import {calServiceDiscount} from 'src/pages/sys/package/const'
import TaskCard from 'src/components/ServiceTask/TaskCard.vue'

/*
  props && emits
*/
const props = defineProps({servicePackUserId: String})
const emit = defineEmits(['load'])

/*
  consts
*/
const pub = pubStore()
const route = useRoute()
const router = useRouter()
const id = ref(props.servicePackUserId || route.params.id)
const isShowBanner = ref(route?.query?.associatedTaskStatus)

const {sysMap} = useSubject()
const {sysCurriculumMap} = useAcademicSetting()
const {isLogin, isAdmin, isStudent, isSchool, schoolId} = useSchool()

const isPointMode = ref(!!route?.query?.isPointMode)
const sharedSchool = ref(route?.query?.sharedSchool)

const pStore = pointStore()
const common = commonStore()
const unavailable = ref(false)
const payBack = ref(null)

const packageData = ref({})
const schoolPriceData = ref({})
const userPackageData = ref({})
const servicePackApply = ref({})
const applyActions = ref(null)
const freePayMethods = ref({})
const packUserIds = ref([])
const list = ref([])
const session = ref()
const purchased = ref(false)
const isSysView = ref(false)
const policyOpen = ref(false)
const loading = ref(false)
const buying = ref(false)
const shareLink = ref(null)
const discountEnded = ref(false)
const readonly = ref(route.params.tab == 'limit')
const expired = ref(false)
const applicationApproved = ref(false)
const interviewIsInvited = ref(false)
const packageBought = ref(false)
const enrollmentExpired = ref(false)
const bannerClosed = ref(false)
const updatePrice = ref(null)
const updatedItems = ref([])
const onCampusTab = ref(null)
const onCampusPrice = ref({})
const pointPackageDiscount = ref({
  count: 0,
  oriPrice: 0,
  price: 0,
  gifts: 0,
  _id: 'customize_point',
})
const onCampusList = ref([])

const substituteDetail = ref(null)
const associatedTask = ref(null)
const associatePackUserId = ref(null)

/*
  exposes
*/
defineExpose({
  buy: (back) => {
    payBack.value = back
    toBuy()
  },
})

/*
  computeds
*/

const totalDiscount = computed(() => {
  return calServiceDiscount(packageData.value?.discountConfig)
})

const showCountdown = computed(() => {
  //<!-- https://github.com/zran-nz/bug/issues/5192#issuecomment-********** -->
  //<!-- https://github.com/zran-nz/bug/issues/5192#issuecomment-********** -->
  return (
    !isAdmin.value &&
    !isViewApplication.value &&
    !(servicePackApply.value.status === 1 && (!sharedSchool.value || (sharedSchool.value && schoolPriceData.value?._id && !schoolPriceData.value.priceEnable)))
  )
})

const bannerAssociatedTask = computed(() => {
  console.log('jjj', route.query?.associatedTaskStatus)
  if (route.query?.associatedTaskStatus === '1') {
    return ' No active service providers available from the past 5 days to assign the task. Please choose and book a relevant session to proceed.'
  } else if (route.query?.associatedTaskStatus === '2') {
    return ' Book your session first. Post session, based on your learning experience, you may continue with the same service provider or choose one from the previous system-recommended list.'
  }
  return ''
})

const isViewApplication = computed(() => {
  return servicePackApply.value?._id && servicePackApply.value.status == 0 && !servicePackApply.value.interviewInvited
})

const userEnrollStatus = computed(() => {
  return UserEnrollStatus(servicePackApply.value)
})

const isB2C = computed(() => {
  return !!servicePackApply.value?.sharedSchool || !!sharedSchool.value
})

const isForPersonal = computed(() => {
  //5052#issuecomment-**********
  return !isAdmin.value && !isSchool.value && !packageData.value.backgroundCheck && !packageData.value.interviewPack?._id
})

const contentOrientatedEnableNoOfService = computed(() => {
  if (packageData.value.contentOrientatedEnable) {
    return packageData.value.contentOrientated.reduce((sum, item) => sum + item.times, 0)
  } else {
    return 0
  }
})

const isContentOrientatedEnableAndPurchased = computed(() => {
  return purchased.value && userPackageData.value.snapshot?.contentOrientatedEnable
})

const isSubOnCampusAndPurchased = computed(() => {
  return purchased.value && userPackageData.value.snapshot?.serviceRoles === 'substitute' && userPackageData.value.snapshot?.isOnCampus
})

const isSubAndFeatured = computed(() => {
  return !purchased.value && packageData.value?.serviceRoles === 'substitute'
})

const isSubPurchased = computed(() => {
  return purchased.value && userPackageData.value.snapshot?.serviceRoles === 'substitute'
})

const isContentOrientatedEnableAndFeatured = computed(() => {
  return !purchased.value && packageData.value.contentOrientatedEnable
})

const bannerMessage = computed(() => {
  let message = ''
  let approved = false
  if (isContentOrientatedEnableAndFeatured.value && applicationNotApprovedAndInterviewInvited.value) {
    //https://github.com/zran-nz/bug/issues/5052#issuecomment-2337497742
    //https://github.com/zran-nz/bug/issues/5192#issuecomment-2335022351
    message = 'We suggest to complete the interview within 3 days, otherwise it may result in enrolment failure.'
  }
  if (userPackageData.value?.snapshot) {
    const servicePackSnapshot = userPackageData.value.snapshot
    const has1V1Mentor = Array.isArray(servicePackSnapshot.contentOrientated) && servicePackSnapshot?.contentOrientated?.some((e) => e.servicePack)
    const hasCarerPack = servicePackSnapshot?.carerPack?._id
    const isInterviewPack = ['interview', 'interviewTeacher'].includes(servicePackSnapshot?.consultant?.type)
    if (isContentOrientatedEnableAndPurchased.value) {
      if (has1V1Mentor && hasCarerPack) {
        message = isAdmin.value
          ? 'You have successfully purchased the Premium lecture, 1v1 mentor and Carer service. You can go to the voucher management by clicking on check vouchers voucher button to allocate the vouchers to your applicants.'
          : 'You have successfully purchased the Premium content letcure, 1v1 Mentor, Carer service. You can use the 1v1 Mentor to assist you with your lecure study, and book a carer secvice to provide you with lecure study progress and advices.'
      } else if (has1V1Mentor) {
        message = isAdmin.value
          ? 'You have successfully purchased the Premium lecture and 1v1 mentor. You can go to the voucher management by clicking on check vouchers button to allocate the vouchers to your applicants.'
          : 'You have successfully purchased the Premium content letcure and 1v1 Mentor. You can use the 1v1 Mentor to assist you with your lecure study.'
        if (!isAdmin.value) {
          approved = true
        }
      } else if (hasCarerPack) {
        message = isAdmin.value
          ? 'You have successfully purchased the Premium lecture and Carer service. You can go to the voucher management by clicking on check vouchers button to allocate the vouchers to your applicants.'
          : 'You have successfully purchased the Premium content letcure and Carer service. You can book a carer secvice to provide you with lecure study progress and advices.'
      }
    } else if (isInterviewPack && userPackageData.value.total > userPackageData.value.used) {
      //https://github.com/zran-nz/bug/issues/5052#issuecomment-2364952656
      message = 'Please choose a suitable time and book a teacher for your interview'
    }
  }

  return {message, approved}
})

const computedActions = computed(() => {
  if (isAdmin.value && schoolId.value && schoolPriceData.value?._id) {
    return [{label: 'Buy', fn: toBuy}]
  } else if (!isAdmin.value && applyActions.value) {
    return applyActions.value
  }

  return null
})

const allPricesAreHidden = computed(() => {
  return isB2C.value && (!schoolPriceData.value.priceEnable || (schoolPriceData.value.priceEnable && !applyActions.value))
})

const interviewPurchaseExpired = computed(() => {
  return servicePackApply.value?.interviewPurchaseExpired
})

const applicationNotApprovedAndInterviewInvited = computed(() => {
  return servicePackApply.value?.status == 0 && servicePackApply.value?.interviewInvited
})

const disableAllActions = computed(() => {
  return (expired.value || new Date(deadline.value) < new Date()) && !isAdmin.value && (isB2C.value || interviewIsInvited.value)
})

const deadline = computed(() => {
  //若从管理员分享链接进入（通过B端分销给C端）的详情页，最上方显示管理员所设置的报名倒计时】
  //https://github.com/zran-nz/bug/issues/4616
  //https://github.com/zran-nz/bug/issues/5192#issuecomment-2350925949
  return applicationApproved.value
    ? servicePackApply.value.purchaseExpireAt
    : applicationNotApprovedAndInterviewInvited.value
      ? servicePackApply.value.interviewPurchaseExpireAt
      : schoolPriceData.value?.deadline
})

const packageDiscount = computed(() => {
  return isPointMode.value ? [...(packageData.value?.discount || []), pointPackageDiscount.value] : packageData.value?.discount || []
})

const showBook = computed(() => {
  const queryBack = route.query?.back
  return (
    !queryBack?.includes('/detail/session') ||
    (queryBack?.includes('/detail/session') && (queryBack?.includes('/study/purchased') || queryBack?.includes('/home/<USER>')))
  )
})

const available = computed(() => {
  if (isSubPurchased.value) {
    return userPackageData.value.total > 0
  } else {
    return userPackageData.value.total > userPackageData.value.used
  }
})

const lowestPrice = computed(() => {
  if (packageData.value?.contentOrientatedEnable) {
    //https://github.com/zran-nz/bug/issues/5297#issuecomment-2373603286
    const price = updatePrice.value
      ? updatePrice.value
      : isAdmin.value
        ? packageData.value.contentOrientatedConfig?.schoolPrice
        : packageData.value.contentOrientatedConfig?.price
    return ((price || 0) / 100).toFixed(2)
  }

  if (packageData.value?.discount?.length) {
    let fromPrice = 0
    let packagePrice
    packageData.value?.discount.map((discount) => {
      packagePrice = (discount.count * (100 - (packageData.value.discountConfig?.enable && !discountEnded.value ? discount.discount : 0))) / 100
      if (fromPrice === 0 || packagePrice <= fromPrice) {
        fromPrice = packagePrice
      }
    })
    return ((fromPrice * packageData.value?.price) / 100).toFixed(2)
  }
  return 0
})

const subLowestPrice = computed(() => {
  if (packageData.value?.serviceRoles === 'substitute') {
    if (packageData.value?.isOnCampus) {
      return (onCampusPrice.value?.price / 100).toFixed(2)
    } else {
      return (packageData.value?.price / 100).toFixed(2)
    }
  }
  return 0
})

const groupGroupDisplay = computed(() => {
  let gradeGroup = {}
  if (packageData.value?.type === 'mentoring' && packageData.value?.mentoringType === 'teacherTraining') {
    gradeGroup = {
      label: 'Teacher training',
      grades: packageData.value?.gradeGroup,
    }
  } else {
    Object.keys(GradeGroupMap).map((grade) => {
      if (grade === packageData.value?.gradeGroup?.[0]) {
        gradeGroup.label = GradeGroupMap[grade].label
        let grades = GradeGroupMap[grade].grades[packageData.value?.curriculum]
        if (grades?.length) {
          gradeGroup.grades = grades
        } else {
          gradeGroup.grades = GradeGroupMap[grade].grades.classcipe
        }
      }
    })
  }
  return gradeGroup
})

/*
  watches
*/

watch(
  () => id.value,
  () => {
    window.location.reload()
  }
)

/*
  methods
*/
const gradeOptions = (snapshot) => {
  const mentoringType = snapshot?.mentoringType
  if (['teacherTraining', 'teacherTrainingSubject'].includes(mentoringType)) {
    return EducatorGrades.map((value) => {
      return {label: value, value: value}
    }).filter((e) => snapshot?.gradeGroup?.includes(e.value))
  } else {
    if ((mentoringType == 'academic' && snapshot.curriculum) || mentoringType !== 'academic') {
      return Object.entries(GradeGroupMap)
        .map(([key, value]) => {
          value.key = key
          return value
        })
        .filter((e) => snapshot?.gradeGroup?.includes(e.key))
    } else {
      return false
    }
  }
}

const goBack = () => {
  if (route.query.back) {
    router.replace(route.query.back)
  } else {
    router.go(-1)
  }
}

const onLectureUpdate = (e, item) => {
  if (!updatedItems.value.includes(item._id)) {
    updatedItems.value.push(item._id)
    updatePrice.value += (e?.lecture?.price || 0) + (e?.mentoring?.price || 0)
  }
}

const onCarerUpdate = (e, item) => {
  if (!updatedItems.value.includes(item._id)) {
    updatedItems.value.push(item._id)
    updatePrice.value += e?.carer?.price || 0
  }
}

const onScheduleClick = (item) => {
  console.log('<===============onScheduleClick')
}

const onPurchaseClick = async (item) => {
  console.log('<===============onPurchaseClick', item)
  $q.loading.show()
  const orderCheck = await App.service('order').get('checkLinks', {
    query: {
      links: [{id: item?.premium?.premium, style: 'service_premium'}],
      servicePremium: packageData.value?._id,
    },
  })
  $q.loading.hide()
  setTimeout(() => {
    $q.loading.hide()
  }, 3000)
  if (orderCheck?.unpaidOrderId?.length > 0) {
    router.push({path: `/order/detail/${orderCheck?.unpaidOrderId?.[0]}`, query: {back: route.query.back}})
    return
  }

  $q.dialog({
    component: PurMoreDialog,
    componentProps: {
      packUserId: item?.premium?._id,
      servicePremium: packageData.value,
      lecture: item?.premium,
    },
  })
}

const onCheckVouchersClick = () => {
  router.push({path: `/premcpack/applicationTrackVoucher/${packageData.value._id}`, query: {back: route.fullPath}})
}

const onPremiumContentClick = (item, main, times) => {
  router.push({path: `/detail/content/limit/${item.unit._id}`, query: {spuid: main._id, times, back: route.fullPath}})
}

const onEnrollmentExpired = () => {
  enrollmentExpired.value = true
  if (applicationNotApprovedAndInterviewInvited.value) {
    location.reload()
  }
}

const onExpired = () => {
  expired.value = true
  if (applicationNotApprovedAndInterviewInvited.value) {
    location.reload()
  }
}

const validate = () => {
  if (!isLogin.value) {
    router.push({path: '/login', query: {back: location.pathname + location.search}})
    return false
  }
  let authorized = false
  const role = packageData.value.serviceRoles
  const serviceType = packageData.value.mentoringType
  const consultantType = packageData.value.consultant?.type
  const isForEducators = ['teacherTraining', 'teacherTrainingSubject'].includes(serviceType)

  //~https://github.com/zran-nz/bug/issues/4968#issuecomment-2205167481~
  //~https://github.com/zran-nz/bug/issues/5126#issuecomment-2285227528~
  //https://github.com/zran-nz/bug/issues/5126#issuecomment-2339862361
  //https://github.com/zran-nz/bug/issues/5342
  if (
    (role == 'mentoring' &&
      (isAdmin.value ||
        (!isForEducators &&
          isStudent.value &&
          (!sharedSchool.value ||
            (isContentOrientatedEnableAndFeatured.value &&
              sharedSchool.value &&
              (!schoolPriceData.value?.withinSchool || (schoolPriceData.value?.withinSchool && schoolPriceData.value?.students?.includes(pub.user._id)))))) ||
        (isForEducators &&
          !isStudent.value &&
          (!sharedSchool.value ||
            (isContentOrientatedEnableAndFeatured.value &&
              sharedSchool.value &&
              (!schoolPriceData.value?.withinSchool || (schoolPriceData.value?.withinSchool && schoolPriceData.value?.teachers?.includes(pub.user._id)))))))) ||
    (role == 'correcting' &&
      ((!isStudent.value && !isSchool.value && ['academic', 'teacherTraining', 'teacherTrainingSubject', 'steam', 'essay'].includes(serviceType)) ||
        (isAdmin.value && ['academic'].includes(serviceType)))) ||
    (role == 'substitute' &&
      ['academic', 'essay', 'overseasStudy', 'teacherTraining', 'teacherTrainingSubject', 'steam'].includes(serviceType) &&
      (isStudent.value || isAdmin.value)) ||
    (role == 'consultant' &&
      ((!['teacherTraining', 'teacherTrainingSubject'].includes(serviceType) && consultantType == 'interview' && isStudent.value) ||
        (consultantType == 'interviewTeacher' && !isStudent.value)))
  ) {
    authorized = true
  }

  console.log(pub.user._id, role, serviceType, authorized, '<======================')
  if (!authorized) {
    $q.dialog({
      component: PromptPage,
      componentProps: {
        type: 'unauthorized',
        title: 'You have no access to the page',
      },
    })
    return false
  } else {
    return true
  }
}

const onPointPackageDiscountUpdate = (choiceCount) => {
  if (choiceCount >= 0) {
    const itemList = (packageData.value?.discount || []).sort((a, b) => a.count - b.count)

    const nowDate = new Date()
    const endDate = new Date(packageData.value?.discountConfig?.end || nowDate.getTime() + 200)
    const diff = endDate.getTime() - nowDate.getTime()

    const hasDiscount = packageData.value?.discountConfig?.enable && diff > 0
    const isDiscount = packageData.value?.discountConfig?.enable && packageData.value?.discountConfig?.discount
    const price = hasDiscount ? (packageData.value?.price * (100 - packageData.value?.discountConfig?.discount)) / 100 : packageData.value?.price || 0
    let choiceItem = {}
    if (choiceCount < itemList[0].count) {
      choiceItem = {
        count: +choiceCount,
        discount: 0,
      }
    } else {
      for (let i = 0; i < itemList.length; i++) {
        if (choiceCount <= itemList[i].count) {
          choiceItem = itemList[i]
          break
        } else if (choiceCount > itemList[itemList.length - 1].count) {
          choiceItem = itemList[itemList.length - 1]
        }
      }
    }

    pointPackageDiscount.value.price = (choiceCount * (100 - choiceItem.discount) * price) / 100
  }
}

function policyClick() {
  policyOpen.value = true
}

function closePolicy() {
  policyOpen.value = false
}

const subBook = async (item) => {
  console.log('item', item)

  if (!route.query.subId) {
    $q.dialog({
      component: AlertDialog,
      componentProps: {
        title: 'Please go to account_substitute teacher tracking page and the substitube service package can only be used upon approval of substitute request.',
        confirmText: 'Go to substitute teacher tracking',
      },
    }).onOk(() => {
      router.push({
        path: '/substitute/track',
        query: {
          back: encodeURIComponent(route.fullPath),
        },
      })
    })
    return
  }

  let duration = date.getDateDiff(new Date(substituteDetail.value.end), new Date(substituteDetail.value.start), 'minutes')
  if (isSubOnCampusAndPurchased.value) {
    $q.loading.show()
    const res = await App.service('campus-location').find({
      query: {
        archive: false,
        city: item.city,
        country: item.country,
      },
    })
    $q.loading.hide()
    if (res?.total > 0) {
      const data = res.data[0]
      duration += data?.compensationHour * 60
    } else {
      $q.notify({
        type: 'negative',
        message: `No Compensation time  in ${item.city}, ${item.country}`,
      })
      return
    }
  }

  let available = item?.total - item?.used

  console.log('duration', duration, available)
  if (duration > available) {
    $q.dialog({
      title: 'Confirm',
      message: `Your current balance is insufficient, please buy again.`,
      cancel: true,
    }).onOk(async () => {
      router.push({
        path: `/order/confirm/service_substitute/${item?.snapshot._id}`,
        query: {
          back: route.query.back,
          payBack: encodeURIComponent(route.fullPath),
          city: item.city,
        },
      })
    })
    return
  }

  if (isSubOnCampusAndPurchased.value && !item?.place_id) {
    subLocation(item, () => {
      jumpBook(item)
    })
  } else {
    jumpBook(item)
  }

  console.log('<===============substituteBook', item)
}

const jumpBook = (item) => {
  router.push({
    path: `/substitute/book/${route.query.subId}`,
    query: {
      packUser: item._id,
    },
  })
}

const subLocation = (item, cb) => {
  console.log('<===============subLocation', item)
  $q.dialog({
    component: SubLocationDialog,
    componentProps: {
      country: item.country,
      place_id: item.place_id,
      address: item.address,
    },
  }).onOk(async ({address, place_id}) => {
    await App.service('service-pack-user')
      .patch('location', {
        _id: item._id,
        place_id,
        address,
      })
      .then(() => {
        getOnCampusUser()
        if (cb) {
          cb()
        }
      })
  })
}

const onBookExtraSessionClick = (item) => {
  //:to="`/booking/leave-a-message?packId=${item.premium._id}&pid=${id}`"
}

const toBook = async (item) => {
  if (userPackageData.value.snapshot?.serviceRoles === 'substitute') {
    subBook(userPackageData.value)
    return
  }

  if (route.query.topic) {
    getServiceConf(item, route.query.topic)
  } else if (Array.isArray(packageData.value?.topic) && packageData.value?.topic?.length > 1 && !item) {
    $q.dialog({
      component: SubjectDialog,
      componentProps: {
        pack: packageData.value,
      },
    }).onOk(async (topic) => {
      getServiceConf(item, topic)
    })
  } else {
    getServiceConf(item)
  }
}

const getServiceConf = async (item, topic) => {
  const grades = gradeOptions(item?.premium?.snapshot || packageData.value)
  $q.loading.show()
  const query = {packUserId: item?.premium?._id || userPackageData.value._id, gradeGroup: grades.map((e) => e.key || e.value)}
  if (topic) {
    query.topic = [topic]
  }
  await App.service('service-conf')
    .get('teachersByPack', {query})
    .then((res) => {
      if (res?.data?.length) {
        doBook(item, topic)
      } else {
        $q.dialog({
          title: 'Message',
          message: 'Classcipe currently has no available teachers, please be patient ......',
          ok: {
            label: 'I got it ',
            color: 'primary',
            rounded: true,
            'no-caps': true,
          },
        })
      }
    })
  $q.loading.hide()
}

const doBook = (item, topic) => {
  /*
  //https://github.com/zran-nz/bug/issues/5584
  if (userPackageData.value.session || route.query.premium) {
    router.push({
      path: `/booking/choose/${route.query.premium || userPackageData.value._id}`,
      query: {pid: route.query.premium ? userPackageData.value._id : ''},
    })
  } else { }
  */
  let path = `/booking/leave-a-message`
  let query = {packId: userPackageData.value._id}
  if (userPackageData.value.session) query.bindingCourseId = userPackageData.value.session._id
  if (route.query.bindingCourseId) query.bindingCourseId = route.query.bindingCourseId

  if (item) {
    query.packId = item.premium._id
    query.pid = id.value
  } else {
    if (['interview', 'interviewTeacher'].includes(packageData.value.consultant?.type)) {
      path += '/interview'
      query.interviewId = userPackageData.value.snapshot._id
      if (packageData.value.consultant.type == 'interviewTeacher') {
        query.teacher = 1
      }
    }
  }
  if (topic) {
    query.topic = topic
  }
  router.push({
    path,
    query,
  })
}

function onDiscountEnd() {
  discountEnded.value = true
}

const toApply = async () => {
  if (!validate()) {
    return
  }

  const {backgroundCheck, interviewPack} = packageData.value

  // toOrder
  // if (!sharedSchool.value && !backgroundCheck && !interviewPack) {
  //   $q.dialog({
  //     component: AlertDialog,
  //     componentProps: {
  //       title: 'The current product/service do not need to apply for review and can be bought directly.',
  //       isPersistent: true,
  //     },
  //   }).onOk(async () => {
  //     $q.loading.show()
  //     await App.service('service-pack-apply')
  //       .create({
  //         uid: pub?.user?._id,
  //         servicePack: packageData.value._id,
  //         status: 1,
  //         name: pub?.user?.name,
  //       })
  //       .then(() => {
  //         orderCheck(false, id.value, () => {
  //           router.push({
  //             path: `/order/confirm/service_premium/${id.value}`,
  //           })
  //         })
  //       })
  //       .catch((err) => {
  //         $q.notify({type: 'negative', message: err?.message})
  //       })

  //     setTimeout(() => {
  //       $q.loading.hide()
  //     }, 3000)
  //   })
  //   return
  // }

  let path = `/premcpack/enroll/${packageData.value._id}`
  if (isAdmin.value) {
    path = `/premcpack/schoolPrice/${packageData.value._id}`
  }

  router.push({
    path,
    query: {
      back: route.path,
      inviteCode: route?.query?.inviteCode,
      isPointMode: route?.query?.isPointMode,
      sharedSchool: sharedSchool.value,
      inviteSource: route?.query?.inviteSource,
      inviteSourceId: route?.query?.inviteSourceId,
      schoolInviter: route?.query?.schoolInviter,
    },
  })
}

const toOffer = async () => {
  if (!validate()) {
    return
  }
  console.log('<==============route to offer')
}

const toApplication = async () => {
  if (!validate()) {
    return
  }
  const query = {
    back: route.path,
    sharedSchool: sharedSchool.value,
  }
  router.push({
    path: `/premcpack/enroll/${id.value}`,
    query,
  })
}

const toBuyInterview = async () => {
  const query = {
    back: route.query.back,
    servicePackApply: servicePackApply.value._id,
    inviteCode: route?.query?.inviteCode,
    isPointMode: route?.query?.isPointMode,
    inviteSource: route?.query?.inviteSource,
    inviteSourceId: route?.query?.inviteSourceId,
    schoolInviter: route?.query?.schoolInviter,
  }

  orderCheck(false, packageData.value.interviewPack._id, () => {
    router.push({
      path: `/order/confirm/service/${packageData.value.interviewPack._id}`,
      query,
    })
  })
}

const toBuy = async () => {
  if (!validate()) {
    return
  }
  let ChoiceSession = undefined
  if (route?.query?.isPointMode && pointPackageDiscount.value?.count > 0) {
    ChoiceSession = pointPackageDiscount.value?.count
  }
  if (userPackageData.value?._id && !available.value) {
    buying.value = true
    const res = await getServicePack(packageData.value._id)
    if (res?.status) {
      orderCheck(false, packageData.value._id, () => {
        const serviceType = res?.serviceRoles === 'substitute' ? 'service_substitute' : 'service'
        const query = {
          back: route.query.back,
          inviteCode: route?.query?.inviteCode,
          isPointMode: route?.query?.isPointMode,
          ChoiceSession,
          inviteSource: route?.query?.inviteSource,
          inviteSourceId: route?.query?.inviteSourceId,
          schoolInviter: route?.query?.schoolInviter,
        }
        if (payBack.value) {
          query.payBack = encodeURIComponent(payBack.value)
        }
        router.push({
          path: `/order/confirm/${serviceType}/${packageData.value._id}`,
          query,
        })
      })
    } else {
      buying.value = false
      $q.dialog({
        component: PromptPage,
        componentProps: {
          fullscreen: false,
          logo: false,
          title: 'The product doese not exist',
        },
      })
    }
  } else {
    const query = {
      back: route.query.back,
      inviteCode: route?.query?.inviteCode,
      isPointMode: route?.query?.isPointMode,
      inviteSource: route?.query?.inviteSource,
      inviteSourceId: route?.query?.inviteSourceId,
      schoolInviter: route?.query?.schoolInviter,
      // city: onCampusTab.value ? onCampusTab.value : undefined,
    }
    if (packageData.value.contentOrientatedEnable) {
      if (sharedSchool.value) {
        query.sharedSchool = sharedSchool.value
      }
    } else {
      query.ChoiceSession = ChoiceSession
    }

    const serviceType = packageData.value?.serviceRoles === 'substitute' ? 'service_substitute' : 'service'

    orderCheck(packageData.value.contentOrientatedEnable, id.value, () => {
      router.push({
        path: `/order/confirm/${packageData.value.contentOrientatedEnable ? 'service_premium' : serviceType}/${id.value}`,
        query,
      })
    })
  }
}

const orderCheck = async (isPremium, goodsId, cb) => {
  const checkQuery = isPremium
    ? {
        servicePremium: goodsId,
        sharedSchool: sharedSchool.value,
        links: [],
      }
    : {
        links: [{id: goodsId, style: 'service'}],
      }
  console.log('dkkfkfkkfk', checkQuery)
  $q.loading.show()
  const orderCheck = await App.service('order').get('checkLinks', {
    query: checkQuery,
  })
  $q.loading.hide()
  if (orderCheck?.unpaidOrderId?.length > 0) {
    router.push({path: `/order/detail/${orderCheck?.unpaidOrderId?.[0]}`, query: {back: route.query.back}})
    return
  }
  if (cb) {
    cb()
  }

  setTimeout(() => {
    $q.loading.hide()
  }, 3000)
}

const getServicePack = async (packId) => {
  const res = await App.service('service-pack').get(packId || id.value || route.query.id)

  if (packId) {
    return res
  } else {
    packageData.value = res ?? {}
    if (packageData.value.discountConfig?.enable && packageData.value.discountConfig?.end && new Date(packageData.value.discountConfig.end) <= new Date()) {
      discountEnded.value = true
    }
  }

  if (res?.isOnCampus && res?.serviceRoles === 'substitute') {
    const tab = res?.onCampusPrice?.[0]?.city
    onCampusTab.value = tab
    onCampusTabChange(tab)
  }
}

const getServicePackUser = async () => {
  try {
    const rs = await App.service('service-pack-user').get(id.value)
    userPackageData.value = rs
    packageData.value = rs.snapshot
    purchased.value = true
    if (rs.session) {
      try {
        session.value = await App.service('session').get(rs.session._id || rs.session)
      } catch (e) {
        console.warn(e)
      }
    }
  } catch (e) {
    unavailable.value = true
    $q.dialog({
      component: PromptPage,
      componentProps: {
        fullscreen: false,
        logo: false,
        title: 'The product doese not exist',
      },
    })
  }
}

const getSchoolPriceData = async () => {
  const rs = await App.service('service-pack-school-price').find({
    query: {
      school: sharedSchool.value ? sharedSchool.value : isAdmin.value && schoolId.value ? schoolId.value : null,
      servicePack: id.value,
    },
  })
  if (rs?.data?.[0]) {
    schoolPriceData.value = rs.data[0]
    //console.log(schoolPriceData.value, '<===============schoolPriceData')
  }
}

const getBoughtStatus = async () => {
  const resCheck = await App.service('order').get('checkLinks', {
    query: {
      links: [],
      servicePremium: id.value,
      sharedSchool: sharedSchool.value,
    },
  })
  if (resCheck?.orderId?.length) {
    packageBought.value = true
  }
}

const getApplyStatus = async () => {
  const rs = await App.service('service-pack-apply').find({
    query: {
      uid: pub.user?._id,
      sharedSchool: sharedSchool.value ? sharedSchool.value : null,
      servicePack: id.value,
    },
  })
  if (rs?.data?.[0]) {
    //https://github.com/zran-nz/bug/issues/5192#issuecomment-2350925949
    servicePackApply.value = rs.data[0]
    //console.log(servicePackApply.value, '<==============servicePackApply')
    const {status, interviewInvited} = rs.data[0]
    let actions = null
    if (status === 0) {
      // not approve
      if (interviewInvited) {
        actions = [{label: 'Buy interview package', fn: toBuyInterview}]
        interviewIsInvited.value = true
      } else {
        actions = [{label: 'View application', fn: toApplication}]
      }
    } else if (status === 1) {
      // approved
      applicationApproved.value = true
      actions = [{label: 'View offer', fn: toOffer}]
      if (!isB2C.value || (isB2C.value && schoolPriceData.value.priceEnable)) {
        actions.push({label: 'Buy', fn: toBuy})
      }
    } else if (status === -1) {
      // rejected
      actions = [{label: 'View application', fn: toApplication}]
    }
    applyActions.value = actions
  }
}

const getPayMethods = async () => {
  const ids = []
  if (packageData.value.contentOrientatedEnable) {
    ids.push(...packUserIds.value)
  } else {
    ids.push(id.value)
  }

  await App.service('service-pack-user')
    .get('checkFree', {query: {ids}})
    .then((res) => {
      if (res?.res) {
        freePayMethods.value = res.res
      }
    })
}

const getContentOrientated = async () => {
  const data = packageData.value
  const premiumIds = data?.contentOrientated.map((e) => e.premium)
  const servicePackIds = data?.contentOrientated.map((e) => e.servicePack)

  if (premiumIds?.length) {
    let rs
    if (purchased.value) {
      const query = {pid: id.value}
      if (isAdmin.value) {
        query.$isSchool = true
      }
      rs = await App.service('service-pack-user').find({query})
      if (rs?.data?.length) {
        packUserIds.value.push(...rs.data.map((e) => e._id))
      }
    } else {
      rs = await App.service('service-auth').get('unit', {query: {_id: {$in: premiumIds}}})
    }

    rs?.data?.map((f) => {
      const content = data.contentOrientated.find((e) => (purchased.value && f.premium == e.premium) || (!purchased.value && f._id == e.premium))
      const item = {
        ...content,
        ...{
          premium: f,
        },
      }

      if (purchased.value) {
        item.premium.snapshot = {...item.premium.snapshot, ...{duration: userPackageData.value.snapshot.duration, break: userPackageData.value.snapshot.break}}
      }
      list.value.push(item)
    })
  }

  if (servicePackIds?.length) {
    let rs
    if (purchased.value) {
      rs = await App.service('service-pack-user').find({query: {uid: pub.user._id, 'snapshot._id': {$in: servicePackIds}}})
      if (rs?.data?.length) {
        packUserIds.value.push(...rs.data.map((e) => e._id))
      }
    } else {
      rs = await App.service('service-pack').find({query: {_id: {$in: servicePackIds}}})
    }
    list.value.map((e) => {
      const pack = rs.data?.find((f) => (purchased.value && f.snapshot._id == e.servicePack) || (!purchased.value && f._id == e.servicePack))
      if (!(purchased.value && list.value.some((f) => f.servicePack?._id == pack?._id))) {
        e.servicePack = pack
      }
    })
  }

  //https://github.com/zran-nz/bug/issues/5297#issuecomment-2367711344
  if (servicePackApply.value?.contentOrientated?.length && applyActions.value) {
    const priceMap = new Map(
      //schoolPriceData.value.contentOrientated.map((item) => {
      servicePackApply.value.contentOrientated.map((item) => {
        return [`${item.premium}-${item.servicePack || 0}`, item.price]
      })
    )
    list.value = list.value
      .map((item) => {
        const key = `${item.premium._id}-${item.servicePack?._id || 0}`
        if (priceMap.has(key)) {
          return {...item, price: priceMap.get(key)} // Update price
        }
        return null // Mark for removal
      })
      .filter((item) => item !== null)
  }

  if (data?.carerPack?._id) {
    let carerPackInfo
    if (purchased.value) {
      const rs = await App.service('service-pack-user').find({query: {uid: pub.user._id, 'snapshot._id': data.carerPack._id}})
      if (rs?.data?.length) {
        packUserIds.value.push(...rs.data.map((e) => e._id))
      }
      if (rs.data?.length) {
        carerPackInfo = rs.data[0]
      }
    } else {
      carerPackInfo = await App.service('service-pack').get(data?.carerPack._id)
    }
    if (carerPackInfo) {
      data.carerPackInfo = carerPackInfo
    }
  }

  list.value.map((e) => {
    if (!sharedSchool.value) {
      e.discountConfig = data?.discountConfig
    }
    e.type = 'lecture'
    e.splitSale = data.splitSale
    e.choose = true
  })

  if (data?.carerPackInfo) {
    list.value.push({
      ...data?.carerPackInfo,
      type: 'carer',
      times: data.carerPack.times,
    })
  }
  console.log('list', list.value)
}

const getOnCampusUser = async () => {
  const rs = await App.service('service-pack-user').find({
    query: {
      $isSchool: true,
      pid: id.value,
    },
  })
  onCampusList.value = rs.data || []
}

const isSchoolAdmin = () => {
  return pub.schoolUserList?.some((e) => e.school == route.query.school && e.role?.includes('admin'))
}

const onCampusTabChange = (tab) => {
  onCampusPrice.value = packageData.value.onCampusPrice.find((e) => e.city == tab)
}

const getSubstituteDetail = async () => {
  if (packageData.value?.serviceRoles === 'substitute' && route.query.subId && purchased.value) {
    substituteDetail.value = await App.service('session').get(route.query.subId)
  }
}

const onSetParticipantsClick = (item) => {
  console.log('item', item)
  router.push({
    path: `/premcpack/participants/${item.premium?._id}`,
    query: {
      packId: packageData.value?._id,
      lectureId: item.premium?.premium,
    },
  })
}

const init = async () => {
  console.log('init')
  loading.value = true
  //https://github.com/zran-nz/bug/issues/5074
  // if (route.query.school) {
  //   if (!isSchoolAdmin()) {
  //     $q.dialog({
  //       component: PromptPage,
  //       componentProps: {
  //         fullscreen: true,
  //         type: 'unauthorized',
  //         title: 'Link no longer available!',
  //       },
  //     })
  //     return
  //   }
  // }
  if (route.params.tab == 'my' || props.servicePackUserId) {
    await getServicePackUser()
    if (route.query.tab === 'purchased') {
      const rs = await App.service('service-pack-user').get(route.params.id)
      const rs2 = await App.service('service-pack-user').find({query: {pid: route.params.id}})
      if (rs2.data.length && rs2.data[0]?.snapshot?.type === 'serviceTask') {
        associatePackUserId.value = rs2.data[0]._id
        associatedTask.value = rs2.data[0].snapshot
        console.log('rs2 value', rs2.data[0]._id)
      }
    }
  } else {
    if (isPointMode.value) {
      await pStore.getClaimSetting()
    }
    await getServicePack()
    if (route.path.includes('sys')) {
      isSysView.value = true
    }

    if (!packageData.value.status) {
      $q.dialog({
        component: PromptPage,
        componentProps: {
          fullscreen: true,
          type: 'unauthorized',
          title: 'The product you chose has been unpublished.',
          //subtitle: 'You have been removed from the school list',
        },
      })
      return
    }
  }

  if (packageData.value?.serviceRoles === 'substitute' && packageData.value?.isOnCampus && purchased.value) {
    await getOnCampusUser()
  }

  if (packageData.value.contentOrientatedEnable) {
    if (!purchased.value && !readonly.value && pub?.user?._id) {
      if (sharedSchool.value || (isAdmin.value && schoolId.value)) {
        await getSchoolPriceData()
      }
      await getApplyStatus()
      await getBoughtStatus()
    }
    await getContentOrientated()
  }

  if (packageData.value?.associatedTask?._id) {
    associatedTask.value = await App.service('service-pack').get(packageData.value.associatedTask._id)
  }

  if (purchased.value) {
    await getPayMethods()
  }
  shareLink.value = location.origin + '/v2' + route.matched[1]?.path?.replace(':id', packageData.value._id)
  if (isAdmin.value && schoolId.value) {
    router.replace({query: {school: schoolId.value, ...route.query}})
  }

  loading.value = false
  console.log('juhhhh', unavailable.value, userPackageData.value)
  emit('load', unavailable.value, userPackageData.value)
  common.getCountryList()
  getSubstituteDetail()
  // if (route.query.book) {
  //   toBook()
  // }
}

onMounted(async () => {
  console.log('route.path', route.path)
  init()
})

watch(
  () => route.path,
  () => {
    window.location.reload()
  }
)
</script>
