<template>
  <PubHeader showAccount showNotice />
  <q-page-container>
    <div class="full-width flex justify-center q-pa-lg" v-if="loadingPage">
      <q-spinner-ball color="primary" size="2em" class="full-width" />
    </div>
    <q-page class="pc-sm q-pa-md" v-show="!loadingPage">
      <div class="text-h5 text-weight-medium">
        <q-btn flat round dense size="lg" icon="arrow_back" @click="router.back()"></q-btn>
        {{ title }}
      </div>
      <div class="hidden">
        <pre>bookingStore.selectedTimeSlots:{{ bookingStore.selectedTimeSlots }}</pre>
        <pre>bookingStore.invalidTimeSlots:{{ bookingStore.invalidTimeSlots }}</pre>
      </div>
      <div class="q-pa-md booking-confirmation-wrap">
        <div class="row items-center q-pa-md">
          <PubAvatar size="3.6rem" :src="teacher.owner?.avatar" />
          <div class="text-bold q-pl-md">{{ nameFormatter(teacher.owner) }}</div>
        </div>
        <q-list separator>
          <q-item v-for="item in list" :key="item._id" :class="{highlight: item.newError}">
            <q-item-section>
              <div class="text-weight-medium q-pt-md">Booking time</div>
              <div class="text-weight-medium q-pt-sm">{{ item.startTime }} - {{ item.endTime }}</div>
              <div class="text-weight-medium">{{ item.startDate.slice(0, 5) }}</div>
              <q-icon class="q-pr-md hidden" :class="item.invalid ? '' : 'invisible'" name="error" color="negative" size="1.2rem"></q-icon>
              <q-item-label class="hidden">{{ item.error || item.name }}</q-item-label>
              <div class="text-weight-medium q-pt-md">Title</div>
              <div class="q-pb-md q-pt-sm">{{ bindingCourse.name || pack?.snapshot?.name || pack?.snapshot?.unitSnapshot?.name }}</div>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-page>
  </q-page-container>
  <q-footer reveal elevated class="bg-white">
    <q-toolbar>
      <q-space></q-space>
      <q-btn rounded color="primary" label="Confirm" :disable="disableConfirm" :loading="confirming" no-caps @click="confirmBooking" />
    </q-toolbar>
  </q-footer>
</template>
<script setup>
/*
imports
*/
import {date} from 'quasar'
import moment from 'moment'
import {ref, onMounted, onBeforeUnmount, watch, computed, inject} from 'vue'
import {pubStore} from 'stores/pub'
import {useRoute, useRouter} from 'vue-router'

import AccountMenu from 'components/AccountMenu.vue'
import NoticePage from 'components/NoticePage.vue'

import {bookingStore as bStore} from 'stores/booking'
const bookingStore = bStore()

/*
consts
*/
const loadingPage = ref(true)
const bookNORTimeLead = inject('BOOK_NOR_TIME_LEAD')
const bookCONTimeLead = inject('BOOK_CON_TIME_LEAD')
const isConsultant = ref(false)
const title = ref('Booking confirmation')
const moreState = ref(false)
const route = useRoute()
const router = useRouter()
import {useQuasar} from 'quasar'
const $q = useQuasar()
import nameFormatter from 'src/utils/formatters/nameFormatter'
const teacherId = ref(route.params.teacherId)
const packId = ref(route.params.packId)
const bindingCourseId = ref(route.query.bindingCourseId)
const bindingCourse = ref({})

const teacher = ref({})
const pack = ref({})
const list = ref([])
const confirming = ref(false)
const conflictWithOwnSession = ref(false)
let lastInvalidTimeSlots = []

const disableConfirm = computed(() => {
  const errors = list.value.filter((item) => item.invalid)
  return errors.length == list.value.length
})

const confirmBooking = async () => {
  const successfulBookings = []
  confirming.value = true
  const invalidTimeSlots = await handleBookingSlots(true)
  const firstMissingItem = findFirstMissingItem(lastInvalidTimeSlots, invalidTimeSlots)
  if (firstMissingItem) {
    lastInvalidTimeSlots = invalidTimeSlots
  } else {
    for (const slot of bookingStore.selectedTimeSlots) {
      try {
        const res = await createBooking(teacherId.value, packId.value, slot[0], slot[1])
        successfulBookings.push(res)
      } catch (e) {
        console.error(e)
      }
    }
    if (route.query.applyId) {
      await App.service('service-pack-apply').patch(route.query.applyId, {interviewApply: true})
    }
    if (route.query.authId) {
      await App.service('service-auth').patch(route.query.authId, {interviewApply: true})
    }
    const bookingId = successfulBookings.map((e) => e._id).join(',') || ''
    router.push(`/booking/booking-confirmed/${teacherId.value}/${packId.value}?bookingId=${bookingId}`)
  }

  confirming.value = false
}

const getTimes = (start, end) => {
  const diff = date.getDateDiff(new Date(end), new Date(start), 'minutes')
  let times = 1
  if (pack.value?.snapshot?.duration && pack.value?.snapshot?.break) {
    times = (diff / (pack.value.snapshot.duration + pack.value.snapshot.break)).toFixed(0)
  }

  return parseInt(times)
}

const createBooking = async (teacherId, packId, start, end) => {
  const times = getTimes(start, end)
  const serviceBooking = {
    packUser: packId,
    servicer: teacherId,
    start: start,
    end: end,
    duration: pack.value?.snapshot.duration + pack.value?.snapshot.break || 0,
    times,
    message: bookingStore.message,
    attachments: bookingStore.attachments,
    slides: bookingStore.slides,
  }
  if (bindingCourseId.value) {
    if (route.query.unit) {
      serviceBooking.packUserTasks = [bindingCourseId.value]
    } else {
      serviceBooking.oldSession = bindingCourse.value
    }
  }

  if (route.query.applyId) {
    serviceBooking.servicePackApply = route.query.applyId
  }

  if (route.query.authId) {
    serviceBooking.serviceAuthId = route.query.authId
  }

  if (route.query.topic) {
    serviceBooking.topic = route.query.topic.split(',')
  }

  const res = await App.service('service-booking').create(serviceBooking)
  return res
}

const findFirstMissingItem = (arr1, arr2) => {
  for (let i = 0; i < arr2.length; i++) {
    let currentItem = arr2[i]
    let found = false

    for (let j = 0; j < arr1.length; j++) {
      if (arraysAreEqual(arr1[j], currentItem)) {
        found = true
        break
      }
    }

    if (!found) {
      return currentItem
    }
  }

  return null
}

const arraysAreEqual = (arr1, arr2) => {
  if (arr1.length !== arr2.length) {
    return false
  }

  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) {
      return false
    }
  }

  return true
}

const handleBookingSlots = async (isConfirm) => {
  const bookedTeacher = await App.service('service-conf').get(teacherId.value, {query: {booking: 1, session: 1}})
  const bookedTeacherSlots = bookedTeacher.booking.concat(bookedTeacher.session).concat(bookedTeacher.holiday)
  console.log(bookedTeacherSlots, '<=========bookedTeacherSlots 老师的已占用的时间')
  const bookedMine = await App.service('service-booking').get('myHours')
  const bookedMineSlots = bookedMine.booking //.concat(bookedMine.session)
  const allList = []
  console.log(bookedMineSlots, '<==========bookedMineSlots 学生自己已占用的时间')

  let overTimeSolts = [],
    teacherBookedSolts = [],
    mineBookedSolts = []
  let validSolts = []
  const selectedTimeSlots = JSON.parse(JSON.stringify(bookingStore.selectedTimeSlots))

  /*TESTING ONLY START*/
  //selectedTimeSlots.push(bookedTeacherSlots[0])
  //selectedTimeSlots.push(bookedMineSlots[0])
  /*TESTING ONLY END*/

  console.log(selectedTimeSlots, '<==============selectedTimeSlots 当前选择的时间')

  //https://github.com/zran-nz/bug/issues/5501#issue-2595939085
  const timeLead = isConsultant.value ? bookCONTimeLead : bookNORTimeLead
  for (const t of selectedTimeSlots) {
    const isOverTime = new Date(t[0]).getTime() - Date.now() < timeLead * 60 * 60 * 1000
    const isTeacherBooked = findNonOverlappingIntervalsInA([t], bookedTeacherSlots)?.overlapping?.length
    const isMineBooked = findNonOverlappingIntervalsInA([t], bookedMineSlots)?.overlapping?.length
    if (isOverTime) {
      overTimeSolts.push(t)
    } else if (isMineBooked) {
      mineBookedSolts.push(t)
    } else if (isTeacherBooked) {
      teacherBookedSolts.push(t)
    } else {
      validSolts.push(t)
    }
  }
  overTimeSolts = mergeTimeIntervals(overTimeSolts)
  teacherBookedSolts = mergeTimeIntervals(teacherBookedSolts)
  mineBookedSolts = mergeTimeIntervals(mineBookedSolts)
  validSolts = mergeTimeIntervals(validSolts)

  if (isConfirm) {
    bookingStore.setSelectedTimeSlots(validSolts)
  }
  const invalidTimeSlots = [...overTimeSolts, ...teacherBookedSolts, ...mineBookedSolts]
  const uniqueArray = Array.from(new Set(invalidTimeSlots.map(JSON.stringify)), JSON.parse)
  bookingStore.setInvalidTimeSlots(uniqueArray)

  for (const t of overTimeSolts) {
    const timeObject = {
      _id: Math.random(),
      startTime: moment(t[0]).format('hh:mm A'),
      startDate: moment(t[0]).format('MM-DD-YY'),
      endTime: moment(t[1]).format('hh:mm A'),
      endDate: moment(t[1]).format('MM-DD-YY'),
    }
    const coreText = 'conflicts with the earliest available booking time'
    allList.push({
      ...timeObject,
      invalid: true,
      error: `The selected time slot ${coreText}.`,
    })
  }

  if (overTimeSolts?.length) {
    const coreText = 'conflicts with the earliest available booking time'
    console.warn(overTimeSolts, coreText)
  }
  for (const t of teacherBookedSolts) {
    const timeObject = {
      _id: Math.random(),
      startTime: moment(t[0]).format('hh:mm A'),
      startDate: moment(t[0]).format('MM-DD-YY'),
      endTime: moment(t[1]).format('hh:mm A'),
      endDate: moment(t[1]).format('MM-DD-YY'),
    }
    const coreText = 'has been booked by other student'
    allList.push({
      ...timeObject,
      invalid: true,
      error: `The selected time slot ${coreText}.`,
    })
  }
  if (teacherBookedSolts?.length) {
    const coreText = 'has been booked by other student'
    console.warn(teacherBookedSolts, coreText)
  }
  for (const t of mineBookedSolts) {
    const timeObject = {
      _id: Math.random(),
      startTime: moment(t[0]).format('hh:mm A'),
      startDate: moment(t[0]).format('MM-DD-YY'),
      endTime: moment(t[1]).format('hh:mm A'),
      endDate: moment(t[1]).format('MM-DD-YY'),
    }
    const coreText = 'conflicts with your other booked or enrolled session'
    allList.push({
      ...timeObject,
      invalid: true,
      error: `The selected time slot ${coreText}.`,
    })
    conflictWithOwnSession.value = true
  }
  if (mineBookedSolts?.length) {
    const coreText = 'conflicts with your other booked or enrolled session'
    console.warn(mineBookedSolts, coreText)
  }
  for (const t of validSolts) {
    const timeObject = {
      _id: Math.random(),
      startTime: moment(t[0]).format('hh:mm A'),
      startDate: moment(t[0]).format('MM-DD-YY'),
      endTime: moment(t[1]).format('hh:mm A'),
      endDate: moment(t[1]).format('MM-DD-YY'),
    }
    allList.push({
      ...timeObject,
      name: pack.value.snapshot?.name,
    })
  }
  list.value = allList
  const allInvalidSlots = allList.filter((item) => item.invalid)
  const allValidSlots = allList.filter((item) => !item.invalid)
  console.warn(allInvalidSlots, '<================所有预约失败的时间')
  console.log(allValidSlots, '<================所有预约成功的时间')

  if (allInvalidSlots.length) {
    let message = 'The facilitator is no longer available.'
    if (conflictWithOwnSession.value) {
      message = 'You are not available for the chosen time slot.'
    }
    $q.notify({color: 'grey-7', position: 'center', message, icon: null})
    if (isConfirm) {
      await sleep(3000)
    }
  }
  return uniqueArray
}

function findNonOverlappingIntervalsInA(a, b) {
  const nonOverlapping = []
  const overlapping = []
  for (const intervalA of a) {
    let isOverlapping = false
    for (const intervalB of b) {
      const startA = new Date(intervalA[0])
      const endA = new Date(intervalA[1])
      const startB = new Date(intervalB[0])
      const endB = new Date(intervalB[1])
      //if (startA <= endB && endA >= startB) {
      if (startA < endB && endA > startB) {
        isOverlapping = true
        break
      }
    }
    if (isOverlapping) {
      overlapping.push(intervalA)
    } else {
      nonOverlapping.push(intervalA)
    }
  }
  return {nonOverlapping, overlapping}
}

function mergeTimeIntervals(intervals) {
  if (intervals.length <= 1) {
    return intervals
  }
  intervals.sort((a, b) => new Date(a[0]) - new Date(b[0]))
  const mergedIntervals = [intervals[0]]
  for (let i = 1; i < intervals.length; i++) {
    const currentInterval = intervals[i]
    const lastMergedInterval = mergedIntervals[mergedIntervals.length - 1]
    const currentIntervalStart = new Date(currentInterval[0])
    const currentIntervalEnd = new Date(currentInterval[1])
    const lastMergedIntervalEnd = new Date(lastMergedInterval[1])
    if (currentIntervalStart <= lastMergedIntervalEnd) {
      lastMergedInterval[1] = currentIntervalEnd.toISOString()
    } else {
      mergedIntervals.push(currentInterval)
    }
  }
  return mergedIntervals
}

const fetchData = async () => {
  await App.service('service-pack-user')
    .get(packId.value)
    .then((res) => {
      if (res?._id) {
        pack.value = res
        if (pack.value.snapshot?.consultant?.type) {
          isConsultant.value = true
        }
      }
    })
  teacher.value = await App.service('service-conf').get(teacherId.value)
  getParentServicePackUser()
}

const getParentServicePackUser = async () => {
  if (route.query.pid) {
    await App.service('service-pack-user')
      .get(route.query.pid)
      .then((res) => {
        if (res?.snapshot?.duration && res?.snapshot?.break && pack.value?.snapshot) {
          pack.value.snapshot.duration = res.snapshot.duration
          pack.value.snapshot.break = res.snapshot.break
        }
      })
  }
}
const main = async () => {
  loadingPage.value = true
  await fetchData()
  lastInvalidTimeSlots = await handleBookingSlots()

  if (bindingCourseId.value && !route.query.unit) {
    const {_id, name, image} = await App.service('session').get(bindingCourseId.value)
    bindingCourse.value = {_id, name, image}
  }
  loadingPage.value = false
}

onMounted(main)
</script>
<style lang="sass">
.booking-confirmation-wrap
 height: 100vh
.confirm-booking-wrap
 bottom: 1rem
 right:0

$highlight-color: yellow

@keyframes highlight
  0%
    background-color: $highlight-color
  50%
    background-color: transparent
  100%
    background-color: $highlight-color

.highlight
  animation: highlight 1s ease-in-out
</style>
