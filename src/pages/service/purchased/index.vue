<template>
  <q-layout view="hHh LpR fFf">
    <PubHeader :showShare="false" />
    <q-page-container class="pc-sm explicit-page-container bg-white">
      <q-page class="column overflow-hidden" v-if="loading">
        <div class="col text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
      </q-page>
      <q-page v-else-if="unavailable">
        <NoData></NoData>
      </q-page>
      <q-page v-else>
        <div class="q-pa-md">
          <BreadCrumbs title="Preview"></BreadCrumbs>
          <div class="row items-center justify-between">
            <div class="text-weight-medium col text-h5">
              <q-btn flat round dense size="lg" icon="arrow_back" @click="goBack"></q-btn>
              {{ packageData.name }}
            </div>

            <div class="text-subtitle2 text-teal">
              <template v-if="!isContentOrientatedEnable">
                <q-chip v-if="freePayMethods[id]" label="Free" color="primary" square outline :ripple="false" size="sm"></q-chip>
                <q-icon v-else dense name="o_monetization_on" size="sm" style="color: #c06612" />
              </template>
            </div>
          </div>
          <q-banner inline-actions rounded class="bg-amber-2 text-black q-my-md" v-if="!bannerClosed && bannerMessage?.message">
            {{ bannerMessage.message }}
            <template v-slot:action>
              <q-btn flat rounded icon="close" class="q-pa-sm" @click="bannerClosed = true" />
            </template>
          </q-banner>

          <div class="q-my-md">
            <q-img
              spinner-color="white"
              fit="cover"
              class="fit rounded-borders-md"
              :ratio="16 / 5"
              :src="hashToUrl(packageData?.cover) || '/v2/img/avatar.png'">
            </q-img>
          </div>
          <div class="text-negative" v-if="bannerMessage?.approved">Status: Approved</div>

          <div class="row q-gutter-md q-my-md" v-if="isContentOrientatedEnable">
            <q-btn :to="toFeature()" class="col" color="primary" rounded no-caps label="Go to feature"></q-btn>
          </div>

          <div v-if="!readonly && isContentOrientatedEnable && isAdmin">
            <div class="row q-gutter-md">
              <q-btn :to="toCheckVouchers()" class="col" color="primary" rounded no-caps label="Check vouchers"></q-btn>
            </div>
          </div>

          <div class="q-my-md">
            <div class="text-subtitle1">The current service package can provide service for the following subjects</div>
            <q-card class="q-mt-md q-py-md rounded-borders-md">
              <q-card-section class="q-py-none">
                <PackageChips :pack="packageData" :is-educator="!showBook"></PackageChips>
              </q-card-section>
            </q-card>
          </div>
          <PackagePacket
            v-if="!isSubOnCampus"
            :packageData="packageData"
            :duration-only="isContentOrientatedEnable"
            :userPackageData="userPackageData"></PackagePacket>
          <q-card class="rounded-borders-md q-my-md">
            <q-card-section class="text-subtitle1"> Value of the service package </q-card-section>
            <q-card-section class="q-pt-none">
              <div class="q-my-sm text-body2" v-for="(point, i) in packageData?.points" :key="i">
                {{ point }}
              </div>
            </q-card-section>
          </q-card>
          <q-card class="rounded-borders-md q-my-md" v-if="packageData?.attachments?.length">
            <q-card-section class="text-subtitle1"> Promotional material</q-card-section>
            <q-card-section class="q-pt-none">
              <PreviewLists :files="packageData.attachments"></PreviewLists>
            </q-card-section>
          </q-card>
          <template v-if="isContentOrientatedEnable">
            <q-list v-for="item in list" :key="item._id" class="q-mt-md">
              <template v-if="item.premium">
                <q-expansion-item default-opened>
                  <template v-slot:header>
                    <div class="row full-width">
                      <div class="col-11">
                        <PackageCard
                          :onItemClick="onPremiumContentClick"
                          shadow
                          detail
                          :payMethod="freePayMethods[item.premium._id] ? 'free' : 'cash'"
                          :service-no="item.times"
                          :is-educator="false"
                          tag="Premium lecture"
                          :pack="item.premium.snapshot"
                          :pack-user="item.premium" />
                      </div>
                    </div>
                  </template>
                  <div class="q-pt-md">
                    <PackagePacket :packageData="item.premium.snapshot" :userPackageData="item.premium"></PackagePacket>
                  </div>
                  <q-btn class="fit q-mt-md" color="primary" outline rounded no-caps @click="toBook(item)" label="Book extra session"></q-btn>
                  <q-btn @click="onPurchaseClick(item)" class="fit q-mt-md" color="primary" label="Purchase more" outline rounded no-caps />
                  <q-btn class="fit q-mt-md" color="primary" outline rounded no-caps label="History" :to="`/order/servicePack/${item.premium._id}`"></q-btn>
                  <q-btn
                    v-if="isAdmin"
                    class="fit q-mt-md"
                    color="primary"
                    outline
                    rounded
                    no-caps
                    label="Set participants"
                    :to="toSetParticipants(item)"></q-btn>
                  <q-separator class="q-mt-md"></q-separator>
                </q-expansion-item>
              </template>
            </q-list>
            <template v-if="!isAdmin">
              <div v-for="item in list" :key="item._id" class="q-mt-md">
                <PackageCard
                  v-if="item.servicePack?._id"
                  shadow
                  :payMethod="freePayMethods[item.servicePack._id] ? 'free' : 'cash'"
                  :is-educator="false"
                  :pack="item.servicePack.snapshot"
                  :pack-user="item.servicePack"
                  :premium="item.premium?.snapshot?.unit?._id"
                  category="purchased" />
                <div class="q-mt-md">
                  <TaskCard
                    v-if="item.associateTask?._id"
                    style="margin-top: 16px"
                    isView
                    isPurchased
                    :task="item.associateTask.snapshot"
                    :clickAllowed="true"
                    :associatePackUserId="item.associateTask?._id" />
                </div>
              </div>
              <div v-for="item in list" :key="item._id" class="q-mt-md">
                <PackageCard
                  v-if="item.type == 'carer'"
                  shadow
                  :is-educator="false"
                  :payMethod="freePayMethods[item] ? 'free' : 'cash'"
                  :pack="item.snapshot"
                  :pack-user="item"
                  category="purchased" />
              </div>
            </template>
          </template>
          <template v-else-if="isSubOnCampus">
            <div v-for="item in onCampusList" :key="item._id">
              <q-card class="rounded-borders-md q-my-md">
                <q-card class="rounded-borders-md q-my-md">
                  <q-card-section>
                    <div class="row q-col-gutter-md items-center">
                      <div class="col-xs-12 col-sm-2">
                        <q-img
                          class="full-width rounded-borders-md"
                          :ratio="$q.screen.gt.xs ? 16 / 12 : 16 / 9"
                          fit="cover"
                          :src="hashToUrl(item?.snapshot?.onCampusPrice?.find((v) => v.city === item.city)?.hash) || '/v2/img/no-img.png'" />
                      </div>

                      <div class="col-xs-12 col-sm-10">
                        <div class="text-primary text-bold">{{ item.city }}</div>
                        <div class="row q-mt-sm">
                          <div class="col-4">No of minutes</div>
                          <div class="col-8">{{ item.total - item.used }} / {{ item.total }} mins available</div>
                        </div>
                        <div class="row q-mt-sm">
                          <div class="col-4">Purchased on</div>
                          <div class="col-8">{{ date.formatDate(item.createdAt, 'DD/MM/YYYY') }}</div>
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </q-card>
              <div class="q-gutter-md q-pr-md q-mb-md">
                <q-btn v-if="item.total - item.used > 0" class="fit" color="primary" outline rounded no-caps label="Book now" @click="subBook(item)" />
                <q-btn class="fit" color="primary" outline rounded no-caps label="Location setting" @click="subLocation(item)" />
                <q-btn
                  class="fit"
                  color="primary"
                  outline
                  rounded
                  no-caps
                  label="History"
                  :to="`/order/servicePack/${item._id}?type=${packageData.serviceRoles}`" />
              </div>
            </div>
          </template>

          <template v-else>
            <div v-if="associatedTasks.length > 0" class="q-mb-md">
              <div class="q-mb-md text-bold text-subtitle1">Associated Tasks</div>
              <div v-for="(task, index) in associatedTasks" :key="associatePackUserIds[index]" class="q-mb-md">
                <TaskCard isView isPurchased :task="task" :clickAllowed="true" :associatePackUserId="associatePackUserIds[index]" />
              </div>
            </div>
            <div class="q-gutter-md q-pr-md q-mb-md" v-if="showBook && !readonly">
              <q-btn v-if="!available" :to="buyLink()" class="fit" color="primary" outline rounded no-caps label="Buy"></q-btn>
              <q-btn v-if="available" color="primary" class="fit" outline rounded no-caps label="Book now" @click="toBook()"></q-btn>
              <q-btn
                class="fit"
                color="primary"
                outline
                rounded
                no-caps
                label="History"
                :to="`/order/servicePack/${userPackageData._id}?type=${packageData.serviceRoles}`"></q-btn>
              <Policy v-if="!session" />
            </div>
            <div v-if="session && !route.query?.back?.includes('/detail/session')" class="q-my-md">
              <q-separator></q-separator>
              <div class="col q-my-md text-center text-subtitle1">Bundled premium workshop</div>
              <SessionBoard :session="session" isRelevant />
            </div>
          </template>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import {ref, onMounted, inject, watch, computed, onUnmounted, openBlock} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {date} from 'quasar'
import {pubStore} from 'stores/pub'
import {pointStore} from 'stores/point'
import {GradeGroupMap, EducatorGrades} from 'src/boot/const'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import PackagePacket from 'components/PackagePacket.vue'
import PackageChips from 'components/PackageChips.vue'
import PackageCard from 'components/PackageCard.vue'
import useSubject from 'src/composables/account/academic/useSubject'
import SessionBoard from 'components/SessionBoard.vue'
import BreadCrumbs from 'components/BreadCrumbs.vue'
import PromptPage from 'components/PromptPage.vue'
import useSchool from 'src/composables/common/useSchool'
import AlertDialog from 'src/components/AlertDialog.vue'
import PurMoreDialog from 'src/pages/booking/components/PurMoreDialog.vue'

import PreviewLists from 'src/components/PreviewLists.vue'
import SubLocationDialog from 'src/pages/substitute/SubLocationDialog.vue'
import SubjectDialog from 'components/SubjectDialog.vue'
import Policy from 'src/pages/service/Policy.vue'
import TaskCard from '../../../components/ServiceTask/TaskCard.vue'

/*
  consts
*/
const pub = pubStore()
const route = useRoute()
const router = useRouter()
const id = ref(route.params.id)

const {sysMap} = useSubject()
const {isAdmin} = useSchool()

const unavailable = ref(false)

const packageData = ref({})
const userPackageData = ref({})
const freePayMethods = ref({})
const packUserIds = ref([])
const list = ref([])
const session = ref()

const loading = ref(false)

const readonly = ref(route.query.type == 'view')

const bannerClosed = ref(false)

const onCampusList = ref([])

const substituteDetail = ref(null)
const associatedTasks = ref([])
const associatePackUserIds = ref([])

/*
  computeds
*/

const isContentOrientatedEnable = computed(() => {
  return userPackageData.value.snapshot?.contentOrientatedEnable
})

const isSubOnCampus = computed(() => {
  return userPackageData.value.snapshot?.serviceRoles === 'substitute' && userPackageData.value.snapshot?.isOnCampus
})

const isSub = computed(() => {
  return userPackageData.value.snapshot?.serviceRoles === 'substitute'
})

const bannerMessage = computed(() => {
  let message = ''
  let approved = false
  if (userPackageData.value?.snapshot) {
    const servicePackSnapshot = userPackageData.value.snapshot
    const has1V1Mentor = Array.isArray(servicePackSnapshot.contentOrientated) && servicePackSnapshot?.contentOrientated?.some((e) => e.servicePack)
    const hasCarerPack = servicePackSnapshot?.carerPack?._id
    const isInterviewPack = ['interview', 'interviewTeacher'].includes(servicePackSnapshot?.consultant?.type)
    if (isContentOrientatedEnable.value) {
      if (has1V1Mentor && hasCarerPack) {
        message = isAdmin.value
          ? 'You have successfully purchased the Premium lecture, 1v1 mentor and Carer service. You can go to the voucher management by clicking on check vouchers voucher button to allocate the vouchers to your applicants.'
          : 'You have successfully purchased the Premium content letcure, 1v1 Mentor, Carer service. You can use the 1v1 Mentor to assist you with your lecure study, and book a carer secvice to provide you with lecure study progress and advices.'
      } else if (has1V1Mentor) {
        message = isAdmin.value
          ? 'You have successfully purchased the Premium lecture and 1v1 mentor. You can go to the voucher management by clicking on check vouchers button to allocate the vouchers to your applicants.'
          : 'You have successfully purchased the Premium content letcure and 1v1 Mentor. You can use the 1v1 Mentor to assist you with your lecure study.'
        if (!isAdmin.value) {
          approved = true
        }
      } else if (hasCarerPack) {
        message = isAdmin.value
          ? 'You have successfully purchased the Premium lecture and Carer service. You can go to the voucher management by clicking on check vouchers button to allocate the vouchers to your applicants.'
          : 'You have successfully purchased the Premium content letcure and Carer service. You can book a carer secvice to provide you with lecure study progress and advices.'
      }
    } else if (isInterviewPack && userPackageData.value.total > userPackageData.value.used) {
      //https://github.com/zran-nz/bug/issues/5052#issuecomment-2364952656
      message = 'Please choose a suitable time and book a teacher for your interview'
    }
  }

  return {message, approved}
})

const showBook = computed(() => {
  const queryBack = route.query?.back
  return (
    !queryBack?.includes('/detail/session') ||
    (queryBack?.includes('/detail/session') && (queryBack?.includes('/study/purchased') || queryBack?.includes('/home/<USER>')))
  )
})

const available = computed(() => {
  if (isSub.value) {
    return userPackageData.value.total > 0
  } else {
    return userPackageData.value.total > userPackageData.value.used
  }
})

/*
  methods
*/
const gradeOptions = (snapshot) => {
  const mentoringType = snapshot?.mentoringType
  if (['teacherTraining', 'teacherTrainingSubject'].includes(mentoringType)) {
    return EducatorGrades.map((value) => {
      return {label: value, value: value}
    }).filter((e) => snapshot?.gradeGroup?.includes(e.value))
  } else {
    if ((mentoringType == 'academic' && snapshot.curriculum) || mentoringType !== 'academic') {
      return Object.entries(GradeGroupMap)
        .map(([key, value]) => {
          value.key = key
          return value
        })
        .filter((e) => snapshot?.gradeGroup?.includes(e.key))
    } else {
      return false
    }
  }
}

const goBack = () => {
  if (route.query.back) {
    router.replace(route.query.back)
  } else {
    router.go(-1)
  }
}

const onPurchaseClick = async (item) => {
  $q.loading.show()
  const orderCheck = await App.service('order').get('checkLinks', {
    query: {
      links: [{id: item?.premium?.premium, style: 'service_premium'}],
      servicePremium: packageData.value?._id,
    },
  })
  $q.loading.hide()
  setTimeout(() => {
    $q.loading.hide()
  }, 3000)
  if (orderCheck?.unpaidOrderId?.length > 0) {
    router.push({path: `/order/detail/${orderCheck?.unpaidOrderId?.[0]}`, query: {back: route.query.back}})
    return
  }

  $q.dialog({
    component: PurMoreDialog,
    componentProps: {
      packUserId: item?.premium?._id,
      servicePremium: packageData.value,
      lecture: item?.premium,
    },
  })
}

const toFeature = () => {
  return {path: `/service/pack/${userPackageData.value?.snapshot?._id}`, query: {}}
}

const toCheckVouchers = () => {
  return {path: `/premcpack/applicationTrackVoucher/${packageData.value._id}`, query: {back: route.fullPath}}
}

const onPremiumContentClick = (item, main, times) => {
  router.push({path: `/detail/content/limit/${item.unit._id}`, query: {spuid: main._id, times, back: route.fullPath}})
}

const subBook = async (item) => {
  console.log('item', item)

  if (!route.query.subId) {
    $q.dialog({
      component: AlertDialog,
      componentProps: {
        title: 'Please go to account_substitute teacher tracking page and the substitube service package can only be used upon approval of substitute request.',
        confirmText: 'Go to substitute teacher tracking',
      },
    }).onOk(() => {
      router.push({
        path: '/substitute/track',
        query: {
          back: encodeURIComponent(route.fullPath),
        },
      })
    })
    return
  }

  let duration = date.getDateDiff(new Date(substituteDetail.value?.end), new Date(substituteDetail.value?.start), 'minutes')
  if (isSubOnCampus.value) {
    $q.loading.show()
    const res = await App.service('campus-location').find({
      query: {
        archive: false,
        city: item.city,
        country: item.country,
      },
    })
    $q.loading.hide()
    if (res?.total > 0) {
      const data = res.data[0]
      duration += data?.compensationHour * 60
    } else {
      $q.notify({
        type: 'negative',
        message: `No Compensation time  in ${item.city}, ${item.country}`,
      })
      return
    }
  }

  let available = item?.total - item?.used
  if (duration > available) {
    $q.dialog({
      title: 'Confirm',
      message: `Your current balance is insufficient, please buy again.`,
      cancel: true,
    }).onOk(async () => {
      router.push({
        path: `/order/confirm/service_substitute/${item?.snapshot._id}`,
        query: {
          back: route.query.back,
          payBack: encodeURIComponent(route.fullPath),
          city: item.city,
        },
      })
    })
    return
  }

  if (isSubOnCampus.value && !item?.place_id) {
    subLocation(item, () => {
      jumpBook(item)
    })
  } else {
    jumpBook(item)
  }

  console.log('<===============substituteBook', item)
}

const jumpBook = (item) => {
  router.push({
    path: `/substitute/book/${route.query.subId}`,
    query: {
      packUser: item._id,
    },
  })
}

const subLocation = (item, cb) => {
  console.log('<===============subLocation', item)
  $q.dialog({
    component: SubLocationDialog,
    componentProps: {
      country: item.country,
      place_id: item.place_id,
      address: item.address,
    },
  }).onOk(async ({address, place_id}) => {
    await App.service('service-pack-user')
      .patch('location', {
        _id: item._id,
        place_id,
        address,
      })
      .then(() => {
        getOnCampusUser()
        if (cb) {
          cb()
        }
      })
  })
}

const toBook = async (item) => {
  if (userPackageData.value.snapshot?.serviceRoles === 'substitute') {
    subBook(userPackageData.value)
    return
  }

  if (route.query.topic) {
    getServiceConf(item, route.query.topic)
  } else if (Array.isArray(packageData.value?.topic) && packageData.value?.topic?.length > 1 && !item) {
    $q.dialog({
      component: SubjectDialog,
      componentProps: {
        pack: packageData.value,
      },
    }).onOk(async (topic) => {
      getServiceConf(item, topic)
    })
  } else {
    getServiceConf(item)
  }
}

const getServiceConf = async (item, topic) => {
  const grades = gradeOptions(item?.premium?.snapshot || packageData.value)
  $q.loading.show()
  const query = {packUserId: item?.premium?._id || userPackageData.value._id, gradeGroup: grades.map((e) => e.key || e.value)}
  if (topic) {
    query.topic = [topic]
  }
  await App.service('service-conf')
    .get('teachersByPack', {query})
    .then((res) => {
      if (res?.data?.length) {
        doBook(item, topic)
      } else {
        $q.dialog({
          title: 'Message',
          message: 'Classcipe currently has no available teachers, please be patient ......',
          ok: {
            label: 'I got it ',
            color: 'primary',
            rounded: true,
            'no-caps': true,
          },
        })
      }
    })
  $q.loading.hide()
}

const doBook = (item, topic) => {
  let path = `/booking/leave-a-message`
  let query = {packId: userPackageData.value._id}
  if (userPackageData.value.session) query.bindingCourseId = userPackageData.value.session._id
  if (route.query.bindingCourseId) query.bindingCourseId = route.query.bindingCourseId

  if (item) {
    query.packId = item.premium._id
    query.pid = id.value
  } else {
    if (['interview', 'interviewTeacher'].includes(packageData.value.consultant?.type)) {
      path += '/interview'
      query.interviewId = userPackageData.value.snapshot._id
      if (packageData.value.consultant.type == 'interviewTeacher') {
        query.teacher = 1
      }
    }
  }
  if (topic) {
    query.topic = topic
  }
  router.push({
    path,
    query,
  })
}

const buyLink = () => {
  const serviceType = packageData.value?.serviceRoles === 'substitute' ? 'service_substitute' : 'service'
  const query = {
    back: route.query.back,
    inviteCode: route?.query?.inviteCode,
    inviteSource: route?.query?.inviteSource,
    inviteSourceId: route?.query?.inviteSourceId,
    schoolInviter: route?.query?.schoolInviter,
  }

  return {
    path: `/order/confirm/${serviceType}/${packageData.value._id}`,
    query,
  }
}

const getServicePackUser = async () => {
  try {
    const rs = await App.service('service-pack-user').get(id.value)
    console.log('getServicePackUser', rs)
    const rs2 = await App.service('service-pack-user').find({query: {pid: id.value}})
    console.log('rs2.data.length', rs2.data.length)
    if (rs2.data.length) {
      // Filter all items that have type === 'serviceTask'
      const serviceTasks = rs2.data.filter((item) => item?.snapshot?.type === 'serviceTask')
      if (serviceTasks.length > 0) {
        associatePackUserIds.value = serviceTasks.map((task) => task._id)
        associatedTasks.value = serviceTasks.map((task) => task.snapshot)
        console.log('rs2 serviceTasks count:', serviceTasks.length)
        console.log('rs2 values:', associatePackUserIds.value)
      }
    }
    userPackageData.value = rs
    packageData.value = rs.snapshot
    if (rs.session) {
      try {
        session.value = await App.service('session').get(rs.session._id || rs.session)
      } catch (e) {
        console.warn(e)
      }
    }
  } catch (e) {
    unavailable.value = true
    $q.dialog({
      component: PromptPage,
      componentProps: {
        fullscreen: false,
        logo: false,
        title: 'The product doese not exist',
      },
    })
  }
}

const getPayMethods = async () => {
  const ids = []
  if (packageData.value.contentOrientatedEnable) {
    ids.push(...packUserIds.value)
  } else {
    ids.push(id.value)
  }

  await App.service('service-pack-user')
    .get('checkFree', {query: {ids}})
    .then((res) => {
      if (res?.res) {
        freePayMethods.value = res.res
      }
    })
}

const getContentOrientated = async () => {
  const data = packageData.value
  const premiumIds = data?.contentOrientated.map((e) => e.premium)
  const servicePackIds = data?.contentOrientated.map((e) => e.servicePack)

  if (premiumIds?.length) {
    let rs

    const query = {pid: id.value}
    if (isAdmin.value) {
      query.$isSchool = true
    }
    rs = await App.service('service-pack-user').find({query})
    if (rs?.data?.length) {
      packUserIds.value.push(...rs.data.map((e) => e._id))
    }

    console.log('rs', rs)

    rs?.data?.map((f) => {
      const content = data.contentOrientated.find((e) => f.premium == e.premium)
      const item = {
        ...content,
        ...{
          premium: f,
        },
      }

      item.premium.snapshot = {...item.premium.snapshot, ...{duration: userPackageData.value.snapshot.duration, break: userPackageData.value.snapshot.break}}

      list.value.push(item)
    })
  }

  if (servicePackIds?.length) {
    let rs = await App.service('service-pack-user').find({query: {uid: pub.user._id, 'snapshot._id': {$in: servicePackIds}}})
    if (rs?.data?.length) {
      packUserIds.value.push(...rs.data.map((e) => e._id))
    }

    const associatedTaskIds = await App.service('service-pack-user').find({
      query: {uid: pub.user._id, 'snapshot.type': 'serviceTask', pid: {$in: packUserIds.value}},
    })
    if (associatedTaskIds?.data?.length) {
      packUserIds.value.push(...associatedTaskIds.data.map((e) => e._id))
    }
    list.value.map((e) => {
      const pack = rs.data?.find((f) => f.snapshot._id == e.servicePack)
      if (!list.value.some((f) => f.servicePack?._id == pack?._id)) {
        e.servicePack = pack
      }
      const associatePack = associatedTaskIds.data?.find((f) => f.pid == pack?._id)
      if (associatePack) {
        e.associateTask = associatePack
      }
    })
  }

  if (data?.carerPack?._id) {
    let carerPackInfo
    const rs = await App.service('service-pack-user').find({query: {uid: pub.user._id, 'snapshot._id': data.carerPack._id}})
    if (rs?.data?.length) {
      packUserIds.value.push(...rs.data.map((e) => e._id))
    }
    if (rs.data?.length) {
      carerPackInfo = rs.data[0]
    }

    if (carerPackInfo) {
      data.carerPackInfo = carerPackInfo
    }
  }

  list.value.map((e) => {
    e.discountConfig = data?.discountConfig
    e.type = 'lecture'
    e.splitSale = data.splitSale
    e.choose = true
  })

  if (data?.carerPackInfo) {
    list.value.push({
      ...data?.carerPackInfo,
      type: 'carer',
      times: data.carerPack.times,
    })
  }
  console.log('list', list.value)
}

const getOnCampusUser = async () => {
  const rs = await App.service('service-pack-user').find({
    query: {
      $isSchool: true,
      pid: id.value,
    },
  })
  onCampusList.value = rs.data || []
}

const getSubstituteDetail = async () => {
  if (packageData.value?.serviceRoles === 'substitute' && route.query.subId) {
    substituteDetail.value = await App.service('session').get(route.query.subId)
  }
}

const toSetParticipants = (item) => {
  return {path: `/premcpack/participants/${item.premium?._id}`, query: {packId: packageData.value?._id, lectureId: item.premium?.premium}}
}

onMounted(async () => {
  loading.value = true
  await getServicePackUser()

  if (isSubOnCampus.value) {
    await getOnCampusUser()
  }

  if (packageData.value.contentOrientatedEnable) {
    await getContentOrientated()
  }

  await getPayMethods()
  loading.value = false
  getSubstituteDetail()
})
</script>
