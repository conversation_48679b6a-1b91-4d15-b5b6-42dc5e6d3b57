<template>
  <q-layout view="hHh LpR fFf">
    <PubHeader :showShare="!(isAdmin && isSchool) && pub?.user?.id" :link="shareLink" isShareDetail />
    <q-page-container class="pc-sm explicit-page-container bg-white">
      <q-page class="column overflow-hidden" v-if="loading">
        <div class="col text-center q-pa-xl text-grey">
          <q-spinner-ball color="primary" size="2em" class="full-width" />
        </div>
      </q-page>
      <q-page v-else>
        <div class="q-pa-md">
          <BreadCrumbs title="Preview" />
          <template v-if="isContentOrientated">
            <ContentOrientated :item="packageData" :back="goBack" :readonly="readonly" />
          </template>
          <Substitute v-else-if="isSub" :item="packageData" :back="goBack" :readonly="readonly" />
          <Main v-else :item="packageData" :back="goBack" :readonly="readonly" :associatedTask="associatedTask" />
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import {ref, onMounted, inject, watch, computed, onUnmounted, openBlock} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import useSchool from 'src/composables/common/useSchool'
import useSubject from 'src/composables/account/academic/useSubject'
import PromptPage from 'components/PromptPage.vue'
import {date} from 'quasar'
import {pubStore} from 'stores/pub'
import {pointStore} from 'stores/point'
import BreadCrumbs from 'components/BreadCrumbs.vue'
import Main from './main.vue'
import Substitute from './substitute.vue'
import ContentOrientated from './contentOrientated.vue'
/*
  consts
*/

const pub = pubStore()
const route = useRoute()
const router = useRouter()
const id = ref('')
// init data PackageChips
const {sysMap} = useSubject()
const {isLogin, isAdmin, isStudent, isSchool, schoolId} = useSchool()

const isPointMode = ref(null)
const pStore = pointStore()

const packageData = ref({})
const loading = ref(false)
const shareLink = ref(null)
// readonly mode before is limit
const readonly = ref(null)
const associatedTask = ref(null)

/*
  computeds
*/

const isSub = computed(() => {
  return packageData.value?.serviceRoles === 'substitute'
})

const isContentOrientated = computed(() => {
  return packageData.value.contentOrientatedEnable
})

/*
  watches
*/

watch(
  () => route.path,
  () => {
    console.log('route.path', route.path)
    packageData.value = {}
    init()
  }
)

/*
  methods
*/

const goBack = () => {
  if (route.query.back) {
    router.replace(route.query.back)
  } else {
    router.go(-1)
  }
}

const getServicePack = async () => {
  const res = await App.service('service-pack').get(id.value)
  if (res?.associatedTask?._id) {
    associatedTask.value = await App.service('service-pack').get(res.associatedTask._id)
    console.log('associatedTask', associatedTask.value)
  }
  packageData.value = res ?? {}
}

const isSchoolAdmin = () => {
  return pub.schoolUserList?.some((e) => e.school == route.query.school && e.role?.includes('admin'))
}

const init = async () => {
  id.value = route.params.id
  isPointMode.value = !!route?.query?.isPointMode
  readonly.value = route.query.type == 'view'

  console.log('init')
  loading.value = true
  // if (route.query.school) {
  //   if (!isSchoolAdmin()) {
  //     $q.dialog({
  //       component: PromptPage,
  //       componentProps: {
  //         fullscreen: true,
  //         type: 'unauthorized',
  //         title: 'Link no longer available!',
  //       },
  //     })
  //     return
  //   }
  // }

  if (isPointMode.value) {
    await pStore.getClaimSetting()
  }

  await getServicePack()

  if (!packageData.value?.status) {
    $q.dialog({
      component: PromptPage,
      componentProps: {
        fullscreen: true,
        type: 'unauthorized',
        title: 'The product you chose has been unpublished.',
      },
    })
    return
  }

  shareLink.value = location.origin + '/v2' + route.matched[1]?.path?.replace(':id', packageData.value._id)
  if (isAdmin.value && schoolId.value) {
    router.replace({query: {school: schoolId.value, ...route.query}})
  }

  loading.value = false
}

onMounted(async () => {
  init()
})
</script>
