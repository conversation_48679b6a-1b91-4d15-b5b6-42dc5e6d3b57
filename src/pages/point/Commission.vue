<template>
  <q-layout view="hHh LpR fFf" class="bg-white">
    <Header />
    <q-page-container class="pc-md page-box">
      <q-page class="q-pa-md">
        <q-breadcrumbs>
          <q-breadcrumbs-el label="Account" to="/account/info" />
          <q-breadcrumbs-el label="Invitation history" class="text-grey-6" />
        </q-breadcrumbs>
        <div class="text-h6 q-mt-sm q-mb-md">Invitation history</div>
        <PointTab />
        <!-- <div class="text-h6 q-my-md">Points</div> -->

        <q-card class="bg-green-1 q-pa-md q-my-md flex justify-between items-center">
          <div class="flex items-center">
            <q-icon class="q-mr-md" color="green" name="o_payments" size="md" />
            <div>Earn additional commission as a school ambassador</div>
          </div>
          <q-btn label="Learn more" no-caps class="bg-teal-4 text-white" outline rounded @click="() => onAmbassadorClick()"></q-btn>
        </q-card>

        <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1 row items-center">
          <q-avatar size="3rem"><img src="~assets/img/point/ic_cash.png" /></q-avatar>
          <div class="text-h6 q-ml-md">My cash</div>
          <div class="text-primary text-h6">
            <span class="text-blue text-subtitle1 q-ml-lg q-mr-sm">Actual</span><span class="text-bold">USD {{ (pStore?.myBalance / 100).toFixed(2) }}</span>
          </div>
          <div class="text-primary text-h6">
            <span class="text-orange text-subtitle1 q-ml-lg q-mr-sm">Expected</span
            ><span class="text-bold">USD {{ ((pStore?.commissionCount?.expected_amount || 0) / 100).toFixed(2) }}</span>
          </div>
        </div>
        <div class="rounded-borders-sm q-mt-md q-border-1 q-pa-md bg-white shadow-1">
          <div class="row justify-between">
            <div class="text-h6">Cash balance</div>
            <div class="col-2 q-ml-xl">
              <Filters class="" v-model="filters" :options="filterOptions" @update:modelValue="resetFn" />
            </div>
          </div>
          <DetailView ref="child" :load="handleLoad" showMore class="q-mt-lg">
            <div>
              <div class="row text-grey-6">
                <div class="col-3">Earning method/Claimed</div>
                <div class="col-2">Cash(USD)</div>
                <div class="col-2">Status</div>
                <div class="col-3">Time</div>
                <div class="col-2">Balance(USD)</div>
              </div>
              <div class="row q-py-sm" v-for="item in child.data" :key="item._id">
                <div class="col-3 text-primary text-capitalize cursor-pointer" @click="toDetail(item)">{{ productLabel(item) }}</div>
                <div class="col-2" :class="item?.value > 0 ? 'text-positive' : 'text-negative'">
                  {{ item?.value > 0 ? '+' : '' }}
                  {{ (item?.value / 100).toFixed(2) }}
                </div>
                <div class="col-2">
                  <span class="text-orange" v-if="item?.status == 0">Expected</span>
                  <span class="text-blue" v-if="item?.status == 1">Actual</span>
                </div>
                <div class="col-3">{{ date.formatDate(item.createdAt, 'MM/DD/YYYY HH:mm:ss') }}</div>
                <div class="col-2">{{ (item?.total / 100).toFixed(2) }}</div>
                <div class="col-12">
                  <q-separator class="q-mt-sm" />
                </div>
              </div>
            </div>
            <template v-slot:noData>
              <NoData message="No commission yet!" messageColor="text-grey" />
              <div class="text-center">
                <div class="text-grey-6">
                  There are no commission. <br />
                  Learn how to earn commission?
                </div>
                <q-btn class="q-mt-md" @click="earnDialog = true" rounded color="teal" no-caps label="How to earn"></q-btn>
              </div>
            </template>
          </DetailView>
        </div>
        <q-dialog v-model="earnDialog">
          <q-card class="q-pa-md" style="width: 342px">
            <q-card-section>
              You can earn points by inviting new user to register successfully.<br /><br />
              You can earn points or commission by recommending user to subscribe SAAS tools.<br /><br />
              You can earn points or commission by recommending user to purchase products or services.
            </q-card-section>
            <q-btn flat color="primary" label="more" no-caps to="/point/earn" />
            <q-card-actions>
              <q-btn icon="done" class="q-mt-md full-width" rounded color="teal" no-caps label="I got it" v-close-popup></q-btn>
            </q-card-actions>
          </q-card>
        </q-dialog>
      </q-page>
    </q-page-container>
  </q-layout>
</template>
<script setup>
/*
  imports
*/
import {onMounted, ref, computed} from 'vue'
import {useRouter} from 'vue-router'
import {pointStore} from 'stores/point'
import DetailView from 'components/DetailView.vue'
import {date} from 'quasar'
import Header from './Header.vue'
import PointTab from './PointTab.vue'
import {productLabel, commissionFilterOptions} from './consts'
import Filters from './components/Filters.vue'
import useParser from 'src/composables/utils/useParser'

const pStore = pointStore()

const router = useRouter()
const {parsePathToHref} = useParser()
const child = ref(null)
const earnDialog = ref(false)
const skip = ref(0)
const filters = ref({
  status: [0, 1],
  category: [
    'refund',
    'order',
    'self_study',
    'service',
    'points_purchase',
    'invite',
    'session',
    'service_substitute',
    'saas_tool_trail',
    'saas_tool_paid',
    'service_correct',
    'service_premium',
    'verify',
    'unit',
    'task',
    'bonus',
  ],
})

const filterOptions = computed(() => {
  return commissionFilterOptions.map((item) => {
    item.list = item.list.map((opt) => {
      if (item.value == 'status' && opt.value == 0) {
        opt.count = pStore?.commissionCount?.expected_count || 0
        opt.count = opt.count > 100 ? '100+' : opt.count
      }
      if (item.value == 'status' && opt.value == 1) {
        opt.count = pStore?.commissionCount?.actual_count || 0
        opt.count = opt.count > 100 ? '100+' : opt.count
      }
      if (item.value == 'category') {
        opt.count = pStore?.commissionCount[opt.value] || 0
        opt.count = opt.count > 100 ? '100+' : opt.count
      }
      return opt
    })
    return item
  })
})

const onLoad = async () => {
  child.value.onLoad()
}

const handleLoad = async () => {
  let filterData = Acan.clone(filters.value)
  const $limit = 10
  let query = {
    $limit,
    $skip: skip.value,
    $sort: {createdAt: -1},
    createdAt: {$gt: Date.now() - 365 * 24 * 60 * 60 * 1000},
    status: {$in: filterData.status},
    category: {$in: filterData.category},
  }

  skip.value += $limit
  return App.service('commission-log').find({query})
}

const toDetail = (item) => {
  router.push(`/point/commission/${item._id}`)
}

const getPoint = async () => {
  pStore.getCommission()
}

const main = () => {
  getPoint()
  onLoad()
}

const resetFn = () => {
  skip.value = 0
  // handleLoad()
  child.value.onReset()
  child.value.onLoad()
}

const url = ref('/account/ambassador-verification')
function onAmbassadorClick() {
  window.open(parsePathToHref(url.value), '_blank')
}

onMounted(main)
</script>
<style lang="sass" scope>

.page-box
  padding-left: 0 !important
</style>
