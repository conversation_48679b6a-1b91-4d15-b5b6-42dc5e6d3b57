<template>
  <div class="column">
    <q-list padding class="rounded-borders relative-position">
      <q-item v-ripple>
        <q-item-section class="row">
          <q-btn icon="add" label="Add Target" @click="showFn" no-caps />
          <!-- <q-btn icon="settings_suggest" to="target/tree" no-caps></q-btn> -->
        </q-item-section>
      </q-item>
      <q-item-label header>Learning outcomes</q-item-label>
      <q-item clickable v-ripple :to="'target/edit?index=' + i" v-for="(o, i) in addon.targets" :key="i">
        <q-badge class="q-ma-xs" color="orange" floating>Unused</q-badge>
        <q-item-section>
          <q-item-label lines="3">{{ o.text }}</q-item-label>
          <q-item-label caption>
            <div class="row">
              <q-btn class="col-4 q-mr-sm" color="white" text-color="orange-9" size="sm" label="Verb" no-caps>
                <q-badge color="orange" floating text-color="black" :label="o.verb?.length" />
              </q-btn>
              <q-btn class="col-4" color="white" text-color="teal-9" size="sm" label="Tags" no-caps v-if="o.tags?.length > 0">
                <q-badge color="orange" floating text-color="black" :label="o.tags?.length" />
              </q-btn>
            </div>
          </q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-icon name="arrow_forward_ios" />
        </q-item-section>
      </q-item>
      <q-inner-loading :showing="loading" label-class="text-teal" label-style="font-size: 1.1em" />
    </q-list>
    <TargetTree v-show="show" />
  </div>
</template>

<script>
import {ref} from 'vue'
import {defineComponent} from 'vue'

import {addonStore} from 'stores/addon'
import {targetsStore} from 'stores/targets'
import TargetTree from 'components/addon/TargetTree.vue'

export default defineComponent({
  components: {TargetTree},
  provide() {
    return {
      close: this.showFn,
    }
  },
  setup() {
    const addon = addonStore()
    const targets = targetsStore()
    return {ids: ref([]), addon, targets, show: ref(false), loading: ref(true)}
  },
  async mounted() {
    await this.addon.getOutlines()
    this.loading = false
  },
  methods: {
    showFn() {
      this.show = !this.show
      if (this.show) return
      this.addon.targets.map((v) => {
        if (!this.ids.includes(v.id)) this.ids.push(v.id)
      })
    },
  },
})
</script>
