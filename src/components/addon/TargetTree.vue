<template>
  <div class="column full-width absolute-full bg-white">
    <q-toolbar class="bg-secondary text-white shadow-2">
      <div class="col">
        Choose outline
        <q-badge class="q-ma-xs" color="orange">{{ selects.length }}</q-badge>
      </div>
      <q-btn flat dense icon="done" @click="doneFn" label="Done" :loading="loading" />
    </q-toolbar>
    <template v-if="isSearch">
      <q-input ref="tergetSearch" class="q-pt-sm q-px-sm" bottom-slots v-model="text" label="Search" dense :autofocus="true">
        <template v-slot:before>
          <q-icon name="arrow_back" @click="isSearch = false" />
        </template>
        <template v-slot:append>
          <q-icon v-if="text !== ''" name="close" @click="text = ''" class="cursor-pointer" />
          <q-icon name="search" />
        </template>
      </q-input>
      <q-list dense bordered separator padding class="rounded-borders col overflow-auto">
        <template v-for="(o, i) in searchList" :key="i">
          <q-item style="padding: 0 0.5rem" clickable v-ripple v-if="!(key.length === 1 && i === '__year')" @click="o.id ? '' : key.push(i), changeFn()">
            <q-item-section>
              <template v-if="o.id">
                <q-checkbox v-model="selects" :val="o" @click="changeFn" :label="o.desc" :left-label="true" />
              </template>
              <template v-else>{{ i }}</template>
            </q-item-section>
            <q-item-section side v-if="!o.id">
              <q-icon name="navigate_next" />
            </q-item-section>
          </q-item>
        </template>
      </q-list>
    </template>
    <template v-if="!isSearch">
      <q-toolbar class="q-pl-none q-pr-sm" v-if="key.length > 0">
        <q-btn flat round dense icon="navigate_before" class="q-mr-sm" @click="key.pop()" />
        <div class="col ellipsis">
          {{ key[key.length - 1] }}
          <q-tooltip>{{ key[key.length - 1] }}</q-tooltip>
        </div>
        <q-checkbox v-if="list?.[0]?.id" v-model="selectAll" @click="changeAll" />
        <q-btn v-else flat dense icon="search" @click="openSearchFn" />
      </q-toolbar>
      <div class="" bordered separator v-if="targets.data && key.length === 0">
        <q-select
          class="col items-start"
          color="teal-9"
          multiple
          label="Subjects"
          use-chips
          filled
          autofocus
          v-model="subjects"
          :options="targets.data.__subject" />
        <q-select class="col items-start" color="teal-9" multiple label="Years" use-chips filled autofocus v-model="years" :options="targets.data.__years" />
      </div>
      <q-list dense bordered separator padding class="rounded-borders col overflow-auto">
        <q-item-label header>Choice outlines</q-item-label>
        <template v-for="(o, i) in list" :key="i">
          <q-item style="padding: 0 0.5rem" clickable v-ripple v-if="!(key.length === 1 && i === '__year')" @click="o.id ? '' : key.push(i), changeFn()">
            <q-item-section>
              <template v-if="o.id">
                <q-checkbox v-model="selects" :val="o" @click="changeFn" :label="o.desc" :left-label="true" />
              </template>
              <template v-else>{{ i }}</template>
            </q-item-section>
            <q-item-section side v-if="!o.id">
              <q-icon name="navigate_next" />
            </q-item-section>
          </q-item>
        </template>
      </q-list>
    </template>
  </div>
</template>

<script>
import {ref} from 'vue'
import {useRoute} from 'vue-router'
import {addonStore} from 'stores/addon'
import {targetsStore} from 'stores/targets'
export default {
  inject: ['close'],
  setup() {
    const addon = addonStore()
    const route = useRoute()
    const {index} = route.query
    return {
      loading: ref(false),
      isSearch: ref(false),
      text: ref(''),
      key: ref([]),
      selectAll: ref(false),
      selects: ref([]),
      years: ref([]),
      subjects: ref([]),
      index,
      targets: ref({}),
      addon,
    }
  },
  computed: {
    list() {
      const obj = this.key.length > 0 ? Acan.objGet(this.targets.data, this.key.join('.'), {}) : this.targets.data
      if (!obj) return
      let list
      if (Array.isArray(obj)) {
        list = obj.map((v) => {
          v.key = this.key
          return v
        })
      } else {
        list = {}
        let isSubject = this.key.length === 0 && this.subjects.length > 0
        let isYear = this.key.length === this.targets.data.__year && this.years.length > 0
        for (const key of Object.keys(obj)) {
          if (['__subject', '__years', '__year'].includes(key)) continue
          if (isSubject && !this.subjects.includes(key)) continue
          if (isYear && !this.years.includes(key)) continue
          list[key] = obj[key]
        }
      }
      return list
    },
    searchList() {
      if (!this.text) return []
      return this.targets.search(this.targets.data[this.key[0]], null, this.years, this.text)
    },
  },
  methods: {
    async openSearchFn() {
      this.isSearch = true
      await this.$forceUpdate()
      this.$refs.tergetSearch.focus()
    },
    changeAll() {
      this.list.map((o) => {
        if (this.selectAll) {
          if (this.selects.includes(o)) return
          this.selects.push(o)
        } else {
          this.selects.splice(this.selects.indexOf(o), 1)
        }
      })
    },
    changeFn() {
      if (!this.list?.[0]?.id) return
      let i = 0
      this.list.map((o) => {
        if (this.selects.includes(o)) return
        i++
      })
      console.log(this.selects, 'changeFn')
      this.selectAll = i === 0
    },
    async doneFn() {
      if (Acan.isEmpty(this.selects)) return this.close()
      this.loading = true
      const list = []
      this.selects.map((v) => {
        list.push({text: v.desc, md5: v.id, code: v.code, path: v.key.join('.')})
      })
      await this.addon.subAddTarget(list)
      this.loading = false
      this.close()
    },
    async getExt() {
      const rs = await App.service('outlines').get('oldExt', {query: {task: this.addon.task}})
      this.years = rs.years
      this.subjects = rs.subjects
      console.log(rs)
    },
  },
  async mounted() {
    await this.getExt()
    const targets = targetsStore()
    await targets.getData()
    this.targets = targets
    console.log(targets.data)
  },
  created() {},
}
</script>
