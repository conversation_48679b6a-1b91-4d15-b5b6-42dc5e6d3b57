<template>
  <div class="column">
    <q-inner-loading :showing="loading" label-class="text-teal" label-style="font-size: 1.1em" />
    <div class="row justify-between items-center q-pa-sm q-mt-md">
      <div class="col-4">Bloom</div>
      <q-slider
        class="col-8 q-px-sm"
        :color="BloomColors[form.bloom]"
        markers
        label
        label-always
        track-size="0.5rem"
        v-model="form.bloom"
        :min="0"
        :max="5"
        :label-value="BloomLabels[form.bloom] ?? 'please choose'"
        @update:model-value="subUpFn('bloom')" />
    </div>
    <div class="row justify-between items-center q-pa-sm">
      <div>KnowledgeDimensions</div>
      <q-slider
        class="col-5 q-px-sm"
        :color="'cyan-' + (form.dimension * 2 + 3)"
        markers
        label
        label-always
        track-size="0.5rem"
        v-model="form.dimension"
        :min="0"
        :max="3"
        :label-value="KnowledgeLabels[form.dimension] ?? 'please choose'"
        @update:model-value="subUpFn('dimension')" />
    </div>

    <q-select
      color="orange-9"
      multiple
      use-chips
      clearable
      label="Command term:"
      class="truncate-chip-labels q-pl-sm"
      use-input
      @input-value="(v) => (search.verb = v)"
      v-model="form.verb"
      :options="termsFilter"
      @update:model-value="subUpFn('verb')"
      behavior="dialog">
    </q-select>
    <q-select
      color="teal-9"
      multiple
      use-chips
      clearable
      label="Knowledge tag"
      class="addon-chip-labels q-pl-sm"
      use-input
      @input-value="(v) => (search.tag = v)"
      v-model="form.tags"
      :options="tagsFilter"
      @update:model-value="subUpFn('tags')"
      behavior="dialog">
    </q-select>
    <div class="q-pa-lg"></div>
  </div>
</template>
<style>
.addon-chip-labels .q-chip,
.q-select__dialog .q-chip {
  white-space: pre-wrap;
  word-break: break-word;
  height: auto;
  padding: 0.2rem 0.3rem;
}
.q-chip__content,
.q-chip__content .ellipsis {
  flex-wrap: wrap;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
<script setup>
import {ref, computed, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
import {addonStore} from 'stores/addon'
const addon = addonStore()
import {pubStore} from 'stores/pub'
const pub = pubStore()

const defTarget = {text: '', verb: [], tags: []}
const props = defineProps(['type'])
const {index} = route.query
const form = ref({}),
  loading = ref(false)
const search = ref({verb: '', tag: ''})

const termsFilter = computed(() => {
  const key = search.value.verb.toLowerCase()
  if (!key) return pub.terms
  return pub.terms.filter((v) => v.toLowerCase().includes(key))
})
const tagsFilter = computed(() => {
  const key = search.value.tag.toLowerCase()
  if (!key) return pub.tags
  return pub.tags.filter((v) => v.toLowerCase().includes(key))
})

function rmFn(o, val) {
  console.log(o, val)
}

async function subUpFn(key) {
  if (!addon.question._id) {
    return console.log(addon.question)
  }
  console.log({[key]: form.value[key]}, 'subUp')
  loading.value = true
  await addon.upQuestion({[key]: form.value[key]})
  loading.value = false
}

onMounted(async () => {
  form.value = addon.question
  if (!addon.question._id) form.value.type = props.type
  pub.getTerms()
  pub.getTags()
})
</script>
