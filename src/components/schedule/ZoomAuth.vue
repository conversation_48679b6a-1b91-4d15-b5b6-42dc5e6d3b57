<template>
  <div class="flex column">
    <div class="flex row items-center justify-between q-px-md q-py-sm">
      <q-img src="~/assets/img/zoom.png" style="height: 30px; width: 75px"></q-img>
      <q-toggle :disable="props.disable || !canCloseZOOM" v-model="defaultOpenZOOM"></q-toggle>
    </div>
    <div v-if="defaultOpenZOOM">
      <q-field :rules="[() => zoomChecked || 'Zoom account not authorized.']" v-if="!zoomChecked" borderless>
        <q-btn color="negative" icon="warning" no-caps class="q-mt-sm" @click="authZoomAcc">Link your Zoom account to schedule a Zoom meeting</q-btn>
      </q-field>
      <div class="q-py-sm q-px-md q-mt-sm text-teal-4 text-weight-medium" v-else>
        <div>The link to this meeting will be generated automatically when you schedule it</div>
      </div>
      <template v-if="!noSetting">
        <q-separator inset></q-separator>
        <q-expansion-item>
          <template v-slot:header>
            <q-item-section class="text-bold"> Zoom meeting setting </q-item-section>
          </template>
          <div class="flex column">
            <div class="flex column">
              <q-item class="hidden">
                <q-item-section>
                  <div class="flex row items-center q-gutter-x-sm">
                    <q-item-label>Passcode</q-item-label>
                    <q-icon name="help" size="14px">
                      <q-tooltip max-width="260px">
                        You can enable a Zoom passcode to secure who joins your meetings. Once enabled, your Zoom meeting will automatically include a passcode
                        same as your session code
                      </q-tooltip>
                    </q-icon>
                  </div>
                </q-item-section>
                <q-item-section avatar>
                  <q-toggle :disable="props.disable" v-model="passwordRef" />
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <div class="flex row items-center q-gutter-x-sm">
                    <q-item-label>Waiting room</q-item-label>
                    <q-icon name="help" size="14px">
                      <q-tooltip max-width="260px"> The Waiting Room feature allows the host to control when a participant joins the meeting. </q-tooltip>
                    </q-icon>
                  </div>
                </q-item-section>
                <q-item-section avatar>
                  <!--
                  <q-toggle :disable="props.disable" v-model="waitingRoomRef" />
                    -->
                  <q-toggle disable v-model="waitingRoomRef" />
                </q-item-section>
              </q-item>
            </div>
          </div>
        </q-expansion-item>
      </template>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, onMounted, watch} from 'vue'
import {pubStore} from 'stores/pub'

const pub = pubStore()

const props = defineProps({
  modelValue: Object,
  disable: Boolean,
  canClose: Boolean,
  noSetting: Boolean,
  openDefault: Boolean,
})

const zoomChecked = ref(false)
const defaultOpenZOOM = ref(false)
const meetingCapacity = ref(0)

//const defaultOpenZOOM = computed(() => props.openDefault)
const canCloseZOOM = computed(() => props.canClose)

watch(
  () => props.openDefault,
  (first, second) => {
    defaultOpenZOOM.value = first
  }
)

onMounted(async () => {
  const rs = await App.service('zoom-meet').get('check')
  if (rs && rs.ext?.meeting_capacity) {
    zoomChecked.value = true
    meetingCapacity.value = rs.ext.meeting_capacity
  }
  defaultOpenZOOM.value = props.openDefault
  update()
})

function authZoomAcc() {
  const authWin = window.open(
    `/fio/zoom/auth?uid=${pub.user._id}`,
    'zoomAuth',
    'width=900,height=600,menubar=yes,resizable=yes,scrollbars=true,status=true,top=100,left=200'
  )
  const authId = setInterval(async () => {
    if (!authWin.closed) {
      return
    }
    clearInterval(authId)
    const rs = await App.service('zoom-meet').get('check')
    if (rs && rs.ext?.meeting_capacity) {
      zoomChecked.value = true
      meetingCapacity.value = rs.ext.meeting_capacity
    }
    update()
    console.log('zoom auth', zoomChecked.value)
  }, 100)
}

const emit = defineEmits(['update:modelValue'])

const passwordRef = ref(props.modelValue.passcode)
const waitingRoomRef = ref(props.modelValue.waiting_room)

watch(
  () => props.modelValue.checked,
  (val) => {
    if (val && !defaultOpenZOOM.value) {
      defaultOpenZOOM.value = true
    }
  }
)

watch(
  () => passwordRef.value,
  (val) => {
    if (!val) {
      update()
      return
    }
    if (waitingRoomRef.value) {
      //waitingRoomRef.value = false
    } else {
      update()
    }
  }
)

watch(
  () => waitingRoomRef.value,
  (val) => {
    if (!val) {
      update()
      return
    }
    if (passwordRef.value) {
      passwordRef.value = false
    } else {
      update()
    }
  }
)

watch(
  () => defaultOpenZOOM.value,
  (val) => {
    update()
  }
)

const update = () => {
  emit('update:modelValue', {
    checked: zoomChecked.value && defaultOpenZOOM.value,
    passcode: passwordRef.value,
    capacity: meetingCapacity.value,
    invalid: defaultOpenZOOM.value && !meetingCapacity.value,
    waiting_room: waitingRoomRef.value,
    zoomChecked: zoomChecked.value,
    defaultOpenZOOM: defaultOpenZOOM.value,
  })
}
</script>

<style lang="scss" scoped>
.zoom-link {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}
</style>
