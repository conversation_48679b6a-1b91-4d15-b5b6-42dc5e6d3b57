<template>
  <div class="flex column">
    <div class="text-subtitle1 text-weight-bold">Zoom meeting setting</div>
    <div class="flex column">
      <q-item tag="label" :v-ripple="!props.disable">
        <q-item-section>
          <div class="flex row items-center q-gutter-x-sm">
            <q-item-label>Passcode</q-item-label>
            <q-icon name="help" size="14px">
              <q-tooltip class="text-body2" max-width="320px"
                >You can enable a Zoom passcode to secure who joins your meetings. Once enabled, your Zoom meeting will automatically include a passcode same as
                your session code</q-tooltip
              >
            </q-icon>
          </div>
        </q-item-section>
        <q-item-section avatar>
          <q-toggle :disable="props.disable" v-model="passwordRef" />
        </q-item-section>
      </q-item>
      <q-item tag="label" :v-ripple="!props.disable">
        <q-item-section>
          <div class="flex row items-center q-gutter-x-sm">
            <q-item-label>Waiting room</q-item-label>
            <q-icon name="help" size="14px">
              <q-tooltip class="text-body2" max-width="320px"
                >The Waiting Room feature allows the host to control when a participant joins the meeting.</q-tooltip
              >
            </q-icon>
          </div>
        </q-item-section>
        <q-item-section avatar>
          <q-toggle :disable="props.disable" v-model="waitingRoomRef" />
        </q-item-section>
      </q-item>
    </div>
  </div>
</template>

<script setup>
import {watch, ref, onMounted} from 'vue'

const props = defineProps({
  modelValue: Object,
  disable: Boolean,
})
const emit = defineEmits(['update:modelValue'])

const passwordRef = ref(props.modelValue.passcode)
const waitingRoomRef = ref(props.modelValue.waiting_room)

watch(
  () => passwordRef.value,
  (val) => {
    if (!val) {
      update()
      return
    }
    if (waitingRoomRef.value) {
      waitingRoomRef.value = false
    } else {
      update()
    }
  }
)

watch(
  () => waitingRoomRef.value,
  (val) => {
    if (!val) {
      update()
      return
    }
    if (passwordRef.value) {
      passwordRef.value = false
    } else {
      update()
    }
  }
)

onMounted(() => update())

function update() {
  emit('update:modelValue', {
    passcode: passwordRef.value,
    waiting_room: waitingRoomRef.value,
  })
}
</script>
