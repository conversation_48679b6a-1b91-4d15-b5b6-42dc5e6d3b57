<template>
  <div class="full" v-if="item">
    <div v-if="type === 'image'" class="full">
      <img :src="hashToUrl(item.key)" class="" />
    </div>

    <div v-else-if="type === 'video'" class="full">
      <!-- <VideoPlayer :url="hashToUrl(item.key)" :isPreviewBig="isPreviewBig" :controls="isDialog" /> -->
      <VideoPlayer :url="hashToUrl(item.key)" :isPreviewBig="isPreviewBig" :controls="true" />
    </div>

    <div v-else-if="['audio', 'mp3'].includes(type)" class="full flex flex-center">
      <AudioPlayer v-if="isPreviewBig" :url="hashToUrl(item.key)" :withMaskButton="true" :id="item._id" :isPreviewBig="isPreviewBig" />
      <div v-else class="column no-wrap flex-center">
        <q-icon name="music_video" size="5rem" color="grey-7" />
        <span v-if="item?.desc">{{ item.desc }}</span>
      </div>
    </div>

    <div v-else-if="type === 'youtube'" class="full">
      <Youtube :id="item.url" :auto="isPreviewBig" :start="item.ext?.start" :isPreviewBig="isPreviewBig" />
    </div>

    <div v-else-if="type === 'pdf'" class="full flex flex-center" style="line-height: initial">
      <PDFViewer v-if="isPreviewBig" isMaterial class="" :url="hashToUrl(item.key)" :item="item" frameborder="0" :isDialog="isDialog" />
      <div v-else class="column no-wrap">
        <q-icon name="picture_as_pdf" size="5rem" color="grey-7" />
        <span v-if="item?.desc">{{ item.desc }}</span>
      </div>
    </div>

    <div v-else class="full flex flex-center">
      <iframe v-if="isPreviewBig" class="" :src="hashToUrl(item.key)" frameborder="0" />
      <span v-else>{{ item.desc || item }}</span>
    </div>
  </div>
</template>

<script setup>
import {computed} from 'vue'
import Youtube from './layouts/Youtube.vue'
import AudioPlayer from './layouts/AudioPlayer.vue'
import VideoPlayer from './layouts/VideoPlayer.vue'
import PDFViewer from './layouts/PDFViewer.vue'

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  isPreviewBig: {
    type: Boolean,
    default: false,
  },
  isDialog: {
    type: Boolean,
    default: false,
  },
})

const type = computed(() => {
  if (props?.item?.mime) {
    if (props.item.mime.includes('image')) return 'image'
    else if (props.item.mime.includes('video')) return 'video'
    else if (props.item.mime.includes('audio')) return 'audio'
    else if (props.item.mime.includes('pdf')) return 'pdf'
    else return props.item.mime
  }
  if (props?.item?.type) return props.item.type
  return ''
})
</script>

<style scoped lang="scss">
// line-height: 0;
// overflow: hidden;
.full {
  width: 100%;
  height: 100%;
  position: relative;
  text-align: center;
  overflow: hidden;
}
.full img {
  height: 100%;
  object-fit: contain;
  max-width: 100%;
}
.icon-button {
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  .icon-play {
    color: #eee;
  }
  &:hover {
    background: rgba(0, 0, 0, 0.5);
    .icon-play {
      color: #fff;
      transform: scale(1.2);
    }
  }
}
</style>
