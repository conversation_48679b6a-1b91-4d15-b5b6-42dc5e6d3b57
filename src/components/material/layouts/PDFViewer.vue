<template>
  <div class="full-width full-height relative-position the-canvas-wrapper">
    <canvas v-if="!open_new_tab" :id="canvasId" class="the-canvas" :style="[isDialog ? '' : 'height: 100%']"></canvas>
    <div v-if="!open_new_tab" class="flex flex-center" style="transform: translateY(-5.5rem)">{{ isLoading ? 'Loading...' : 'No data' }}</div>
    <a v-if="open_new_tab" :href="url" target="_blank" rel="noopener noreferrer">
      <div class="bg-blue">
        <q-icon color="white" flat name="picture_as_pdf" size="3rem"></q-icon>
        <q-item-label class="text-white">
          {{ content.split(':')[content.split(':').length - 1] }}
        </q-item-label>
      </div>
    </a>
    <div v-if="!open_new_tab" class="pdf-controls flex items-center">
      <q-btn flat rounded dense icon="arrow_left" @click="pageNumber--" :disable="pageNumber === 1" />
      <span>{{ pageNumber }} / {{ maxPageNumber }}</span>
      <q-btn flat rounded dense icon="arrow_right" @click="pageNumber++" :disable="pageNumber === maxPageNumber" />
    </div>
  </div>
</template>

<script setup>
import {ref, computed, watch, onMounted} from 'vue'
import {currentFilesStore} from 'stores/materials/current-files'
import ConfirmDialog from 'src/components/utils/dialogs/ConfirmDialog.vue'

const props = defineProps({
  url: {type: String, default: ''},
  item: {type: Object, requite: true},
  isDialog: {type: Boolean, default: false},
  content: {type: String, default: ''},
  open_new_tab: {type: Boolean, default: false}
})

const canvasId = computed(() => `canvas-${props.item.key}-${props.isDialog ? 'dialog' : 'normal'}`)
const currentFiles = currentFilesStore()
const isPdfFull = computed(() => currentFiles?.isPdfFull)
const isLoading = ref(true)

const {pdfjsLib} = globalThis
pdfjsLib.GlobalWorkerOptions.workerSrc = '/v2/plugin/pdf.worker.4.0.242.mjs'

const pageNumber = ref(1)
const maxPageNumber = ref(1)
const pdfDoc = ref(null)

onMounted(() => {
  if(props.open_new_tab) return
  pdfDoc.value = pdfjsLib.getDocument(props.url)
  loadPdf()
})
watch(pageNumber, loadPdf)

function loadPdf() {
  pdfDoc.value.promise.then(
    (pdf) => {
      maxPageNumber.value = pdf?.numPages ?? 0
      // Fetch the first page
      pdf.getPage(pageNumber.value).then(function (page) {
        var scale = 1.3
        var viewport = page.getViewport({scale: scale})

        if (isPdfFull.value) {
          scale = window.innerWidth / viewport.width
          viewport = page.getViewport({scale: scale})
        }

        // Prepare canvas using PDF page dimensions
        const canvas = document.getElementById(canvasId.value)
        console.log(canvasId.value)
        const context = canvas.getContext('2d')
        if (props.isDialog) {
          canvas.height = viewport.height
          canvas.width = viewport.width
        } else {
          canvas.height = viewport.height
          canvas.width = viewport.width
          // canvas.height = '100%'
          // canvas.width = '100%'
        }
        // Render PDF page into canvas context
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        }
        const renderTask = page.render(renderContext)
        renderTask.promise.then(function () {
          // console.log('Page rendered')
        })
      })
      isLoading.value = false
    },
    function (error) {
      if (error?.code === 1 || error?.message === 'No password given') {
        const title = 'This document is password-protected or corrupted, please contact the owner.'
        const message = ''
        const okButtonLabel = 'I got it'
        $q.dialog({
          component: ConfirmDialog,
          componentProps: {title, message, okButtonLabel, hasCancelButton: false},
        })
          .onOk(() => {})
          .onCancel(() => {})
          .onDismiss(() => {})
      } else {
        console.error(error)
        $q.notify({type: 'negative', message: error?.message || 'Error'})
      }
      isLoading.value = false
    }
  )
}
watch(isPdfFull, loadPdf)
</script>

<style lang="scss" scoped>
.the-canvas-wrapper {
  overflow-y: scroll;
  overflow-x: scroll;
}
.the-canvas {
  background: white;
  border: 1px solid black;
  direction: ltr;
}
.pdf-controls {
  position: fixed;
  bottom: -0.5rem;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  box-shadow: 1px 1px 3px 2px rgba(0, 0, 0, 0.15);
}
</style>
