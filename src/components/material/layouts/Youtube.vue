<template>
  <div class="full-width full-height flex flex-center relative-position" @click="playVideo()" style="cursor: pointer">
    <div class="flex flex-center" style="position: absolute">
      <q-btn flat size="md" class="flex flex-center icon-button">
        <i class="fa fa-play icon2 icon-play"></i>
      </q-btn>
    </div>
    <img v-if="id" class="full-width full-height" style="object-fit: contain; user-select: none" :src="youtubeIdThumb(id)" alt="" />
    <!-- <iframe class="flex1" style="border:0;margin:0;" frameborder="0" allowfullscreen :src="url"></iframe> -->
  </div>
</template>
<script setup>
import {roomStore} from 'stores/rooms'
import {currentFilesStore} from 'stores/materials/current-files'

const rooms = roomStore()
const currentFiles = currentFilesStore()

const props = defineProps({
  id: {type: String, default: ''},
  start: {type: Number, default: 0},
  auto: {type: Boolean, default: false},
  isPreviewBig: {type: Boolean, default: false},
})

const {youtubeIdThumb} = Fn

async function localPlay(o) {
  setSyncUI({fullPlayPaused: false, fullPlayVolume: 0, fullPlayOn: '', ...o})
}
async function setSyncUI(o) {
  // if (rooms.session.status === 'live') await App.service('auth').patch('syncUI', o)
}

function playVideo() {
  if (!props.isPreviewBig) return
  currentFiles.youtubeUrl = `https://www.youtube.com/embed/${props.id}?autoplay=1&start=${props.start}`
  currentFiles.isMaterialShowMap.youtube = true
}
</script>

<style scoped lang="scss">
.icon-button {
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  .icon-play {
    color: #eee;
  }
  // &:hover {
  & {
    background: rgba(0, 0, 0, 0.25);
    .icon-play {
      color: #fff;
      transform: scale(1.2);
    }
  }
  // }
}
</style>
