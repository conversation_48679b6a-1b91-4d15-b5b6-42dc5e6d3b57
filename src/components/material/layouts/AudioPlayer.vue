<template>
  <div class="full-width full-height flex flex-center" style="min-height: 4rem">
    <audio
      :src="hashToUrl(url)"
      preload="meta"
      :controls="controls"
      controlslist="nodownload"
      playsinline
      style="width: 100%"
      :style="isCommentTakeaway ? ($q.screen.lt.sm ? {minWidth: '150px'} : {minWidth: '300px'}) : {}">
      <source :src="url" type="video/mp3" />
      <source :src="url" type="audio/mp3" />
      <source :src="url" type="audio/mpeg" />
      <source :src="url" type="audio/ogg" />
      Your browser does not support the audio element.
    </audio>
  </div>
</template>

<script setup>
const props = defineProps({
  url: {
    type: String,
    require: true,
  },
  controls: {type: Boolean, default: true},
  isPreviewBig: {type: Boolean, default: false},
  isCommentTakeaway: {type: Boolean, default: false},
})
</script>
