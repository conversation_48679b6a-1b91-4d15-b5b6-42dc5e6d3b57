<template>
  <div class="full-width full-height flex flex-center relative-position" @click="playVideo()" style="cursor: pointer">
    <!-- <div v-if="!isPreviewBig" class="flex_c w100 h100" style="position: absolute; z-index: 50">
      <q-btn flat type="text" class="flex_c icon-button">
        <i class="fa fa-play icon2 icon-play"></i>
      </q-btn>
    </div> -->

    <!-- <video controlslist="nodownload" :src="url" preload="meta" muted /> -->
    <video
      class="full-width full-height"
      :style="isTakeaway ? {maxHeight: '550px'} : isCommentTakeaway ? {maxHeight: '130px', minWidth: '250px'} : {}"
      controlslist="nodownload"
      :controls="controls"
      preload="meta"
      ref="videoPlayer"
      playsinline>
      <source :src="url" type="video/mp4" />
    </video>
    <!-- <img v-if="id" class="full-width full-height" style="object-fit: contain; user-select: none" :src="youtubeIdThumb(id)" alt="" /> -->

    <!-- Video for pc browser -->
    <!-- <video -->
    <!--   v-if="!$q.platform.is.ios" -->
    <!--   class="full-width full-height" -->
    <!--   controls -->
    <!--   muted -->
    <!--   controlslist="nodownload" -->
    <!--   :src="url" -->
    <!--   preload="meta" -->
    <!--   ref="videoPlayer"></video> -->

    <!-- Video for ios safari -->
    <!-- <video class="full-width full-height" controls controlslist="nodownload" playsinline muted preload="meta" ref="videoPlayer"> -->
    <!--   <source :src="hashToUrl(response.content)" /> -->
    <!-- </video> -->
  </div>
</template>

<script setup>
import {useQuasar} from 'quasar'
import {roomStore} from 'stores/rooms'
import {currentFilesStore} from 'stores/materials/current-files'

const $q = useQuasar()
const rooms = roomStore()
const currentFiles = currentFilesStore()

const props = defineProps({
  url: {type: String, default: ''},
  start: {type: Number, default: 0},
  auto: {type: Boolean, default: false},
  isPreviewBig: {type: Boolean, default: false},
  controls: {type: Boolean, default: false},
  isTakeaway: {type: Boolean, default: false},
  isCommentTakeaway: {type: Boolean, default: false},
})

function playVideo() {
  if (!props.isPreviewBig) return
  currentFiles.videoUrl = props.url
  currentFiles.isMaterialShowMap.video = true
}
</script>

<style scoped lang="scss">
.icon-button {
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  .icon-play {
    color: #eee;
  }
  // &:hover {
  & {
    background: rgba(0, 0, 0, 0.5);
    .icon-play {
      color: #fff;
      transform: scale(1.2);
    }
  }
  // }
}
</style>
