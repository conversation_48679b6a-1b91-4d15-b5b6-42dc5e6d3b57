<template>
  <div class="full-width q-px-md">
    <q-banner inline-actions rounded class="bg-amber-2 text-black q-mt-sm" v-if="isViewOnly && !bannerClosed">
      You cannot edit this content because the owner has set it to view only.
      <template v-slot:action>
        <q-btn flat rounded icon="close" class="q-pa-sm" @click="bannerClosed = true" />
      </template>
    </q-banner>
  </div>
  <video
    ref="videoPlayerRef"
    :class="!isPreview ? 'video-js vjs-default-skin' : ''"
    controls
    v-bind="isPreview ? {poster: ''} : {width: '640', height: '264'}"></video>
  <div v-if="isEdit && showTrimmer" class="trimmer-overlay">
    <div ref="trimmer" class="trimmer">
      <div :style="{left: `${trimRange?.start >= 0 ? trimRange?.start : 0}%`}" class="trim-handle start-handle" @mousedown="startTrimDragging('start')">
        ||<span class="trim-tooltip">{{ secondToTimeFormat((trimRange.start * videoData.endTime) / 100) }}</span>
      </div>
      <div :style="{left: `${trimRange?.end <= 100 ? trimRange?.end : 100}%`}" class="trim-handle end-handle" @mousedown="startTrimDragging('end')">
        ||<span class="trim-tooltip">{{ secondToTimeFormat((trimRange.end * videoData.endTime) / 100) }}</span>
      </div>
      <div
        class="trimmer-fill"
        :style="{
          left: `${trimRange.start}%`,
          width: `${trimRange.end - trimRange.start}%`,
        }"></div>
    </div>
    <div class="trimmer-controls">
      <button class="trim-active" @click="applyTrim">Trim</button>
      <button @click="cancelTrim">Cancel</button>
    </div>
  </div>
  <VideoFooter
    v-if="isEdit && !isViewOnly"
    :disabledAddActivity="showTrimmer || isAnyMarkerOverlapping || quizModalVisible"
    ref="footerRef"
    class="video-footer" />
  <div v-if="isEdit">
    <Transition name="bounce">
      <MainModal
        :readOnly="isViewOnly"
        v-if="quizModalVisible"
        :selectedQuiz="currentQuiz"
        :isLandscape="!isLandscape"
        @clearSelectedQuizFromChild="handleModalClose" />
    </Transition>
  </div>
  <div v-else-if="isStudent">
    <Transition name="bounce">
      <MainModalStud v-if="quizModalVisible && currentQuiz" :selectedQuiz="currentQuiz" @clearSelectedQuizFromChild="clearSelectedQuiz" />
      <EyesUp v-else-if="!rooms.studentPaced" />
    </Transition>
    <CaseStudy v-if="isCaseStudy" />
  </div>
</template>

<script setup>
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import 'videojs-youtube'
import {ref, onMounted, onBeforeUnmount, watch, computed, nextTick, isReadonly} from 'vue'
import {useRoute} from 'vue-router'
import {roomStore} from '../../stores/rooms'
import {interactiveVideoStore} from '../../stores/interactiveVideo'
import {useQuasar} from 'quasar'
import VideoFooter from '../../pages/InteractiveVideo/components/VideoFooter.vue'
import MainModal from '../../pages/InteractiveVideo/components/ActivityModal/MainModal.vue'
import MainModalStud from '../../pages/class/student/interactive-video/ActivityModal/MainModal.vue'
import EyesUp from '../../pages/class/student/interactive-video/ActivityModal/EyesUp.vue'
import CaseStudy from '../../pages/class/student/interactive-video/ActivityModal/CaseStudy.vue'

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  isStudent: {
    type: Boolean,
    default: false,
  },
  videoData: {
    type: Object,
    default: undefined,
  },
  isSaving: {
    type: Boolean,
    default: false,
  },
  isPreview: {
    type: Boolean,
    default: false,
  },
  startTrim: {
    type: Number,
    default: null,
  },
  endTrim: {
    type: Number,
    default: null,
  },
  isLandscape: {
    type: Boolean,
    default: false,
  },
  unitData: {
    type: Object,
  },
  isReadonly: {
    type: Boolean,
  },
})

const rooms = roomStore()
const videoStore = interactiveVideoStore()
const route = useRoute()
const $q = useQuasar()
let player = null
let currentTooltip = null
let currentMarker = null
let trimmedOverlay = null
let startCancelTrim = null
let endCancelTrim = null
let isSeekDragging = false
let isTrimDragging = false
let isMarkerDragging = false
let markerClickedTime = null
const trimRange = ref({start: 0, end: 100})
const showTrimmer = ref(false)
const isPreview = computed(() => props.isPreview)
const isViewOnly = computed(() => route.path.includes('/video/view/') || props.isReadonly)
const startTrim = isPreview.value ? computed(() => props.startTrim) : ref(null)
const endTrim = isPreview.value ? computed(() => props.endTrim) : ref(null)
const dragType = ref(null)
const initialX = ref(0)
const isAnyMarkerOverlapping = ref(false)
const resumedFromMarker = ref(false)
const videoPlayerRef = ref(null)
const isCaseStudy = ref(false)
const isPausedAtMarker = ref(false)
const isMarkerClicked = ref(false)
const getQuizIdFromAuth = computed(() => rooms.currentQuizId)
const isLandscape = computed(() => props.isLandscape)
const isSaving = computed(() => props.isSaving)
const isEdit = computed(() => props.isEdit)
const isStudent = computed(() => props.isStudent)
const currentQuiz = ref(isEdit.value ? {} : null)
const quizModalVisible = isEdit.value ? computed(() => videoStore.getQuizModalVisibility) : ref(false)
const questions = isEdit.value ? computed(() => videoStore.getQuestions || []) : ref([])
const videoData = computed(() => {
  if (isEdit.value) {
    return videoStore.getVideoData || {}
  } else {
    return props.videoData
  }
})
const bannerClosed = ref(false)

watch(isSaving, (newState) => {
  if (newState) {
    if (isEdit.value && showTrimmer.value) {
      cancelTrim()
    }
  }
})

watch(isMarkerClicked, (newVal) => {
  if (newVal) {
    player.pause()
  } else {
    player.currentTime(markerClickedTime + 1)
    markerClickedTime = null
    player.play()
  }
})

watch(quizModalVisible, (newQuizModalVisible) => {
  if (newQuizModalVisible) {
    player.pause()
  }
})

watch(
  questions,
  (newQuestions) => {
    if (player) {
      updateMarkers(newQuestions)
    }
    if (isStudent.value && !rooms.studentPaced) {
      currentQuiz.value = newQuestions.find((quiz) => quiz._id === getQuizIdFromAuth.value)
    }
  },
  {deep: true}
)

watch(
  () => rooms.currentCaseStudy,
  (newCaseStudy) => {
    if (!rooms.studentPaced) {
      isCaseStudy.value = newCaseStudy?.length ? true : false
    }
  }
)

watch([startTrim, endTrim], () => {
  if (isEdit.value || isStudent.value) {
    updateMarkers(questions.value)
  }
})

watch(isPausedAtMarker, (newValue) => {
  if (newValue) {
    updateTimeOnMarker()
  }
})

watch(showTrimmer, (newValue) => {
  const controlBar = player.getChild('controlBar')
  if (newValue) {
    const trimButton = document.querySelector('.vjs-trim-button')
    if (trimButton) {
      trimButton.remove()
    }
    player.duration = function () {
      return videoData.value.endTime
    }
    const trimStartTime = videoData?.value?.trimOptions?.startTime || videoData.value.startTime
    const trimEndTime = videoData?.value?.trimOptions?.endTime || videoData.value.endTime
    trimRange.value.start = (trimStartTime * 100) / videoData.value.endTime
    trimRange.value.end = (trimEndTime * 100) / videoData.value.endTime
  } else {
    let trimButton = document.querySelector('.vjs-trim-button')
    if (!trimButton) {
      trimButton = videojs.dom.createEl('button', {
        className: 'vjs-button vjs-trim-button',
        innerHTML: 'Trim',
      })
      trimButton.addEventListener('click', () => {
        startCancelTrim = startTrim.value
        endCancelTrim = endTrim.value
        changeShowTrimmer()
      })
      controlBar.el().appendChild(trimButton)
    }
  }
  if (newValue) {
    player.pause()
    const videoDataCopy = JSON.parse(JSON.stringify(videoData.value))
    if (!videoDataCopy['trimOptions']) {
      videoDataCopy['trimOptions'] = {}
    }
    videoDataCopy['trimOptions']['startTime'] = 0
    videoDataCopy['trimOptions']['endTime'] = player.duration()
    videoStore.setVideoData(videoDataCopy)
    if (document.getElementById('custom-duration').textContent !== secondToTimeFormat(videoData.value.endTime)) {
      if (document.getElementById('custom-duration')) document.getElementById('custom-duration').textContent = secondToTimeFormat(videoData.value.endTime)
    }
    const progressControl = player.getChild('controlBar').getChild('progressControl')
    const seekBar = progressControl.getChild('seekBar').el()

    const videoFullDuration = player.duration()
    seekBar.max = videoFullDuration

    const progressBar = progressControl.el()
    progressBar.style.width = '100%'

    trimmedOverlay = document.querySelector('.trimmed-overlay')
    if (trimmedOverlay) {
      trimmedOverlay.remove()
    }

    const startTrimPercentage = (Number(startTrim.value) / videoFullDuration) * 100
    const endTrimPercentage = (Number(endTrim.value) / videoFullDuration) * 100
    const trimmedWidthPercentage = endTrimPercentage - startTrimPercentage

    trimmedOverlay = document.createElement('div')
    trimmedOverlay.classList.add('trimmed-overlay')
    trimmedOverlay.style.position = 'absolute'
    trimmedOverlay.style.left = `${startTrimPercentage}%`
    trimmedOverlay.style.width = `${trimmedWidthPercentage}%`
    trimmedOverlay.style.height = '100%'
    trimmedOverlay.style.backgroundColor = 'rgba(0, 255, 0, 0.5)'
    trimmedOverlay.style.zIndex = '1'
    trimmedOverlay.style.pointerEvents = 'none'

    const customProgressContainer = document.getElementById('custom-progress-container')
    customProgressContainer.appendChild(trimmedOverlay)
  }
})

watch(
  videoData,
  (newVideoData) => {
    if (newVideoData?.trimOptions) {
      startTrim.value = newVideoData.trimOptions.startTime
      endTrim.value = newVideoData.trimOptions.endTime
    }
  },
  {immediate: true}
)

let squiz = null
let smodal = false

watch(
  () => rooms.studentPaced,
  (newVal) => {
    if (isStudent.value) {
      if (newVal) {
        isCaseStudy.value = false
        quizModalVisible.value = squiz
        currentQuiz.value = smodal
        // if (player) player.currentTime(0)
      } else {
        squiz = quizModalVisible.value
        smodal = currentQuiz.value
        quizModalVisible.value = getQuizIdFromAuth.value ? true : false
        currentQuiz.value = questions.value.find((quiz) => quiz._id === getQuizIdFromAuth.value)
        if (player) player.pause()
      }
    }
  }
)

watch(
  () => rooms.currentQuizId,
  (newVal) => {
    if (isStudent.value) {
      if (!rooms.studentPaced) {
        quizModalVisible.value = newVal ? true : false
        currentQuiz.value = questions.value.find((quiz) => quiz._id === getQuizIdFromAuth.value)
      }
    }
  }
)

const handleModalOpen = () => {
  player.pause()
  quizModalVisible.value = true
  resumedFromMarker.value = false
  videoStore.setQuizModalVisibility(true)
  videoStore.setQuestionType(currentQuiz.value.type)
}

const handleModalClose = (seekClicked = false) => {
  const nextSecond = player.currentTime() + 1
  player.currentTime(nextSecond)
  currentQuiz.value = null
  videoStore.setQuizModalVisibility(false)
  videoStore.setQuestionType(undefined)
  if (!seekClicked) {
    const customProgressPlayed = document.getElementById('custom-progress-played')
    customProgressPlayed.classList.remove('marker-stop')
  }
  player.play()
}

const changeShowTrimmer = () => {
  showTrimmer.value = !showTrimmer.value
}

const updateTimeOnMarker = () => {
  const nextSecond = player.currentTime() + 1
  player.currentTime(nextSecond)
  isPausedAtMarker.value = false
}

const updateMarkers = async (markers) => {
  await nextTick()
  document.querySelectorAll('.vjs-marker').forEach((marker) => marker.remove())
  if (markers?.length) {
    markers.forEach((quiz) => {
      if (parseFloat(quiz.page) >= startTrim.value && parseFloat(quiz.page) <= endTrim.value) {
        addMarker(parseFloat(quiz.page), quiz)
      } else {
        clearMarker(quiz)
      }
    })
  }
}

const clearSelectedQuiz = () => {
  currentQuiz.value = null
  quizModalVisible.value = false
  isMarkerClicked.value = false
  player.play()
}

onBeforeUnmount(() => {
  if (player) {
    // Remove the event listener
    player.off('timeupdate', handleTimeUpdate)
    // Dispose of the player to clean up
    player.dispose()
    player = null
  }
})

onMounted(async () => {
  videoStore.loadState()
  const qRes = await App.service('questions').find({query: {id: videoData.value.videoId}})
  if (isEdit.value && qRes?.data) {
    videoStore.setQuestions(qRes?.data)
  }
  if (isPreview.value) {
    questions.value = qRes?.data
  }
  if (isStudent.value) {
    questions.value = qRes?.data
    quizModalVisible.value = getQuizIdFromAuth.value ? true : false
    currentQuiz.value = questions.value.find((quiz) => quiz._id === getQuizIdFromAuth.value)
    isCaseStudy.value = rooms.currentCaseStudy ? true : false
  }
  videoStore.setQuizModalVisibility(false)
  const videoId = videoData.value.videoId?.length ? videoData.value.videoId.split('::')[0] : undefined
  if (videoData.value.type === 'youtube') {
    player = videojs(videoPlayerRef.value, {
      techOrder: ['youtube'],
      controlBar: {
        volumePanel: {
          inline: false,
        },
        progressControl: {
          seekBar: {
            mouseTimeDisplay: false,
          },
        },
        children: ['playToggle', 'volumePanel'],
      },
      preload: 'auto',
      autoplay: false,
      muted: true,
      bigPlayButton: false,
      poster: '',
      sources: [
        {
          type: 'video/youtube',
          src: `https://www.youtube.com/watch?v=${videoId}`,
        },
      ],
      inactivityTimeout: 0,
      youtube: {
        ytControls: 0,
      },
    })
  } else {
    player = videojs(videoPlayerRef.value, {
      techOrder: ['html5'],
      controlBar: {
        volumePanel: {
          inline: false,
        },
        progressControl: {
          seekBar: {
            mouseTimeDisplay: false,
          },
        },
        children: ['playToggle', 'volumePanel'],
      },
      preload: 'auto',
      autoplay: false,
      muted: true,
      inactivityTimeout: 0,
      bigPlayButton: false,
      sources: [
        {
          type: 'video/mp4',
          src: Fn.hashToUrl(videoId),
        },
        {
          type: 'video/webm',
          src: Fn.hashToUrl(videoId, 'webm'), // e.g. "https://example.com/video.webm"
        },
        {
          type: 'video/ogg',
          src: Fn.hashToUrl(videoId, 'ogv'), // e.g. "https://example.com/video.ogv"
        },
      ],
    })
  }
  player.ready(() => {
    const seekBar = player.controlBar.progressControl.seekBar.el()
    ;['touchstart', 'mousedown', 'touchmove', 'mousemove'].forEach((eventType) => {
      seekBar.addEventListener(eventType, (e) => e.preventDefault(), {passive: false})
    })
    player.on('playing', () => {
      if (isPreview.value) {
        const spinner = player.el().querySelector('.vjs-loading-spinner')
        if (spinner) {
          spinner.style.display = 'none'
        }
      }
    })
    player.on('play', () => {
      if (isEdit.value || isStudent.value) {
        if (resumedFromMarker.value) {
          player.currentTime(player.currentTime() + 1)
          resumedFromMarker.value = false
        }
        if (!isEdit.value && rooms.studentPaced) {
          quizModalVisible.value = false
        }
      }
    })
    player.on('pause', () => {
      if ((isEdit.value || isStudent.value) && resumedFromMarker.value) {
        player.currentTime(player.currentTime() + 1)
        player.play()
      }
    })
    player.on('loadedmetadata', () => {
      const originalDuration = player.duration()
      player.currentTime(startTrim.value)
      player.duration = function () {
        return originalDuration - startTrim.value
      }
      if (isStudent.value || isEdit.value) {
        startTrim.value = startTrim.value === null ? 0 : startTrim.value
        endTrim.value = endTrim.value === null ? originalDuration : endTrim.value
        updateMarkers(questions.value)
      }
      player.on('timeupdate', handleTimeUpdate)
    })
    player.poster('')
    player.controlBar.el().addEventListener('click', (e) => {
      if (isStudent.value || isEdit.value) {
        if (e.target.closest('.vjs-marker')) return
        hideAllTooltips()
      }
    })
    const customTimeDuration = videojs.dom.createEl('div', {
      id: 'custom-time-display',
      className: 'custom-time-display',
      innerHTML: `<span id="custom-current-time">-</span> / <span id="custom-duration">-</span>`,
    })
    player.controlBar.el().appendChild(customTimeDuration)

    const customSeekBar = videojs.dom.createEl('div', {
      id: 'custom-progress-container',
      className: 'custom-progress-container',
      innerHTML: `
      <div id="custom-progress-played" class="custom-progress-played"></div>
      <div id="custom-progress-buffered" class="custom-progress-buffered"></div>
    `,
    })

    let isSeekUpdating = false
    let savedTimeUpdateListener = null
    let newActualTime = 0

    const updateTime = (event) => {
      if (isSeekUpdating) return // Prevent overlapping updates
      isSeekUpdating = true

      const rect = customSeekBar.getBoundingClientRect()
      const clickX = event.clientX - rect.left
      const width = rect.width
      const percent = Math.max(0, Math.min(1, clickX / width)) // Clamp percent between 0 and 1
      const trimmedDuration = endTrim.value - startTrim.value
      const newTrimmedTime = percent * trimmedDuration
      newActualTime = startTrim.value + newTrimmedTime

      if (document.getElementById('custom-current-time')) document.getElementById('custom-current-time').textContent = secondToTimeFormat(newTrimmedTime)
      const playedBar = customSeekBar.querySelector('#custom-progress-played')
      playedBar.style.width = `${percent * 100}%`

      isSeekUpdating = false
    }
    const pauseTimeUpdateEvents = () => {
      savedTimeUpdateListener = onTimeUpdate
      player.off('timeupdate', onTimeUpdate)
    }
    const resumeTimeUpdateEvents = () => {
      if (savedTimeUpdateListener) {
        player.on('timeupdate', savedTimeUpdateListener)
        savedTimeUpdateListener = null
      }
    }
    const onTimeUpdate = () => {
      if (!isSeekDragging && !isSeekUpdating && !isTrimDragging) {
        const currentTime = player.currentTime()
        const trimmedDuration = endTrim.value - startTrim.value
        const percent = (currentTime - startTrim.value) / trimmedDuration

        const playedBar = customSeekBar.querySelector('#custom-progress-played')
        playedBar.style.width = `${Math.max(0, Math.min(1, percent)) * 100}%`
      }
    }
    customSeekBar.addEventListener('click', (event) => {
      updateTime(event)
      let cTimeStr = document.getElementById('custom-current-time')?.textContent
      if (cTimeStr) {
        let timeParts = cTimeStr.split(':').map(Number)
        let curTime = timeParts[0] * 60 + timeParts[1]
        videoStore.setCurrentTime(curTime)
      }
      if (!quizModalVisible.value) {
        player.play()
      }
    })
    customSeekBar.addEventListener('mousedown', (event) => {
      if (isStudent.value && isMarkerClicked.value) return
      event.preventDefault()
      isSeekDragging = true
      pauseTimeUpdateEvents()
      player.play()
      updateTime(event)
    })
    document.addEventListener('mousemove', (event) => {
      if (isSeekDragging) {
        updateTime(event)
      }
    })
    document.addEventListener('mouseup', () => {
      if (isSeekDragging) {
        isSeekDragging = false
        player.currentTime(newActualTime)
        updateTime(event)
        resumeTimeUpdateEvents()
        player.play()
      }
    })
    player.on('timeupdate', onTimeUpdate)
    player.controlBar.el().appendChild(customSeekBar)

    const trimButton = videojs.dom.createEl('button', {
      className: 'vjs-button vjs-trim-button',
      innerHTML: 'Trim',
    })
    trimButton.addEventListener('click', () => {
      startCancelTrim = startTrim.value
      endCancelTrim = endTrim.value
      changeShowTrimmer()
    })
    if (isEdit.value && !isViewOnly.value) {
      player.controlBar.el().appendChild(trimButton)
    }
  })
  onBeforeUnmount(() => {
    if (player) {
      player.dispose()
    }
  })
})

const hideAllTooltips = () => {
  document.querySelectorAll('.vjs-tooltip').forEach((tooltip) => {
    tooltip.style.display = 'none'
  })
}

let isUpdatingTime = false

const handleTimeUpdate = () => {
  if (isStudent.value && !rooms.studentPaced) player.pause()
  if (isUpdatingTime || isSeekDragging || isTrimDragging || isMarkerDragging) return

  const currentTime = player.currentTime()
  const trimmedDuration = endTrim.value - startTrim.value
  const trimmedCurrentTime = currentTime - startTrim.value

  // Store the trimmed current time in the store (from 00:00 for the trimmed video)
  videoStore.setCurrentTime(trimmedCurrentTime)
  // Ensure the video stays within the trimmed segment
  if (currentTime < startTrim.value || currentTime > endTrim.value) {
    isUpdatingTime = true
    player.currentTime(startTrim.value) // Reset to startTrim if out of bounds
    player.play()
    isUpdatingTime = false
    return
  }

  // Update the custom progress bar
  const progressPercent = (trimmedCurrentTime / trimmedDuration) * 100

  const customProgressPlayed = document.getElementById('custom-progress-played')
  if (customProgressPlayed) customProgressPlayed.style.width = `${progressPercent}%`

  // Update the custom time display
  if (document.getElementById('custom-current-time')) document.getElementById('custom-current-time').textContent = secondToTimeFormat(trimmedCurrentTime)
  if (document.getElementById('custom-duration')) document.getElementById('custom-duration').textContent = secondToTimeFormat(trimmedDuration)

  if (isStudent.value || isEdit.value) {
    // Handle quizzes and markers
    questions.value.forEach((quiz) => {
      const markerElement = document.querySelector(`#marker-${quiz._id}`)
      if (markerElement) {
        const markerTime = parseFloat(quiz.page) - startTrim.value
        const markerTolerance = 0.2 // Allowable tolerance in seconds for overlap
        const isOverlapping = Math.abs(trimmedCurrentTime - markerTime) <= markerTolerance
        if (isOverlapping) {
          markerElement.classList.add('vjs-marker-overlapping')
          isAnyMarkerOverlapping.value = true
          player.pause()
          quizModalVisible.value = true
          currentQuiz.value = quiz
          resumedFromMarker.value = false
          if (isStudent.value) {
            isPausedAtMarker.value = true
          } else if (isEdit.value) {
            customProgressPlayed.classList.add('marker-stop')
            player.pause()
            videoStore.setQuizModalVisibility(true)
            videoStore.setQuestionType(quiz.type)
          }
        } else {
          isAnyMarkerOverlapping.value = false
          markerElement.classList.remove('vjs-marker-overlapping')
        }
      }
    })

    if (isAnyMarkerOverlapping.value) {
      customProgressPlayed.classList.add('vjs-play-progress-overlapping')
    } else {
      if (customProgressPlayed) customProgressPlayed.classList.remove('vjs-play-progress-overlapping')
    }

    // Handle quiz display when time matches quiz time
    if (questions?.value?.length) {
      const question = questions.value.find((q) => trimmedCurrentTime === parseFloat(q.page))
      if (question) {
        currentQuiz.value = question
        player.pause()
        return
      }
    }
  }
}

const clearMarker = (quiz) => {
  if (player) {
    const existingMarker = document.querySelector(`#marker-${quiz._id}`)
    if (existingMarker) {
      existingMarker.remove()
    }
  }
}

const addMarker = (time, question) => {
  clearMarker(question)
  const customProgressContainer = document.getElementById('custom-progress-container')
  if (customProgressContainer) {
    const adjustedTime = time - startTrim.value
    const trimmedDuration = endTrim.value - startTrim.value
    const percentage = (adjustedTime / trimmedDuration) * 100
    const marker = videojs.dom.createEl('div', {
      className: 'vjs-marker',
      style: `left: calc(${percentage}% - 6px);`,
      id: `marker-${question._id}`,
    })
    const tooltip = videojs.dom.createEl('div', {
      className: 'vjs-tooltip',
      style: 'display: none;',
      innerHTML: `
          <div class="tooltip-header">
            <div class="quiz-type">${question.type === 'choice' ? 'Multiple Choice Question' : 'Text Question'}</div>
            <div class="close-icon" style="cursor: pointer; color: #FFF;">
              <i class="material-icons">close</i> <!-- Change to your icon name -->
            </div>
          </div>
          <div class="tooltip-content">
            <div class="quiz-text">${question.data.length > 10 ? question.data.slice(0, 10) + '...' : question.data}</div>
            <div class="time-edit">
              ${
                isEdit.value
                  ? `
                    <div class="edit-option">
                      <div class="edit-icon">
                        <i class="material-icons">edit</i> <!-- Change to your icon name -->
                      </div>
                      Edit
                    </div>`
                  : ''
              }
              <div class="time">${secondToTimeFormat(adjustedTime)}</div>
            </div>
          </div>
        `,
    })

    tooltip.querySelector('.close-icon').addEventListener('click', (e) => {
      e.stopPropagation()
      tooltip.style.display = 'none'
    })

    marker.appendChild(tooltip)

    tooltip.addEventListener('mousedown', (e) => {
      e.stopPropagation()
    })

    if (isEdit.value) {
      tooltip.querySelector('.edit-option').addEventListener('click', (e) => {
        e.stopPropagation()
        quizModalVisible.value = true
        player.pause()
        videoStore.setQuizModalVisibility(true)
        videoStore.setQuestionType(question.type)
        tooltip.style.display = 'none'
        currentQuiz.value = question
      })
    }

    marker.addEventListener('mouseover', () => {
      if (currentTooltip && currentTooltip !== tooltip) {
        currentTooltip.style.display = 'none'
      }
      tooltip.style.display = 'block'
      currentTooltip = tooltip
    })

    marker.addEventListener('mousedown', (e) => {
      if (isEdit.value && !isViewOnly.value) {
        e.preventDefault()
        currentMarker = marker
        const rect = customProgressContainer.getBoundingClientRect()
        isMarkerDragging = true
        let isMarkerMove = false
        const customProgressPlayed = document.getElementById('custom-progress-played')
        customProgressPlayed.classList.add('marker-stop')

        const mouseMoveHandler = (e) => {
          if (currentMarker === marker) {
            isMarkerDragging = true
            isMarkerMove = true
            let newLeft = e.clientX - rect.left

            const progressBarRectWidth = rect.width
            newLeft = Math.min(newLeft, progressBarRectWidth)
            newLeft = Math.max(newLeft, 0)
            marker.style.left = `${(newLeft / progressBarRectWidth) * 100}%`
            handleModalClose(true)
          }
        }
        const mouseUpHandler = async () => {
          document.removeEventListener('mousemove', mouseMoveHandler)
          document.removeEventListener('mouseup', mouseUpHandler)
          player.pause()
          if (isMarkerDragging) {
            isMarkerDragging = false
            const markerLeft = marker.getBoundingClientRect().left - rect.left
            const progressBarRectWidth = rect.width
            let newLeft = Math.min(Math.max(markerLeft, 0), progressBarRectWidth)
            marker.style.left = `${(newLeft / progressBarRectWidth) * 100}%`
            const preciseNewTime = (newLeft / progressBarRectWidth) * (endTrim.value - startTrim.value) + startTrim.value
            currentQuiz.value = question
            player.pause()
            if (isMarkerMove) {
              isMarkerMove = false
              currentQuiz.value.page = preciseNewTime.toString()
              updateQuizTimeInStore(question, preciseNewTime)
              await videoStore.updateQuestion({...question, page: preciseNewTime.toString()})
              $q.notify({type: 'positive', message: 'Question Updated Successfully', actions: [{icon: 'close', color: 'white', round: true}]})
              player.currentTime(preciseNewTime)
            } else {
              videoStore.setCurrentTime(parseFloat(currentQuiz.value?.page))
              player.currentTime(parseFloat(currentQuiz.value?.page))
              player.pause()
            }
            handleModalOpen()
          }
        }
        document.addEventListener('mousemove', mouseMoveHandler)
        document.addEventListener('mouseup', mouseUpHandler)
      } else if (isStudent.value) {
        player.pause()
        isMarkerClicked.value = true
        isPausedAtMarker.value = true
        resumedFromMarker.value = false
        currentQuiz.value = question
        quizModalVisible.value = true
        markerClickedTime = time
      }
    })

    customProgressContainer.appendChild(marker)
  }
}

const updateQuizTimeInStore = (quiz, newTime) => {
  const adjustedTime = newTime
  const updatedQuiz = {...quiz, page: adjustedTime.toString()}
  const findQuizIndex = questions.value.findIndex((o) => o._id === quiz._id)
  questions.value[findQuizIndex] = updatedQuiz
  videoStore.setQuestions(questions.value)
  updateMarkers(questions.value)
}

const startTrimDragging = (type) => {
  dragType.value = type
  initialX.value = event.clientX
  document.addEventListener('mousemove', onTrimDrag)
  document.addEventListener('mouseup', stopTrimDragging)
}

const onTrimDrag = (event) => {
  if (!dragType.value) return
  player.pause()
  isTrimDragging = true
  const deltaX = event.clientX - initialX.value
  const controlBarWidth = player.controlBar.width()
  const deltaPercentage = (deltaX / controlBarWidth) * 100

  if (dragType.value === 'start') {
    const newStartTrim = Math.min(trimRange.value.start + deltaPercentage, trimRange.value.end)
    if (newStartTrim >= 0) {
      trimRange.value.start = newStartTrim
      const startTrimTime = (newStartTrim / 100) * player.duration()
      updateCustomTimeDisplay(startTrimTime, 'start')
    }
  } else if (dragType.value === 'end') {
    const newEndTrim = Math.min(100, Math.max(trimRange.value.end + deltaPercentage, trimRange.value.start))
    trimRange.value.end = newEndTrim
    const endTrimTime = (newEndTrim / 100) * player.duration()
    updateCustomTimeDisplay(endTrimTime, 'end')
  }
  initialX.value = event.clientX
}

const stopTrimDragging = () => {
  if (isTrimDragging) {
    if (dragType.value === 'start') {
      const startTrimTime = (trimRange.value.start / 100) * player.duration()
      player.currentTime(startTrimTime)
    } else if (dragType.value === 'end') {
      const endTrimTime = (trimRange.value.end / 100) * player.duration()
      player.currentTime(endTrimTime)
    }
    isTrimDragging = false
    player.pause()
    document.removeEventListener('mousemove', onTrimDrag)
    document.removeEventListener('mouseup', stopTrimDragging)
    dragType.value = null
  }
}

const updateCustomTimeDisplay = (time) => {
  if (document.getElementById('custom-current-time')) {
    document.getElementById('custom-current-time').textContent = secondToTimeFormat(time)
  }
  const percentage = (time / player.duration()) * 100
  const customProgressPlayed = document.getElementById('custom-progress-played')
  if (customProgressPlayed) {
    customProgressPlayed.style.width = `${percentage}%`
  }
}

const applyTrim = () => {
  const videoDataCopy = JSON.parse(JSON.stringify(videoData.value))
  if (!videoDataCopy['trimOptions']) {
    videoDataCopy['trimOptions'] = {}
  }
  let copyPlayerDuration = JSON.parse(JSON.stringify(player.duration()))
  startTrim.value = (trimRange.value.start / 100) * copyPlayerDuration
  endTrim.value = (trimRange.value.end / 100) * copyPlayerDuration
  const trimmedDuration = Number(endTrim.value - startTrim.value)
  player.currentTime(startTrim.value)
  updateProgressControl(trimmedDuration)
  player.duration(trimmedDuration)
  showTrimmer.value = false
  if (trimmedOverlay) {
    trimmedOverlay.style.display = 'none'
  }
  videoDataCopy['trimOptions']['startTime'] = startTrim.value
  videoDataCopy['trimOptions']['endTime'] = endTrim.value
  videoStore.setVideoData(videoDataCopy)
}

const updateProgressControl = (trimmedDuration) => {
  const progressControl = player.getChild('controlBar').getChild('progressControl')
  const seekBar = progressControl.getChild('seekBar').el()

  seekBar.max = trimmedDuration

  const progressBar = progressControl.el()
  progressBar.style.width = '100%'

  const startTrimPercentage = (startTrim.value / trimmedDuration) * 100
  const endTrimPercentage = (endTrim.value / trimmedDuration) * 100
  const trimmedWidthPercentage = endTrimPercentage - startTrimPercentage

  if (trimmedOverlay) {
    trimmedOverlay.remove()
  }

  trimmedOverlay = document.createElement('div')
  trimmedOverlay.classList.add('trimmed-overlay')
  trimmedOverlay.style.position = 'absolute'
  trimmedOverlay.style.left = `${startTrimPercentage}%`
  trimmedOverlay.style.width = `${trimmedWidthPercentage}%`
  trimmedOverlay.style.height = '100%'
  trimmedOverlay.style.backgroundColor = 'rgba(0, 255, 0, 0.5)'
  trimmedOverlay.style.zIndex = '1'
  trimmedOverlay.style.pointerEvents = 'none'

  const customProgressContainer = document.getElementById('custom-progress-container')
  customProgressContainer.appendChild(trimmedOverlay)
}

const cancelTrim = () => {
  const videoDataCopy = JSON.parse(JSON.stringify(videoData.value))
  if (!videoDataCopy['trimOptions']) {
    videoDataCopy['trimOptions'] = {}
  }
  videoDataCopy['trimOptions']['startTime'] = startCancelTrim
  videoDataCopy['trimOptions']['endTime'] = endCancelTrim
  videoStore.setVideoData(videoDataCopy)
  showTrimmer.value = false
  player.play()
  trimmedOverlay = document.querySelector('.trimmed-overlay')
  if (trimmedOverlay) {
    trimmedOverlay.remove()
  }
}

function secondToTimeFormat(seconds) {
  const minutes = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${minutes}:${secs < 10 ? '0' : ''}${secs}`
}
</script>

<style scoped>
.interactive-button {
  display: flex;
  flex-direction: row;
  align-content: center;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 0 3px;
}

.interactive-button .span-text {
  font-size: small;
}

@media screen and (max-width: 1272px) {
  .interactive-button {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 3px;
    align-items: center;
  }

  .interactive-button .span-text {
    font-size: x-small;
  }
}

.switch {
  position: relative;
  display: inline-block;
  width: 27px;
  height: 15px;
  /* top: 5px; */
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: '';
  height: 12px;
  width: 12px;
  left: 0px;
  bottom: 2px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #2196f3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(16px);
  -ms-transform: translateX(16px);
  transform: translateX(16px);
}

.slider.round {
  border-radius: 20px;
}

.slider.round:before {
  border-radius: 50%;
}

@media screen and (max-width: 1272px) {
  .switch {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 10px;
    /* top: 5px; */
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: 0.4s;
    transition: 0.4s;
  }

  .slider:before {
    position: absolute;
    content: '';
    height: 8px;
    width: 8px;
    left: 1px;
    bottom: 1px;
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
  }

  input:checked + .slider {
    background-color: #2196f3;
  }

  input:focus + .slider {
    box-shadow: 0 0 1px #2196f3;
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(8px);
    -ms-transform: translateX(8px);
    transform: translateX(8px);
  }

  .slider.round {
    border-radius: 10px;
  }

  .slider.round:before {
    border-radius: 50%;
  }
}

.video-js-container {
  background-color: #000;
  width: 100%;
  margin: 0 auto;
  position: relative;
}

.video-js-container .video-js {
  width: 80%;
  height: 80%;
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.video-js-container .video-js .vjs-tech {
  border-radius: inherit;
}

.video-js-container .start-handle {
  left: 0;
}

.video-js-container .end-handle {
  right: 0;
}

::v-deep(.vjs-control-bar) {
  display: flex;
  position: inherit;
  background-color: transparent;
  position: absolute;
  bottom: -1px;
  z-index: 1;
  background: #000;
  justify-content: center;
  align-items: center;
  height: 40px;
}

::v-deep(.vjs-play-progress) {
  background-color: #26a69a;
}

::v-deep(.vjs-play-progress):before {
  top: -1px;
  font-size: 1.5em;
}

::v-deep(.vjs-marker).vjs-marker-overlapping {
  z-index: 1;
}

::v-deep(.vjs-play-progress).vjs-play-progress-overlapping:before {
  color: #26a69a;
}

@media screen and (max-width: 900px) {
  ::v-deep(.vjs-button > .vjs-icon-placeholder:before) {
    font-size: 1.5em;
    line-height: 2;
  }
}

::v-deep(.vjs-trim-button) {
  background-color: transparent;
  color: #fff;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  width: 60px !important;
  text-align: center;
  border: 1px solid #fff;
  font-size: 13px;
  height: 1.5rem;
}

@media screen and (max-width: 800px) {
  ::v-deep(.vjs-trim-button) {
    width: 40px !important;
    font-size: 10px;
    border-radius: 10px;
    height: 1.5rem;
  }
}

@media screen and (max-width: 450px) {
  ::v-deep(.vjs-trim-button) {
    width: 30px !important;
    font-size: 10px;
    border-radius: 10px;
    height: 1.5rem;
  }
}

::v-deep(.video-js-container .vjs-trim-button .vjs-icon-placeholder:before) {
  content: 'Trim';
  font-size: 14px;
  line-height: 2;
}

::v-deep(.vjs-button > .vjs-icon-placeholder:before) {
  font-size: 1.8em;
  line-height: 2 !important;
}

@media screen and (max-width: 1272px) {
  .video-js-container .vjs-trim-button {
    font-size: 0.6rem;
    width: 3rem !important;
    height: 1.5rem;
  }
}

@media screen and (max-width: 600px) {
  .video-js-container .vjs-trim-button {
    font-size: 0.6rem;
    width: 3rem !important;
    height: 1rem;
  }
}

.video-js-container .vjs-time-control {
  display: flex;
  align-items: center;
  font-size: 13px;
}

::v-deep(.vjs-marker) {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  background-color: #26a69a !important;
  position: absolute;
  top: -3px;
  bottom: 0;
}

::v-deep(.vjs-marker) .vjs-marker-content {
  background-color: #26a69a;
  height: 100%;
  width: 100%;
}

::v-deep(.vjs-tooltip) {
  position: absolute;
  background-color: #313033;
  color: white;
  width: 300px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  top: -130px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  cursor: auto;
}

::v-deep(.vjs-tooltip) .tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.quiz-type {
  width: 100%;
  text-align: center;
  font-size: 12px;
}

::v-deep(.vjs-tooltip) .tooltip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  font-size: 18px;
}

::v-deep(.vjs-tooltip) .time-edit {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  width: 100%;
}

::v-deep(.vjs-tooltip) .edit-option {
  cursor: pointer;
  display: flex;
  align-items: flex-end;
}

::v-deep(.vjs-tooltip) .edit-icon {
  margin-right: 5px;
}

@media screen and (max-width: 580px) {
  ::v-deep(.vjs-tooltip) {
    width: 200px;
    padding: 10px;
    border-radius: 5px;
    top: -110px;
    font-size: 12px;
  }

  .quiz-type {
    font-size: 12px;
  }

  ::v-deep(.quiz-text) {
    font-size: 0.95rem;
  }

  ::v-deep(.vjs-tooltip) .time-edit {
    font-size: 12px;
  }
}

.video-js-container .trimmer-overlay {
  position: absolute;
  bottom: 110px;
  width: calc(100% - 50px);
  z-index: inherit;
  left: 20px;
  right: 0;
  height: 25px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.video-js-container .trimmer {
  position: relative;
  height: 100%;
  background: #fff;
  width: 65%;
  border-radius: 6px;
}

.video-js-container .trim-handle {
  position: absolute;
  width: 10px;
  height: 25px;
  background-color: #26a64a;
  border-radius: 6px;
  cursor: pointer;
  transform: translateX(-50%);
  z-index: 1;
  color: #fff;
  display: flex;
  justify-content: center;
}

.trim-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 5px;
  white-space: nowrap;
  opacity: 1;
  visibility: visible;
}

@media screen and (max-width: 580px) {
  .video-js-container .trimmer-overlay {
    bottom: 60px;
  }
}

@media screen and (max-width: 580px) {
  ::v-deep(.custom-time-display) {
    font-size: 10px;
    width: 50px;
  }
}

.video-js-container .trimmer-fill {
  position: absolute;
  top: 0;
  bottom: 0;
  background-color: #26a64a;
  height: 100%;
  z-index: 0;
}

.video-js-container .trimmer-controls {
  display: flex;
  gap: 10px;
  margin-left: 20px;
}

@media screen and (max-width: 780px) {
  .video-js-container .trimmer-controls {
    gap: 5px;
    margin-left: 10px;
  }
}

.video-js-container .trimmer-controls button {
  background-color: transparent;
  color: #fff;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  width: 60px !important;
  text-align: center;
  border: 1px solid #fff;
  font-size: 13px;
}

@media screen and (max-width: 580px) {
  .video-js-container .trimmer-controls button {
    font-size: 10px;
    width: 40px !important;
  }
}

.video-js-container .trimmer-controls .trim-active {
  background: #26a64a;
  border: 1px solid #26a64a;
}

::v-deep(.vjs-progress-control) {
  display: none;
}

::v-deep(.vjs-play-progress) {
  background-color: #26a69a;
}

::v-deep(.vjs-play-progress):before {
  top: -1px;
  font-size: 1.9em;
}

::v-deep(.custom-progress-container) {
  position: relative;
  width: 100%;
  height: 7px;
  border-radius: 20px;
  background-color: #c4c2c250;
  cursor: pointer;
  margin-right: 20px;
}

::v-deep(.custom-time-display) {
  font-size: 13px;
  width: 115px;
  text-align: center;
}

@media screen and (max-width: 580px) {
  ::v-deep(.custom-progress-container) {
    margin-right: 5px;
  }
}

::v-deep(.custom-progress-played) {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: #26a69a;
  width: 0%;
}

::v-deep(.custom-progress-played)::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
  width: 14px;
  height: 14px;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
  z-index: 99;
}

::v-deep(.marker-stop)::after {
  right: -1px;
  width: 18px;
  height: 18px;
  background-color: #26a69a;
}

.video-js-container .vjs-custom-progress-bar {
  width: 100%;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.3);
  position: absolute;
  bottom: 10px;
  left: 0;
}

.video-js-container .vjs-custom-progress-bar .vjs-progress-bar-inner {
  height: 100%;
  background-color: #ff0000;
  width: 0;
  transition: width 0.1s linear;
}

.video-js-container .custom-progress-buffered {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: #26a69a;
  width: 0%;
}

.video-js-container .custom-time-display {
  font-size: 14px;
  color: #fff;
  display: flex;
  align-items: center;
  /* margin-right: 10px; */
}

.video-js-container .custom-time-display span {
  margin: 0 2px;
}

@media screen and (max-width: 1272px) {
  .video-js-container .custom-time-display {
    font-size: 10px;
  }
}

.video-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  padding: 0 15px;
  z-index: 1000;
}

.video-js {
  width: 100%;
  height: auto;
}

.bounce-enter-active {
  animation: bounce-in 0.5s;
}

.bounce-leave-active {
  animation: bounce-in 0.5s reverse;
}

@keyframes bounce-in {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.25);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
