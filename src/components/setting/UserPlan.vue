<template>
  <div class="column q-gutter-y-sm col" v-if="pub.plan">
    <div class="flex justify-between">
      <span class="text-indigo-8 text-weight-bold">{{ statusList[pub.plan?.status || 0] }}</span>
      <span v-if="pub.plan.end && !mini" class="text-red-8">{{ `Valid up to ${new Date(pub.plan.end).toLocaleString()}` }}</span>
    </div>
    <q-linear-progress :value="progressSize" color="primary" size="24px" rounded>
      <div class="absolute-full flex flex-center">
        <q-badge color="white" text-color="black" :label="(progressSize * 100).toFixed(2) + '%'" />
      </div>
    </q-linear-progress>
    <div class="row flex-center justify-between">
      <div class="text-grey-7">{{ `${humanStorageSize(pub.plan.used || 0)} of ${humanStorageSize(pub.plan.space || 0)}` }}</div>
      <q-btn v-if="isAdmin" flat dense @click="upgrade" color="teal" no-caps>Upgrade</q-btn>
    </div>
  </div>
</template>

<script setup>
import {computed} from 'vue'
import {pubStore} from 'stores/pub'

const props = defineProps(['mini'])

const pub = pubStore()
const isAdmin = computed(() => {
  if (pub.user?.schoolUser?.role) {
    return pub.user?.schoolUser?.role?.includes('admin')
  } else {
    return false
  }
})

const statusList = ['Unpaid', 'Trial', 'Paid']
statusList[-1] = 'Expired'

const progressSize = computed(() => pub.plan.used || 0 / pub.plan.space || 0)

function upgrade() {
  window.location.href = '/manage/school/space'
}
</script>
