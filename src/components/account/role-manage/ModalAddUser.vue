<template>
  <q-dialog v-model="isModalVisible" ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-dialog-plugin">
      <q-card-section>
        <q-input text-color="dark" v-model="userSearchText" dense autofocus placeholder="Search user">
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>
      </q-card-section>

      <q-card-actions class="column justify-center items-start">
        <div class="full-width" style="max-height: 20rem; overflow: auto">
          <div v-for="user in userListFiltered" :key="user.id" class="row justify-between items-center q-ma-sm q-px-sm full-width">
            <div class="row">
              <PubAvatar :src="user.avatar" />
              <div class="col">
                <b class="q-px-sm">{{ user.email }}</b>
                <div class="q-px-sm">{{ nameFormatter(user) }}</div>
              </div>
            </div>
            <q-btn unelevated padding="2px sm" color="green-7" label="Add" no-caps @click="props.addUser(props.targetId, user)" />
          </div>
        </div>
        <q-btn class="self-end q-ma-sm" color="primary" label="Close" @click="setIsVisible(false)" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, watchEffect} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import nameFormatter from 'src/utils/formatters/nameFormatter'

const props = defineProps({
  isVisible: {
    type: Boolean,
  },
  setIsVisible: {
    type: Function,
  },
  userList: {
    type: Array,
  },
  addUser: {
    type: Function,
  },
  // schoolId, classId, subjectId
  targetId: {
    type: String,
  },
})

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const userListFiltered = ref([])

const isModalVisible = computed({
  get() {
    return props.isVisible
  },
  set(newValue) {
    props.setIsVisible(newValue)
  },
})

const userSearchText = ref('')
watchEffect(() => {
  if (!props.userList || !props.userList.length) return (userListFiltered.value = [])
  if (!userSearchText.value) return (userListFiltered.value = props.userList)
  userListFiltered.value = props.userList.filter((user) => {
    return user.email.includes(userSearchText.value)
  })
})
</script>
