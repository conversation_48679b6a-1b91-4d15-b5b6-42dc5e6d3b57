<template>
  <q-dialog v-model="isModalVisible" ref="dialogRef" @hide="onHide">
    <q-card class="q-dialog-plugin" v-if="step == 1">
      <q-card-section>
        <q-input text-color="dark" v-model="userSearchText" dense autofocus placeholder="Search user">
          <template v-slot:prepend>
            <q-icon name="search" />
          </template>
        </q-input>
      </q-card-section>

      <q-card-actions class="column justify-center items-start">
        <div class="full-width" style="max-height: 20rem; overflow: auto">
          <div v-for="user in userListFiltered" :key="user.id" class="row justify-between items-center q-px-sm full-width">
            <div :class="`rounded-borders-sm q-pa-sm full-width row cursor-pointer ${activeUid === user.uid ? 'bg-primary' : ''}`" @click="addUser(user)">
              <PubAvatar :src="user.avatar" />
              <div class="col">
                <b class="q-px-sm">{{ user.email }}</b>
                <div class="q-px-sm">{{ nameFormatter(user) }}</div>
              </div>
            </div>
          </div>
        </div>
        <q-btn class="self-end q-ma-sm" color="primary" :disable="!activeUid" label="Next" @click="nextStep" />
      </q-card-actions>
    </q-card>
    <div class="bg-teal-1 page-dialog column no-wrap q-gutter-md q-pa-md column justify-between" v-if="step == 2">
      <div class="text-subtitle1">Choose subject(s) under academic curriculum(s) for subject coordinator</div>
      <q-select outlined :options="curriculumListFiltered" label="Please select curriculum(s)" @update:model-value="onSelectCurriculum" />
      <div class="scroll q-gutter-y-md" style="padding: 5px 3px">
        <div class="q-item row bg-white rounded-borders-md shadow-2" v-for="item in activeCurriculum" :key="item._id">
          <div class="row justify-between items-center full-width">
            <span>{{ item.name }}</span>
            <q-icon class="cursor-pointer" name="close" size="xs" @click="removeCurriculum(item)" />
          </div>
          <q-separator class="full-width q-my-md" color="grey-5" />
          <div class="col-12">
            <div class="row items-center q-gutter-md">
              <q-btn
                :outline="!activeSubject.includes(subject._id)"
                :color="subject.coordinator.includes(activeUid) ? 'grey-7' : 'primary'"
                class="rounded-borders-sm"
                :label="subject.name"
                @click="triggerSubject(subject)"
                v-for="subject in item.subjectList"
                :key="subject._id"
                :disable="subject.coordinator.includes(activeUid)" />
            </div>
          </div>
        </div>
      </div>
      <div class="flex q-gutter-md full-width no-wrap">
        <q-btn unelevated outline rounded icon="arrow_back" class="fit" color="primary" label="back" @click="onStepBack" />
        <q-btn
          unelevated
          rounded
          icon="done"
          class="fit"
          :color="activeSubject.length > 0 ? 'primary' : 'grey-7'"
          :disable="activeSubject.length == 0"
          label="Add"
          @click="submit" />
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {ref, computed, watchEffect, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import nameFormatter from 'src/utils/formatters/nameFormatter'

const props = defineProps({
  isVisible: {
    type: Boolean,
  },
  setIsVisible: {
    type: Function,
  },
  userList: {
    type: Array,
  },
  curriculumList: {
    type: Array,
  },
  // schoolId, classId, subjectId
  // targetId: {
  //   type: String,
  // },
})

const emit = defineEmits([...useDialogPluginComponent.emits, 'save'])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const userListFiltered = ref([])

let activeUid = ref('')
let activeCurriculum = ref([])
let activeSubject = ref([])
let step = ref(1)

const addUser = (user) => {
  activeUid.value = user.uid
}

const onSelectCurriculum = (value) => {
  activeCurriculum.value.push(value)
}
const removeCurriculum = (curriculum) => {
  activeCurriculum.value = activeCurriculum.value.filter((e) => e._id !== curriculum._id)

  curriculum.subjectList.forEach((e) => {
    if (activeSubject.value.includes(e._id)) {
      let isFind = activeCurriculum.value.find((curriculum) => {
        return curriculum.subjectList.find((subject) => subject._id === e._id)
      })
      if (!isFind) {
        activeSubject.value = activeSubject.value.filter((subject) => subject !== e._id)
      }
    }
  })
}
const triggerSubject = (subject) => {
  if (activeSubject.value.includes(subject._id)) {
    activeSubject.value = activeSubject.value.filter((e) => e !== subject._id)
  } else {
    activeSubject.value.push(subject._id)
  }
}
const onHide = (e) => {
  activeUid.value = ''
  activeCurriculum.value = []
  activeSubject.value = []
  step.value = 1
}

const nextStep = () => {
  step.value = 2
  if (props.curriculumList.length == 1) {
    activeCurriculum.value = props.curriculumList
  }
}
const onStepBack = () => {
  activeCurriculum.value = []
  activeSubject.value = []
  step.value = 1
}
const submit = async () => {
  for (let i = 0; i < activeSubject.value.length; i++) {
    const e = activeSubject.value[i]
    await App.service('subjects').patch(e, {
      $addToSet: {
        coordinator: activeUid.value,
      },
    })
  }
  emit('save')
  $q.notify({type: 'positive', message: 'Subject coordinator add successfully'})
  isModalVisible.value = false
}

const isModalVisible = computed({
  get() {
    return props.isVisible
  },
  set(newValue) {
    props.setIsVisible(newValue)
  },
})
const curriculumListFiltered = computed(() => {
  return props.curriculumList.filter((e) => {
    return !activeCurriculum.value.map((e) => e._id).includes(e._id)
  })
})

const userSearchText = ref('')
watchEffect(() => {
  if (!props.userList || !props.userList.length) return (userListFiltered.value = [])
  if (!userSearchText.value) return (userListFiltered.value = props.userList)
  userListFiltered.value = props.userList.filter((user) => {
    return user.email.includes(userSearchText.value)
  })
})
</script>
