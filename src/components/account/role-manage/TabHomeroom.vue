<template>
  <div class="q-ma-md">
    <div class="row justify-between items-center q-pa-md">
      <!-- <div>Name: {{ 'Homeroom Teacher' }}</div> -->
      <div></div>
      <q-btn outline color="deep-purple-7" size="md" label="Set class" no-caps icon="widgets" to="/account/classes/standard" />
    </div>
    <div v-if="list.length" class="row justify-start items-start">
      <div v-for="item in list" :key="item.classId" class="col-md-6 col-xs-12">
        <q-card class="q-ma-md">
          <q-card-section class="q-px-md q-py-sm bg-blue-1">
            <div class="row justify-between items-center">
              <div class="text-subtitle2">
                <span>Class: </span
                ><span
                  ><b>{{ item.className }}</b></span
                >
              </div>

              <q-btn
                unelevated
                color="green-6"
                size="md"
                label="Add teacher"
                no-caps
                icon-right="add"
                style="padding: 0px 12px"
                @click="setIsAddUserModalVisible(true), setCurrentClass(item)"></q-btn>
            </div>
          </q-card-section>

          <q-separator />

          <q-card-actions>
            <div class="col text-body q-px-sm">
              <div>Teachers:</div>
              <div v-for="teacher in item.head" :key="teacher._id" class="row justify-between items-center q-ma-sm bg-grey-1 q-pa-sm rounded-borders">
                <div class="row justify-center items-center">
                  <PubAvatar :src="teacher.avatar" />
                  <div class="col column items-start justify-center">
                    <b class="q-px-sm">{{ teacher.email }}</b>
                    <div class="q-px-sm">{{ nameFormatter(teacher) }}</div>
                  </div>
                </div>
                <q-btn outline color="red-7" size="sm" label="Delete" no-caps @click="handleRemoveTeacher(item.classId, teacher._id)" />
              </div>
            </div>
          </q-card-actions>
        </q-card>
      </div>
    </div>

    <NoData v-if="!list.length && !isLoading" message="Please add a standard class for your organization." />

    <ModalAddUser
      :isVisible="isAddUserModalVisible"
      :setIsVisible="setIsAddUserModalVisible"
      :userList="filterAvailableUserList"
      :addUser="addUser"
      :targetId="currentClass.classId">
    </ModalAddUser>
  </div>
</template>

<script setup>
import {ref, watch, computed, onMounted} from 'vue'
import {useQuasar} from 'quasar'
import {useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'

import useSchool from 'src/composables/common/useSchool'
import useClasses from 'src/composables/account/school/useClasses'
import useSchoolUser from 'src/composables/account/school/useSchoolUser'

import ModalAddUser from './ModalAddUser.vue'
import nameFormatter from 'src/utils/formatters/nameFormatter'

const $q = useQuasar()
const $router = useRouter()
const pub = pubStore()

const isLoading = ref(false)
const {schoolId} = useSchool()
const {getList} = useSchoolUser()
// TODO
// const {list} = useClasses()

const list = ref([])
const getClasses = async () => {
  const apiUrl = 'getTeachers'
  const dto = {schoolId: schoolId.value}
  const res = await oldProxy(`/classcipe/api2/school/role/user/${apiUrl}`, dto, 'POST')
  if (!res || !res.result) return
  const classes = res.result
  const users = await App.service('school-user').find({query: {school: schoolId.value, del: false, head: {$in: classes.map((e) => e.classId)}, $limit: 1000}})
  list.value = classes.map((e) => ({
    ...e,
    head: users.data.filter((_) => _.head.includes(e.classId)),
  }))
  console.log(list.value)
}

const filterAvailableUserList = ref([])
const availableUserList = ref([])
const getAvailableUsers = async () => {
  const res = await App.service('school-user').find({query: {school: schoolId.value, del: false, $limit: 1000}})
  availableUserList.value = res.data
}

const currentClass = ref({})
const setCurrentClass = (classItem) => (currentClass.value = classItem)
watch(currentClass, () => {
  const teacherEmails = currentClass.value.head.map((e) => e.email)
  filterAvailableUserList.value = availableUserList.value.filter((user) => !teacherEmails.includes(user.email))
})

const isAddUserModalVisible = ref(false)
const setIsAddUserModalVisible = (bool) => (isAddUserModalVisible.value = bool)

const addUser = async (targetId, user) => {
  await handleAddTeacher(targetId, user._id)
}

const handleRemoveTeacher = async (classId, teacherId) => {
  await handleAddOrRemoveTeacher(false, classId, teacherId)
}

const handleAddTeacher = async (classId, teacherId) => {
  await handleAddOrRemoveTeacher(true, classId, teacherId)
}

const handleAddOrRemoveTeacher = async (isAdd, classId, teacherId) => {
  try {
    isLoading.value = true
    if (isAdd) {
      await App.service('school-user').patch(teacherId, {$addToSet: {head: [classId]}})
      filterAvailableUserList.value = filterAvailableUserList.value.filter((e) => e._id !== teacherId)
    } else {
      await App.service('school-user').patch(teacherId, {$pull: {head: classId}})
      const teacher = availableUserList.value.find((e) => e._id === teacherId)
      if (teacher) filterAvailableUserList.value.push(teacher)
    }
    $q.notify({type: 'positive', message: `Teacher ${isAdd ? 'added' : 'removed'} successfully`})
    await getClasses()
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: `Teacher ${isAdd ? 'added' : 'removed'} unsuccessfully`})
  } finally {
    isLoading.value = false
  }
}

async function init() {
  $q.loading.show()
  isLoading.value = true
  await getList(true)
  await getClasses()
  await getAvailableUsers()
  $q.loading.hide()
  isLoading.value = false
}
watch(schoolId, init)

onMounted(async () => {
  await init()
})
</script>
