<template>
  <section>
    <div class="q-ma-md">
      <div class="row justify-between items-center q-pa-md">
        <div></div>
        <q-btn outline color="green-7" size="md" label="Add Users" no-caps @click="setIsAddUserModalVisible(true)" />
      </div>
      <div class="row justify-between items-center q-mb-xs q-mt-lg q-ma-md">
        <div class="col-sm-4 col-xs-12">
          <Filters v-model="filters" :curriculumList="curriculumList" />
          <!-- <Filters v-model="filters" :curriculumList="curriculumList" @update:modelValue="doSearch" /> -->
        </div>
      </div>

      <div v-if="coordinatorListFiltered.length" class="row justify-start items-center">
        <div v-for="item in coordinatorListFiltered" :key="item._id + item.userInfo.uid" class="col-md-6 col-xs-12">
          <q-card class="q-ma-md">
            <q-card-section class="q-px-md q-py-sm bg-blue-1 row items-center">
              <PubAvatar :src="item.userInfo.avatar" />
              <div class="q-ml-sm">
                <div class="text-body">
                  <span>Email: </span
                  ><span
                    ><b>{{ item.userInfo.email }}</b></span
                  >
                </div>
                <div class="text-body">
                  <span>Name: </span
                  ><span
                    ><b>{{ nameFormatter(item.userInfo) }}</b></span
                  >
                </div>
              </div>
            </q-card-section>

            <q-separator />

            <q-card-actions align="between">
              <div class="text-caption">
                <div>
                  <div class="q-gutter-sm">
                    <q-btn outline color="green-7" size="sm" :label="item.curriculum.join(',')" no-caps @click="setIsAddUserModalVisible(true)" />
                    <q-btn outline color="green-7" size="sm" :label="item.name" no-caps @click="setIsAddUserModalVisible(true)" />
                  </div>
                </div>
                <span>updated time: </span><span>{{ date.formatDate(item.updatedAt, 'YYYY/MM/DD HH:mm') }}</span>
              </div>
              <div>
                <q-btn outline color="red-7" dense rounded no-caps label="Delete" @click="handleDeleteUser(item)" />
              </div>
            </q-card-actions>
          </q-card>
        </div>
      </div>
      <NoData v-if="!coordinatorListFiltered.length && !isLoading" message="Please add a admin for your organization." />
    </div>

    <ModalAddCoordinator
      :isVisible="isAddUserModalVisible"
      :setIsVisible="setIsAddUserModalVisible"
      :userList="availableUserList"
      :curriculumList="curriculumList"
      @save="getCoordinatorList">
    </ModalAddCoordinator>
  </section>
</template>

<script setup>
import {ref, computed, watchEffect, onMounted} from 'vue'
import {useQuasar, date} from 'quasar'
import {pubStore} from 'stores/pub'

import useSchool from 'src/composables/common/useSchool'
import ModalAddCoordinator from './ModalAddCoordinator.vue'
import nameFormatter from 'src/utils/formatters/nameFormatter'
import Filters from './SubjectFilters.vue'
import {curriculumStore} from 'stores/curriculum'

const curriculum = curriculumStore()
const pub = pubStore()
const $q = useQuasar()
const {schoolId} = useSchool()
const filters = ref({})

let coordinatorList = ref([])
let curriculumList = ref([])

const isLoading = ref(false)

const getCoordinatorList = async () => {
  const res = await App.service('subjects').get('coordinator', {query: {school: schoolId.value}})
  coordinatorList.value = res
}

const coordinatorListFiltered = computed(() => {
  let res = coordinatorList.value
  if (filters.value.search) {
    res = res.filter((e) => {
      return e.userInfo.nickname.toLowerCase().includes(filters.value.search.toLowerCase())
    })
  }
  if (filters.value?.selectionSubject?.length) {
    res = res.filter((e) => {
      return filters.value.selectionSubject.includes(e._id)
    })
  }
  return res
})

const availableUserList = ref([])
const getAvailableUsers = async () => {
  const res = await App.service('school-user').find({query: {school: schoolId.value, del: false, $limit: 1000}})
  const adminUsersEmailList = coordinatorList.value.map((e) => e.email)
  const notAdminUsers = res.data.filter((e) => e.status === 2).filter((e) => !adminUsersEmailList.includes(e.email))
  availableUserList.value = notAdminUsers
}

const isAddUserModalVisible = ref(false)
const setIsAddUserModalVisible = (bool) => (isAddUserModalVisible.value = bool)

const handleDeleteUser = async (item) => {
  let {userInfo: user} = item
  $q.dialog({
    title: 'Confirm Delete',
    message: 'Are you sure to delete this subject coordinator?',
    cancel: true,
  })
    .onOk(async () => {
      try {
        isLoading.value = true
        await App.service('subjects').patch(item._id, {
          $pull: {
            coordinator: user.uid,
          },
        })
        $q.notify({type: 'positive', message: 'Subject coordinator deleted successfully'})
        await getCoordinatorList()
      } catch (error) {
        $q.notify({type: 'negative', message: 'Subject coordinator deleted unsuccessfully'})
      } finally {
        isLoading.value = false
      }
    })
    .onCancel(() => {})
}

const getCurriculum = async () => {
  let res = await curriculum.find(schoolId.value, false)
  let resDict = {}
  res.data
    .filter((e) => e.code != 'pd')
    .forEach((e) => {
      e.label = e.name
      e.subjectList = []
      resDict[e.code] = e
    })
  let subjectData = await App.service('subjects').find({query: {uid: schoolId.value, del: false, $limit: 1000}})
  subjectData.data.forEach((e) => {
    e.curriculum.forEach((code) => {
      if (resDict[code]) {
        resDict[code].subjectList.push(e)
      }
    })
  })

  curriculumList.value = Object.values(resDict)
}

onMounted(async () => {
  $q.loading.show()
  isLoading.value = true
  await getAvailableUsers()
  await getCoordinatorList()
  await getCurriculum()
  $q.loading.hide()
  isLoading.value = false
})
</script>
