<template>
  <section>
    <div class="q-ma-md">
      <div class="row justify-between items-center q-pa-md">
        <!-- <div>Name: {{ 'Admin' }}</div> -->
        <div></div>
        <q-btn outline color="green-7" size="md" label="Add Users" no-caps @click="setIsAddUserModalVisible(true)" />
      </div>
      <div v-if="adminList.length" class="row justify-start items-center">
        <div v-for="user in adminList" :key="user.id" class="col-md-6 col-xs-12">
          <q-card class="q-ma-md">
            <q-card-section class="q-px-md q-py-sm bg-blue-1 row items-center">
              <PubAvatar :src="user.avatar" />
              <div class="q-ml-sm">
                <div class="text-body">
                  <span>Email: </span
                  ><span
                    ><b>{{ user.email }}</b></span
                  >
                </div>
                <div class="text-body">
                  <span>Name: </span
                  ><span
                    ><b>{{ nameFormatter(user) }}</b></span
                  >
                </div>
              </div>
            </q-card-section>

            <q-separator />

            <q-card-actions align="between">
              <div class="text-caption">
                <span>updated time: </span><span>{{ date.formatDate(user.updatedAt, 'YYYY/MM/DD HH:mm') }}</span>
              </div>
              <div>
                <q-radio :modelValue="currentMainContact === user.uid" @update:modelValue="(v) => onSelectMainContact(v, user)" :val="true" />
                <span class="q-mr-md">Main contact</span>
                <q-btn
                  outline
                  :color="`${userId === user.uid || currentMainContact === user.uid ? 'grey-6' : 'red-7'}`"
                  dense
                  rounded
                  :disable="userId === user.uid || currentMainContact === user.uid"
                  no-caps
                  label="Delete"
                  @click="handleDeleteUser(user)" />
              </div>
            </q-card-actions>
          </q-card>
        </div>
      </div>
      <NoData v-if="!adminList.length && !isLoading" message="Please add a admin for your organization." />
    </div>

    <ModalAddUser
      :isVisible="isAddUserModalVisible"
      :setIsVisible="setIsAddUserModalVisible"
      :userList="filteredAvailableUserList"
      :addUser="addUser"
      :targetId="schoolId">
    </ModalAddUser>
  </section>
</template>

<script setup>
import {ref, computed, watchEffect, onMounted} from 'vue'
import {useQuasar, date} from 'quasar'
import {pubStore} from 'stores/pub'

import useSchool from 'src/composables/common/useSchool'
import useSchoolUser from 'src/composables/account/school/useSchoolUser'
import ModalAddUser from './ModalAddUser.vue'
import nameFormatter from 'src/utils/formatters/nameFormatter'

const pub = pubStore()
const $q = useQuasar()
const {userId, schoolId} = useSchool()
const {list, getList} = useSchoolUser()

const isLoading = ref(false)

const adminList = computed(() => {
  return list.value.filter((e) => !e?.del && e?.role?.includes('admin'))
})

const filteredAvailableUserList = ref([])
const availableUserList = ref([])
const getAvailableUsers = async () => {
  const res = await App.service('school-user').find({query: {school: schoolId.value, del: false, $limit: 1000}})
  const adminUsersEmailList = adminList.value.map((e) => e.email)
  const notAdminUsers = res.data.filter((e) => e.status === 2).filter((e) => !adminUsersEmailList.includes(e.email))
  availableUserList.value = notAdminUsers
  filteredAvailableUserList.value = availableUserList.value
}

const isAddUserModalVisible = ref(false)
const setIsAddUserModalVisible = (bool) => (isAddUserModalVisible.value = bool)

const addUser = async (schoolId, user) => {
  try {
    const res = await App.service('school-user').patch(user._id, {$addToSet: {role: ['admin']}})
    filteredAvailableUserList.value = filteredAvailableUserList.value.filter((e) => e._id !== user._id)
    await getList(true)
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: res.message})
  }
}

const handleDeleteUser = async (user) => {
  $q.dialog({
    title: 'Confirm Delete',
    message: 'Please confirm that you want to delete the user: ' + user.email,
    cancel: true,
  })
    .onOk(async () => {
      try {
        isLoading.value = true
        const res = await App.service('school-user').patch(user._id, {$pull: {role: 'admin'}})
        $q.notify({type: 'positive', message: 'Admin deleted successfully'})
        const teacher = availableUserList.value.find((e) => e._id === user._id)
        if (teacher) filteredAvailableUserList.value.push(teacher)
        await getList(true)
      } catch (error) {
        console.error(error)
        $q.notify({type: 'negative', message: 'Admin deleted unsuccessfully'})
      } finally {
        isLoading.value = false
      }
    })
    .onCancel(() => {})
}

const currentMainContact = ref('')
async function getMainContact() {
  try {
    const res = await App.service('school-plan').get(schoolId.value)
    currentMainContact.value = res?.contact || ''
  } catch (error) {
    console.error(error)
  }
}
async function onSelectMainContact(isMainContact, teacher) {
  const uid = teacher?.uid
  if (uid) {
    await App.service('school-plan').patch(schoolId.value, {contact: uid})
    await getMainContact()
  }
}

onMounted(async () => {
  $q.loading.show()
  isLoading.value = true
  await getAvailableUsers()
  await getList(true)
  await getMainContact()
  $q.loading.hide()
  isLoading.value = false
})
</script>
