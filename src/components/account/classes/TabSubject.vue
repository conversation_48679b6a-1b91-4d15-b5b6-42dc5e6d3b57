<template>
  <section>
    <div v-if="isLoading" class="flex flex-center" style="min-height: calc(100vh - 10rem)">
      <q-spinner color="primary" size="3em" />
    </div>

    <div v-else class="q-my-md">
      <!-- <div v-if="isUserEditing" class="row justify-between items-center q-ma-md"> -->
      <!--   <q-btn-group outline v-if="schoolCurriculumOptionsList && schoolCurriculumOptionsList.length"> -->
      <!--     <q-btn -->
      <!--       color="orange-7" -->
      <!--       size="md" -->
      <!--       no-caps -->
      <!--       v-for="curriculum in schoolCurriculumOptionsList" -->
      <!--       :outline="currentCurriculumCode !== curriculum.value" -->
      <!--       :key="curriculum.value" -->
      <!--       :value="curriculum" -->
      <!--       @click="currentCurriculumCode = curriculum.value"> -->
      <!--       {{ curriculum.label }} -->
      <!--     </q-btn> -->
      <!--   </q-btn-group> -->
      <!--   <q-btn -->
      <!--     outline -->
      <!--     color="orange-7" -->
      <!--     size="md" -->
      <!--     label="Set curriculum" -->
      <!--     no-caps -->
      <!--     :to="`/${isSys ? 'sys' : 'account'}/academic-setting/subject/curriculumSetting`" /> -->
      <!-- </div> -->

      <!-- <pre>{{ yearList?.[0] }}</pre> -->
      <!-- <pre>{{ termList?.[0] }}</pre> -->
      <!-- <pre>{{ filteredList?.[0] }}</pre> -->

      <div v-for="year in yearList" :key="year._id">
        <!-- <div class="text-h6">{{ year?.title }}</div> -->
        <div v-for="term in termList.filter((e) => e?.year === year._id)" :key="term._id" class="q-my-md">
          <div class="flex justify-between q-my-md">
            <div class="text-subtitle1 q-ml-md">{{ year?.title }} - {{ term?.title }}</div>
            <DialogAddSubjectClass :item="term" />
          </div>

          <div v-for="curriculum in sortedCurriculums" :key="curriculum._id">
            <div v-if="filteredList.filter((e) => e?.term === term._id && e?.curriculum === curriculum._id)?.length" class="q-ml-md">
              <div class="text-bold">{{ curriculum?.label }}</div>
              <div v-if="filteredList.filter((e) => e?.term === term._id && e?.curriculum === curriculum._id && e?.subject)?.length">
                <div v-for="subjectId in curriculum.subjects" :key="subjectId" class="q-ml-md">
                  <div class="">{{ subjectMap[subjectId]?.name }}</div>
                  <div class="flex">
                    <SubjectClassCard
                      v-for="_class in filteredList.filter((e) => e?.term === term._id && e?.curriculum === curriculum._id && e.subject === subjectId)"
                      :key="_class._id"
                      :id="_class.id"
                      :_class="_class"
                      :term="term"
                      @studentClick="onStudentClick"
                      @teacherClick="onTeacherClick" />
                  </div>
                </div>
              </div>
              <div v-if="filteredList.filter((e) => e?.term === term._id && e?.curriculum === curriculum._id && !e?.subject)?.length" class="q-ml-md">
                <div class="">Customize subjects</div>
                <div class="flex">
                  <SubjectClassCard
                    v-for="_class in filteredList.filter((e) => e?.term === term._id && e?.curriculum === curriculum._id && !e?.subject)"
                    :key="_class._id"
                    :id="_class.id"
                    :_class="_class"
                    :term="term"
                    @studentClick="onStudentClick"
                    @teacherClick="onTeacherClick" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <q-separator />
      </div>
    </div>

    <DialogAddTeacher :isVisible="isDialogVisible" :setIsVisible="setIsDialogVisible" :selectedClass="selectedClass" :initAllData="initAllData" />
  </section>
</template>

<script setup>
import {ref, watch, computed, onMounted, onUnmounted} from 'vue'
import {useQuasar} from 'quasar'
import {useRoute, useRouter} from 'vue-router'
import {curriculumStore} from 'stores/curriculum'

// import ConfirmDialog from 'src/components/utils/dialogs/ConfirmDialog.vue'
import DialogAddTeacher from './DialogAddTeacher.vue'
import DialogAddSubjectClass from './DialogAddSubjectClass.vue'
import SubjectClassCard from 'src/components/account/classes/SubjectClassCard.vue'

import useClasses from 'src/composables/account/school/useClasses'
import useSchool from 'src/composables/common/useSchool'
import useSchoolYear from 'src/composables/account/school/useSchoolYear'
import useSchoolTerm from 'src/composables/account/school/useSchoolTerm'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import useSubject from 'src/composables/account/academic/useSubject'

const {list: yearList} = useSchoolYear()
const {list: _termList} = useSchoolTerm()

const curriculum = curriculumStore()
const $q = useQuasar()
const $router = useRouter()
const $route = useRoute()
const {currentCurriculumList} = useAcademicSetting()
const {schoolId, schoolIdOrUserId, userId} = useSchool()
const {
  list,
  listMap,
  //deleteOneById, patchOneById, schoolCurriculumList
} = useClasses()
const {map: subjectMap} = useSubject()

const filteredList = computed(() => {
  // return list.value.filter((e) => !e?.del).filter((e) => e.school === schoolIdOrUserId.value)
  return list.value.filter((e) => e?.type === 'subject').filter((e) => e.school === schoolIdOrUserId.value)
})

const currentCurriculumMap = computed(() =>
  currentCurriculumList.value.reduce((acc, cur) => {
    acc[cur._id] = cur
    return acc
  }, {})
)

const sortedCurriculums = ref([])
watch([filteredList, currentCurriculumMap, subjectMap], sortCurriculum)
function sortCurriculum() {
  const list = filteredList.value
  let curriculumIds = []
  let subjectIds = []
  list.forEach((e) => {
    const curriculumId = e?.curriculum
    const subjectId = e?.subject
    if (curriculumId && !curriculumIds.includes(curriculumId)) {
      curriculumIds.push(curriculumId)
    }
    if (subjectId && !subjectIds.includes(subjectId)) {
      subjectIds.push(subjectId)
    }
  })
  let normalCurriculum = curriculumIds.filter((e) => !Acan.isObjectId(currentCurriculumMap.value?.[e]?.value))
  let customizeCurriculum = curriculumIds.filter((e) => Acan.isObjectId(currentCurriculumMap.value?.[e]?.value))
  normalCurriculum = normalCurriculum.sort((a, b) => {
    const aLabel = currentCurriculumMap.value?.[a]?.label || ''
    const bLabel = currentCurriculumMap.value?.[b]?.label || ''
    if (aLabel?.toLowerCase() < bLabel?.toLowerCase()) return -1
    if (aLabel?.toLowerCase() > bLabel?.toLowerCase()) return 1
    return 0
  })
  customizeCurriculum = customizeCurriculum.sort((a, b) => {
    const aLabel = currentCurriculumMap.value?.[a]?.label || ''
    const bLabel = currentCurriculumMap.value?.[b]?.label || ''
    if (aLabel?.toLowerCase() < bLabel?.toLowerCase()) return -1
    if (aLabel?.toLowerCase() > bLabel?.toLowerCase()) return 1
    return 0
  })
  curriculumIds = [...normalCurriculum, ...customizeCurriculum]

  subjectIds = subjectIds.sort((a, b) => {
    const aLabel = subjectMap.value?.[a]?.name || ''
    const bLabel = subjectMap.value?.[b]?.name || ''
    if (aLabel?.toLowerCase() < bLabel?.toLowerCase()) return -1
    if (aLabel?.toLowerCase() > bLabel?.toLowerCase()) return 1
    return 0
  })
  sortedCurriculums.value = []
  curriculumIds.forEach((curriculumId) => {
    const curriculum = currentCurriculumMap.value?.[curriculumId]
    if (curriculum) {
      const curriculumCode = curriculum?.value
      curriculum.subjects = []
      subjectIds.forEach((subjectId) => {
        const subject = subjectMap.value?.[subjectId]
        if (subject) {
          const subjectCurriculumCode = subject?.curriculum?.[0]
          if (subjectCurriculumCode === curriculumCode || subjectCurriculumCode === curriculumId) {
            curriculum.subjects.push(subjectId)
          }
        }
      })
      sortedCurriculums.value.push(curriculum)
    }
  })
}

const isLoading = ref(true)
const termList = ref([])
const initAllData = async () => {
  try {
    $q.loading.show()
    await getSchoolCurriculumOptionsList()
    handleTermList()
    setCurrentCurriculum()
    sortCurriculum()
    isLoading.value = false
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: `server error: ${error.message || 'request failed'}`})
  } finally {
    $q.loading.hide()
  }
}

function setCurrentCurriculum() {
  if (isLoading.value && schoolCurriculumOptionsList.value && schoolCurriculumOptionsList.value.length) {
    const current = schoolCurriculumOptionsList.value.find((e) => {
      for (let [k, c] of Object.entries(listMap.value)) {
        if (k.includes(e.value)) {
          if (c.length) return e
        }
      }
    })
    if (current) currentCurriculumCode.value = current
    else currentCurriculumCode.value = schoolCurriculumOptionsList.value[0]
  }
}

function handleTermList() {
  termList.value = _termList.value.map((e) => {
    const currentSubjects = list.value.filter((subject) => {
      if (!subject.subject) return false
      const [code] = subject.subject.split(':')
      return subject.term === e.id && code === currentCurriculumCode.value
    })

    const fixedSubjects = currentSubjects.reduce((acc, cur) => {
      const parentSubjectId = cur.subject
      const current = acc.find((_) => _.subject === parentSubjectId)
      if (!current) {
        acc.push({
          id: parentSubjectId,
          parentSubjectId,
          name: `${parentSubjectId.split(':')[1]}`,
          list: [cur],
        })
      } else {
        current.list.push(cur)
      }
      return acc
    }, [])

    return {...e, subjects: fixedSubjects}
  })
}

const currentCurriculumCode = ref(null)
const schoolCurriculumOptionsList = ref(null)
const getSchoolCurriculumOptionsList = async () => {
  await curriculum.init()
  const res = await curriculum.getOptions(schoolId.value)
  if (!res) return
  schoolCurriculumOptionsList.value = res
  if (schoolCurriculumOptionsList.value && schoolCurriculumOptionsList.value.length) {
    if (isLoading.value) {
      currentCurriculumCode.value = schoolCurriculumOptionsList.value[0]
    }
  }

  // const res = await App.service('conf-school').get('get', {query: {key: 'Curriculum', rid: schoolId.value, del: false}})
  // if (!res || !res.val) return
  // schoolCurriculumOptionsList.value = res.val
  // if (schoolCurriculumOptionsList.value.curriculum && schoolCurriculumOptionsList.value.curriculum.length) {
  //   if (isLoading.value) {
  //     currentCurriculumCode.value = schoolCurriculumOptionsList.value.curriculum[0]
  //   }
  // }
}

// const getClassListByTermId = (id) => {
//   if (!list.value.length) return []
//   const _list = []
//   Object.entries(listMap.value).forEach((e) => {
//     const [termId, subjectKey] = e[0].split('@')
//     if (id === 'unassigned' && termId === 'unassigned') {
//       _list.push({
//         subjectId: 'unassigned',
//         subjectName: 'Unassigned',
//         curriculumCode: null,
//         classes: e[1],
//       })
//     } else {
//       const [curriculumCode, subjectId] = subjectKey ? subjectKey.split(':') : ''
//       if (termId === id) {
//         if (curriculumCode === currentCurriculumCode.value)
//           _list.push({
//             subjectId,
//             subjectName: getClassNameById(curriculumCode, subjectId),
//             curriculumCode,
//             classes: e[1],
//           })
//       }
//     }
//   })
//   return _list
// }

// const onDeleteClass = async ({id, name}) => {
//   $q.dialog({
//     title: `Confirm Delete`,
//     message: `Please confirm that you want to delete the class: ${name}`,
//     cancel: true,
//   }).onOk(async () => {
//     try {
//       const res = await deleteOneById(id)
//       $q.notify({type: 'positive', message: 'Class deleted successfully'})
//       await initAllData()
//     } catch (error) {
//       $q.notify({type: 'negative', message: 'Class deleted unsuccessfully'})
//     } finally {
//       editingSubject.value = null
//     }
//   })
// }

// async function onPatchClassName(_id, {scope, termItem}) {
//   try {
//     const dto = {
//       // school: schoolId.value,
//       name: scope.value,
//       // term: termItem.id,
//     }
//     const res = await patchOneById(_id, dto)
//     $q.notify({type: 'positive', message: 'Class name updated successfully'})
//     await getList(true)
//     scope.set()
//   } catch (error) {
//     $q.notify({type: 'negative', message: 'Class name updated unsuccessfully'})
//     console.error(error)
//     scope.cancel()
//   } finally {
//   }
// }

const selectedClass = ref(null)
const isDialogVisible = ref(false)
const setIsDialogVisible = (bool) => (isDialogVisible.value = bool)

// const getClassNameById = (code, classId) => {
//   let subjectName = ''
//   const current = schoolCurriculumList.value.find((e) => e.code === code)
//   if (!current) return ''
//   current.subjects.forEach((e) => {
//     if (e._id === classId) {
//       subjectName = e.name
//       return
//     }
//     if (e.child.length) {
//       e.child.forEach((_) => {
//         if (_._id === classId) {
//           subjectName = e.name
//           return
//         }
//       })
//     }
//   })
//   if (subjectName) return subjectName
//   return ''
// }

// const formatRangeTime = (start, end) => {
//   return `${new Date(start).toLocaleDateString()} - ${new Date(end).toLocaleDateString()}`
// }

// const srcTargetId = ref('')
// const onDragStart = (e, _class) => {
//   srcTargetId.value = e.target.closest('.drag-target').dataset.id
//   const dto = {_id: _class._id, name: _class.name}
//   e.dataTransfer.setData('drag-item', JSON.stringify(dto))
//   e.dataTransfer.dropEffect = 'move'
// }
// const targetTermItem = ref(null)
// const onDragEnter = (e, termItem) => {
//   if (e.target.draggable !== true) {
//     if (e.target.classList.contains('drag-target')) {
//       e.target.classList.add('drag-enter')
//       targetTermItem.value = termItem
//     }
//   }
// }

// const onDragLeave = (e) => {
//   e.target.classList.remove('drag-enter')
//   targetTermItem.value = null
// }

// const onDragOver = (e) => {
//   e.preventDefault()
// }

// const onDrop = async (e) => {
//   e.preventDefault()
//   const target = e.target.closest('.drag-target')
//   if (srcTargetId.value === target.dataset.id) return
//   targetTermItem.value = {
//     _id: target.dataset.id,
//   }
//   const dragItem = JSON.parse(e.dataTransfer.getData('drag-item'))
//   const dto = {term: targetTermItem.value._id}
//   await patchOneById(dragItem._id, dto)
//   e.target.classList.remove('drag-enter')
//   srcTargetId.value = ''
//   await initAllData()
// }

function onStudentClick(_class) {
  // , isUngraded = false
  // if (isUngraded) {
  //   $q.notify({type: 'negative', message: 'Please add students only after dragging the class under the ungraded category to the created grades.'})
  //   return
  // }
  $router.push({path: `/account/subjectStudent`, query: {classId: _class._id, back: $route.path}})
}
function onTeacherClick(_class) {
  // , isUngraded = false
  // if (isUngraded) {
  //   $q.notify({type: 'negative', message: 'Please add teachers only after dragging the class under the ungraded category to the created grades.'})
  //   return
  // }
  selectedClass.value = _class
  isDialogVisible.value = true
}

// async function onDeleteClick(item) {
//   $q.dialog({
//     component: ConfirmDialog,
//     componentProps: {
//       title: '',
//       message: 'Deleting this class will result in clearing all teachers and students data. Are you sure to delete?',
//     },
//   })
//     .onOk(async () => {
//       $q.loading.show()
//       const id = item._id
//       await deleteOneById(id)
//       $q.loading.hide()
//     })
//     .onCancel(() => {})
//     .onDismiss(() => {})
// }

const checkMouseY = (event) => {
  const wh = window.innerHeight
  const y = event.clientY || 0
  const diff = 100
  if (wh - y < diff) {
    window.scrollBy(0, 10)
  } else if (y < diff) {
    window.scrollBy(0, -10)
  }
}

const currentUserClasses = ref([])
async function getCurrentUserClasses() {
  const query = {school: schoolId.value, uid: userId.value, del: false, $limit: 500}
  const res = await App.service('school-user').find({query})
  const schoolUser = res?.data?.[0] ?? {}
  currentUserClasses.value = schoolUser?.class ?? []
}

// function goToApplicationSetting(_class) {
//   const path = `/account/classes/subject/${_class._id}/self-enroll`
//   const query = {back: $route.path}
//   $router.push({path, query})
// }

onMounted(async () => {
  await getCurrentUserClasses()
  await initAllData()
  window.addEventListener('drag', checkMouseY)
})
onUnmounted(() => window.removeEventListener('drag', checkMouseY))
watch([schoolId], async () => {
  await initAllData()
})
</script>

<style lang="scss" scoped>
* {
  scroll-behavior: smooth;
}
.class-card {
  width: 100%;
  // max-width: 250px;
  max-width: 290px;
}
// .draggable {
//   cursor: grab;
// }
.drag-target:hover {
  outline: 1px dashed #999;
  border-radius: 0.5rem;
}
.drag-enter {
  outline: 2px dashed green;
  border-radius: 0.5rem;
}
</style>
