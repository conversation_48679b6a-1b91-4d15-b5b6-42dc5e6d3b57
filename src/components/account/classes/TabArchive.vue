<template>
  <section>
    <div v-if="isLoading" class="flex flex-center" style="min-height: calc(100vh - 10rem)">
      <q-spinner color="primary" size="3em" />
    </div>
    <div v-else class="q-ma-md">
      <div class="row justify-between items-center q-pa-md">
        <!-- <div>Name: {{ 'Archive' }}</div> -->
        <!-- <q-btn outline color="green-7" size="md" label="Set grade(s)" no-caps :to="`/setting/grade/${schoolId}`" /> -->
        <q-input outlined v-model="searchText" placeholder="Search class" />
      </div>

      <q-table :columns="currentColumns" :rows="filteredList" row-key="id" class="q-ma-md">
        <template v-slot:body-cell-actions="props">
          <q-td :props="props">
            <div>
              <q-btn icon="settings_backup_restore" flat dense class="text-blue-4" @click="onRestoreClass(props.value)">
                <q-tooltip anchor="top middle" self="bottom middle">Restore</q-tooltip>
              </q-btn>
              <q-btn icon="delete" flat dense class="text-red-4 q-ml-sm" @click="onDeleteClass(props.value)">
                <q-tooltip anchor="top middle" self="bottom middle">Delete</q-tooltip>
              </q-btn>
            </div>
          </q-td>
        </template>
      </q-table>
    </div>
  </section>
</template>

<script setup>
import {ref, watch, computed, onMounted, watchEffect} from 'vue'
import {useQuasar} from 'quasar'
import {pubStore} from 'stores/pub'
import useSchool from 'src/composables/common/useSchool'
import useClasses from 'src/composables/account/school/useClasses'

const pub = pubStore()
const $q = useQuasar()

const {isSchool, schoolId, schoolIdOrUserId, isUserEditing} = useSchool()
const {list, gradeList, patchOneById, deleteOneById} = useClasses()
const isSchoolMode = ref(false)

const columns = [
  {name: 'class', require: true, label: 'Class name', align: 'left', field: (row) => `${row.name}`},
  {name: 'grade', require: true, label: 'Grade', align: 'left', field: (row) => `${gradeList.value.find((e) => e._id === row.grade)?.name || '-'}`},
  {name: 'student', require: true, label: 'Student', align: 'left', field: (row) => `${row?.student || 0}`},
  {name: 'actions', require: true, label: 'Actions', align: 'left', field: (row) => row},
]
const currentColumns = computed(() => {
  if (isUserEditing.value) {
    return columns
  } else {
    return columns.slice(0, -1)
  }
})

const filteredList = ref([])
const handledList = ref([])

const isLoading = ref(true)
watch(list, async () => await initAllData())
onMounted(async () => {
  await initAllData()
})

async function initAllData() {
  try {
    $q.loading.show()
    isLoading.value = true
    handledList.value = gradeList.value.map((e) => {
      // const currentSubjects = list.value.filter((e) => e?.del).filter((_class) => _class.grade === e._id)
      const currentSubjects = list.value.filter((e) => e?.del)
      return currentSubjects.map((_) => ({
        ..._,
        gradeName: _.name || '',
        gradeId: e._id,
      }))
    })
    handledList.value = handledList.value.reduce((acc, cur) => {
      cur.forEach((e) => {
        if (!acc.some((_e) => _e._id === e._id)) acc.push(e)
      })
      return acc
    }, [])
    filteredList.value = handledList.value
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: `server error: ${error.message || 'request failed'}`})
  } finally {
    $q.loading.hide()
    isLoading.value = false
  }
}

const onDeleteClass = async (_class) => {
  $q.dialog({
    title: `Confirm Delete`,
    message: `Please confirm that you want to delete the class: ${_class.name}`,
    cancel: true,
  }).onOk(async () => {
    try {
      await deleteOneById(_class._id)
      $q.notify({type: 'positive', message: 'Class deleted successfully'})
      await initAllData()
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Class deleted unsuccessfully'})
    }
  })
}

const onRestoreClass = async (_class) => {
  $q.dialog({
    title: 'Confirm Restore',
    message: `Please confirm that you want to restore the class: ${_class.name}`,
    cancel: true,
  }).onOk(async () => {
    try {
      const dto = {del: false}
      await patchOneById(_class._id, dto)
      $q.notify({type: 'positive', message: 'Class restored successfully'})
      await initAllData()
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Class restored unsuccessfully'})
      editingSubject.value = null
    }
  })
}

const searchText = ref('')
watch(searchText, (newValue) => {
  if (newValue) {
    return (filteredList.value = handledList.value.filter((e) => e.name.includes(newValue)))
  } else {
    return (filteredList.value = handledList.value)
  }
})
</script>

<style lang="scss" scoped>
.class-card {
  width: 100%;
  max-width: 250px;
}
</style>
