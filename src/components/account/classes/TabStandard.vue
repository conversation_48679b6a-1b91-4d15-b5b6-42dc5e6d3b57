<template>
  <section class="q-pa-md">
    <div v-if="isLoading" class="flex flex-center" style="min-height: calc(100vh - 10rem)">
      <q-spinner color="primary" size="3em" />
    </div>

    <div v-else>
      <div class="q-pa-md">
        <q-card v-if="isValidCloudRoom" class="class-card">
          <q-card-section class="row justify-between text-grey-8" draggable="false">
            <div class="flex items-center">
              <q-icon name="o_cloud" color="teal" size="sm" class="q-mr-md" />
              <div class="text-body1 text-bold">Clould room</div>
            </div>
          </q-card-section>
          <q-card-actions type="flex" align="around" class="q-px-md q-pb-md" draggable="false">
            <q-btn v-if="isSchool" unelevated no-caps @click.stop="onTeacherClick(cloudRoom)" style="padding: 0" v-ripple>
              <div class="column justify-center items-center q-py-xs q-px-lg rounded-borders bg-grey-3 cursor-pointer relative-position">
                <div class="text-caption">Teachers</div>
                <div class="text-green-7">{{ cloudRoom?.count?.teacher || '0' }}</div>
              </div>
            </q-btn>
            <q-separator v-if="isSchool" vertical inset />
            <q-btn unelevated no-caps style="padding: 0" v-ripple @click.stop="onStudentClick(cloudRoom)" :disable="true">
              <div class="column justify-center items-center q-py-xs q-px-lg rounded-borders bg-grey-3 cursor-pointer relative-position">
                <div class="text-caption">Students</div>
                <div class="text-green-7">{{ cloudRoom?.count?.student || '0' }}</div>
              </div>
            </q-btn>
          </q-card-actions>
        </q-card>
      </div>

      <div v-for="curriculum in currentCurriculumList.filter((e) => currentGradeMapByCurriculum?.[e.value]?.length)" :key="curriculum._id" class="q-mb-lg">
        <div class="text-h6">{{ curriculum?.label ?? 'Unknown curriculum' }}</div>
        <div v-for="(item, i) in currentGradeMapByCurriculum[curriculum.value]" :key="i">
          <div class="row items-center justify-between q-ma-md">
            <div class="text-subtitle1 q-mr-lg">{{ item.name }}</div>
            <q-btn
              v-if="item._id !== 'ungraded' && isUserEditing"
              class="bg-teal-4 text-white"
              size="md"
              no-caps
              flat
              unelevated
              rounded
              label="Add new"
              icon="add_circle_outline"
              @click="onAddClass(item)" />
          </div>

          <div
            class="flex q-py-md"
            :class="{'q-pa-lg': item.length, 'drag-target': isUserEditing}"
            :data-id="item._id"
            :data-name="item.name"
            @dragenter="(e) => onDragEnter(e, item)"
            @dragleave="onDragLeave"
            @dragover="onDragOver"
            @drop="onDrop">
            <q-card
              class="class-card draggable q-ma-md"
              v-for="_class in groupedList[item._id]"
              :id="_class.id"
              :key="_class.id"
              :draggable="isUserEditing"
              @dragstart="(e) => onDragStart(e, _class)">
              <q-card-section class="row justify-between text-bold text-grey-8" draggable="false">
                <div class="text-body1">
                  {{ _class.name }}
                  <q-tooltip anchor="top middle" self="bottom middle">Click to edit</q-tooltip>
                  <q-popup-edit v-model="_class.name" :cover="false" class="bg-teal text-white" v-slot="scope">
                    <q-input
                      dark
                      color="white"
                      v-model="scope.value"
                      dense
                      autofocus
                      counter
                      @keyup.enter="onUpdateSubject({scope, id: _class._id, gradeItem: item})">
                      <template v-slot:append>
                        <q-icon class="cursor-pointer" name="done" @click="onUpdateSubject({scope, id: _class._id, gradeItem: item})" />
                      </template>
                    </q-input>
                  </q-popup-edit>
                </div>

                <div>
                  <q-btn
                    v-if="isUserEditing"
                    icon="class"
                    flat
                    dense
                    class="text-blue-4"
                    @click="onArchiveClass({id: _class._id, item})"
                    :disable="isArchivedDisable">
                    <q-tooltip anchor="top middle" self="bottom middle">Archive</q-tooltip>
                  </q-btn>
                </div>
              </q-card-section>

              <q-card-actions type="flex" align="around" class="q-px-md q-pb-md" draggable="false">
                <q-btn v-if="isSchool" unelevated no-caps @click.stop="onTeacherClick(_class)" style="padding: 0" v-ripple>
                  <div class="column justify-center items-center q-py-xs q-px-lg rounded-borders bg-grey-3 cursor-pointer relative-position">
                    <div class="text-caption">Teachers</div>
                    <div class="text-green-7">{{ _class?.count?.teacher || '0' }}</div>
                  </div>
                </q-btn>
                <q-separator v-if="isSchool" vertical inset />
                <q-btn unelevated no-caps style="padding: 0" v-ripple @click.stop="onStudentClick(_class)">
                  <div class="column justify-center items-center q-py-xs q-px-lg rounded-borders bg-grey-3 cursor-pointer relative-position">
                    <div class="text-caption">Students</div>
                    <div class="text-green-7">{{ _class?.count?.student || '0' }}</div>
                  </div>
                </q-btn>
              </q-card-actions>
            </q-card>
          </div>
        </div>
      </div>

      <hr class="divider" />

      <div v-for="(item, i) in currentGradeList.filter((e) => e._id === 'ungraded')" :key="i">
        <div class="row items-center justify-between q-ma-md">
          <div class="text-subtitle1 q-mr-lg">{{ item.name }}</div>
          <q-btn
            v-if="item._id !== 'ungraded' && isUserEditing"
            class="bg-teal-4 text-white"
            size="md"
            no-caps
            unelevated
            label="Add new"
            icon="add_circle_outline"
            @click="onAddClass(item)" />
        </div>

        <div
          class="flex q-py-md"
          :class="{'q-pa-lg': item.length, 'drag-target': isUserEditing}"
          :data-id="item._id"
          :data-name="item.name"
          @dragenter="(e) => onDragEnter(e, item)"
          @dragleave="onDragLeave"
          @dragover="onDragOver"
          @drop="onDrop">
          <q-card
            class="class-card draggable q-ma-md"
            v-for="_class in groupedList[item._id]"
            :id="_class.id"
            :key="_class.id"
            :draggable="isUserEditing"
            @dragstart="(e) => onDragStart(e, _class)">
            <q-card-section class="row justify-between text-grey-8" draggable="false">
              <div class="text-body1">
                {{ _class.name }}
                <q-tooltip anchor="top middle" self="bottom middle">Click to edit</q-tooltip>

                <q-popup-edit v-model="_class.name" :cover="false" class="bg-teal text-white" v-slot="scope">
                  <q-input
                    dark
                    color="white"
                    v-model="scope.value"
                    dense
                    autofocus
                    counter
                    @keyup.enter="onUpdateSubject({scope, id: _class._id, gradeItem: item})">
                    <template v-slot:append>
                      <q-icon class="cursor-pointer" name="done" @click="onUpdateSubject({scope, id: _class._id, gradeItem: item})" />
                    </template>
                  </q-input>
                </q-popup-edit>
              </div>

              <div>
                <q-btn
                  v-if="isUserEditing"
                  icon="class"
                  flat
                  dense
                  class="text-blue-4"
                  @click="onArchiveClass({id: _class._id, item})"
                  :disable="isArchivedDisable">
                  <q-tooltip anchor="top middle" self="bottom middle">Archive</q-tooltip>
                </q-btn>
              </div>
            </q-card-section>

            <q-card-actions type="flex" align="around" class="q-px-md q-pb-md" draggable="false">
              <q-btn v-if="isSchool" unelevated no-caps @click.stop="onTeacherClick(_class, true)" style="padding: 0" v-ripple>
                <div class="column justify-center items-center q-py-xs q-px-lg rounded-borders bg-grey-3 cursor-pointer relative-position">
                  <div class="text-caption">Teachers</div>
                  <div class="text-green-7">{{ _class?.count?.teacher || '0' }}</div>
                </div>
              </q-btn>
              <q-separator v-if="isSchool" vertical inset />
              <q-btn unelevated no-caps style="padding: 0" v-ripple @click.stop="onStudentClick(_class, true)">
                <div class="column justify-center items-center q-py-xs q-px-lg rounded-borders bg-grey-3 cursor-pointer relative-position">
                  <div class="text-caption">Students</div>
                  <div class="text-green-7">{{ _class?.count?.student || '0' }}</div>
                </div>
              </q-btn>
            </q-card-actions>
          </q-card>
        </div>
      </div>

      <div v-if="!isLoading && !currentGradeList?.length">
        <NoData message="You haven't joined any class yet." messageColor="grey-8" />
      </div>
    </div>

    <DialogAddTeacher :isVisible="isDialogShow" :setIsVisible="setIsDialogShow" :selectedClass="selectedClass" :initAllData="initAllData" />
  </section>
</template>

<script setup>
import {ref, watch, computed, onMounted, onUnmounted} from 'vue'
import {useQuasar} from 'quasar'
import {useRouter, useRoute} from 'vue-router'

import DialogAddTeacher from './DialogAddTeacher.vue'
import useSchool from 'src/composables/common/useSchool'
import useClasses from 'src/composables/account/school/useClasses'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'

const $q = useQuasar()
const $router = useRouter()
const $route = useRoute()
const {currentCurriculumList} = useAcademicSetting()
const {schoolIdOrUserId, userId, schoolId, isSchool, isUserEditing} = useSchool()
const {list, getList, patchOneById, createOne} = useClasses()

const isLoading = ref(false)
const isArchivedDisable = computed(() => {
  return list.value.filter((e) => !e?.del)?.length <= 1
})

onMounted(async () => {
  await getCurrentUserClasses()
  await initAllData()
  window.addEventListener('drag', checkMouseY)
})
onUnmounted(() => window.removeEventListener('drag', checkMouseY))
watch([schoolId], async () => {
  await initAllData()
})

const currentUserClasses = ref([])
async function getCurrentUserClasses() {
  const query = {school: schoolId.value, uid: userId.value, del: false, $limit: 500}
  const res = await App.service('school-user').find({query})
  const schoolUser = res?.data?.[0] ?? {}
  currentUserClasses.value = schoolUser?.class ?? []
}

const checkMouseY = (event) => {
  const wh = window.innerHeight
  const y = event.clientY || 0
  const diff = 100
  if (wh - y < diff) {
    window.scrollBy(0, 10)
  } else if (y < diff) {
    window.scrollBy(0, -10)
  }
}

const groupedList = ref([])
const initAllData = async () => {
  try {
    $q.loading.show()
    isLoading.value = true
    if (!isUserEditing.value) {
      const classes = currentUserClasses.value
      await getList(true, {_id: {$in: classes}})
    } else {
      await getList(true)
    }
    await getGradeList()
    const classGroupList = {ungraded: []}
    let _list = list.value
    if (!isUserEditing.value) {
      _list = _list.filter((e) => e?.count?.teacher)
    }
    _list
      .filter((e) => !e?.del && e?.type === 'standard')
      .map((e) => {
        const key = e.grade
        if (gardeMap.value[key]) {
          if (!classGroupList[key]) classGroupList[key] = []
          classGroupList[key].push(e)
        } else {
          classGroupList.ungraded.push(e)
        }
      })
    groupedList.value = classGroupList
    if (isSchool.value) await getCloudRoomCould()
    if (selectedClass.value) {
      const newClass = _list.find((e) => e._id === selectedClass.value._id)
      selectedClass.value = newClass
    }
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: 'server error!'})
  } finally {
    $q.loading.hide()
    isLoading.value = false
  }
}

const gardeMap = ref({})
const gradeList = ref([])
const getGradeList = async () => {
  if (!schoolIdOrUserId.value) return []
  const res = await App.service('conf-school').get('get', {query: {key: 'Grades', rid: schoolIdOrUserId.value}})
  if (!res?.val?.length) res.val = []
  res.val.forEach((e) => {
    gardeMap.value[e._id] = e
  })
  gardeMap.value.ungraded = {
    name: 'Ungraded Classes',
    _id: 'ungraded',
  }
  gradeList.value = res.val
  gradeList.value.push(gardeMap.value.ungraded)
}
const currentGradeList = computed(() => {
  if (isUserEditing.value) {
    return gradeList.value
  } else {
    return gradeList.value.filter((e) => groupedList.value[e._id]?.length)
  }
})
const currentGradeMapByCurriculum = computed(() => {
  const obj = {}
  currentGradeList.value.forEach((e) => {
    const curriculum = e?.curriculum || []
    curriculum.forEach((c) => {
      if (!obj?.[c]) obj[c] = []
      obj[c].push(e)
    })
  })
  return obj
})

const onAddClass = (item) => {
  $q.dialog({
    title: 'Adding Class',
    message: 'Please input the class name? (Minimum 2 characters)',
    prompt: {
      model: '',
      isValid: (value) => value.length > 1,
      type: 'text',
    },
    cancel: true,
    persistent: true,
  }).onOk(async (value) => {
    const dto = {
      school: schoolIdOrUserId.value,
      name: value,
      grade: item._id,
      type: 'standard',
    }
    try {
      await createOne(dto)
      $q.notify({type: 'positive', message: 'Class added successfully'})
      await initAllData()
    } catch (error) {
      $q.notify({type: 'negative', message: 'Class added unsuccessfully'})
      console.error(error)
    }
  })
}

const onUpdateSubject = async ({scope, id}) => {
  try {
    const dto = {name: scope.value}
    await patchOneById(id, dto)
    $q.notify({type: 'positive', message: 'Updated class name successfully'})
    await initAllData()
    // scope.set()
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: 'Updated class name unsuccessfully'})
    // scope?.cancel()
  }
}

const onArchiveClass = async ({id, item}) => {
  $q.dialog({
    title: `Confirm archive `,
    message: `Please confirm that you want to archive the class: ${item.name}`,
    cancel: true,
  }).onOk(async () => {
    try {
      const dto = {del: true}
      await patchOneById(id, dto)
      $q.notify({type: 'positive', message: 'Class archived successfully'})
      await initAllData()
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Class archived unsuccessfully'})
    }
  })
}

const selectedClass = ref(null)
const isDialogShow = ref(false)
const setIsDialogShow = (bool) => (isDialogShow.value = bool)

const srcTargetId = ref('')
const onDragStart = (e, _class) => {
  srcTargetId.value = e.target.closest('.drag-target').dataset.id
  const dto = {_id: _class._id, name: _class.name}
  e.dataTransfer.setData('drag-item', JSON.stringify(dto))
  e.dataTransfer.dropEffect = 'move'
}

const targetGradeItem = ref(null)
const onDragEnter = (e, gradeItem) => {
  if (e.target.draggable !== true) {
    if (e.target.classList.contains('drag-target')) {
      e.target.classList.add('drag-enter')
      targetGradeItem.value = gradeItem
    }
  }
}

const onDragLeave = (e) => {
  e.target.classList.remove('drag-enter')
  targetGradeItem.value = null
}

const onDragOver = (e) => {
  e.preventDefault()
}

const onDrop = async (e) => {
  e.preventDefault()
  const target = e.target.closest('.drag-target')
  if (srcTargetId.value === target.dataset.id) return
  targetGradeItem.value = {
    _id: target.dataset.id,
    name: target.dataset.name,
  }
  const dragItem = JSON.parse(e.dataTransfer.getData('drag-item'))
  const dto = {grade: targetGradeItem.value._id}
  await patchOneById(dragItem._id, dto)
  e.target.classList.remove('drag-enter')
  srcTargetId.value = ''
  await initAllData()
}

function onStudentClick(_class, isUngraded = false) {
  if (isUngraded) {
    $q.notify({type: 'negative', message: 'Please add students only after dragging the class under the ungraded category to the created grades.'})
    return
  }
  $router.push({path: `/account/student/all`, query: {classId: _class._id, back: $route.path}})
}
function onTeacherClick(
  _class
  // isUngraded = false
) {
  // if (isUngraded) {
  //   $q.notify({type: 'negative', message: 'Please add teachers only after dragging the class under the ungraded category to the created grades.'})
  //   return
  // }
  selectedClass.value = _class
  isDialogShow.value = true
}

const isValidCloudRoom = ref(false)
const cloudRoom = ref({_id: 'cloudRoom', name: 'Cloud room'})
async function getCloudRoomCould() {
  try {
    // ref: https://dev.classcipe.com/doc/#/fio/school-plan?id=school-plan-model
    const res = await App.service('school-plan').get(schoolId.value)
    const schooldStatus = res?.status || -1
    const contentProviderEnable = res?.contentProviderEnable || false
    const contentProviderStatus = res?.contentProviderStatus || false
    isValidCloudRoom.value = [1, 2].includes(schooldStatus) && contentProviderStatus === 2 && contentProviderEnable
    cloudRoom.value.count = {}
    const teacherRes = await App.service('school-user').get('cloudRoomCount', {query: {school: schoolId.value}})
    cloudRoom.value.count.teacher = teacherRes?.count || 0
    const studentRes = await App.service('students').get('cloudRoomCount', {query: {school: schoolId.value}})
    cloudRoom.value.count.student = studentRes?.count || 0
  } catch (error) {
    console.error(error)
  }
}
</script>

<style lang="scss" scoped>
* {
  scroll-behavior: smooth;
}
.class-card {
  width: 100%;
  max-width: 250px;
}
.draggable {
  cursor: grab;
}
.drag-target:hover {
  outline: 1px dashed #999;
  border-radius: 0.5rem;
}
.drag-enter {
  outline: 2px dashed green;
  border-radius: 0.5rem;
}
</style>
