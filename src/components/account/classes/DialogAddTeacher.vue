<template>
  <q-dialog v-model="isDialogShow">
    <q-card class="pc-max bg-teal-1" :style="{width: 'clamp(300px, 80%, 960px)', maxWidth: '960px'}">
      <q-card-section>
        <div class="text-h6 q-mb-md">{{ canEdit ? 'Edit' : 'View' }} "{{ selectedClass.name }}" teachers</div>

        <q-card v-if="isBannerShow" class="q-my-md q-pa-md bg-amber-2 flex justify-between items-center" style="max-width: 100%">
          <div class="flex items-center q-gutter-sm">
            <q-icon name="error" color="amber-7" size="sm" />
            <div class="text-grey-8">You can set head teacher or normal teacher under class role label, only one head teacher can be set per class.</div>
          </div>
          <q-btn dense rounded flat size="sm" icon="close" @click.stop="isBannerShow = false" />
        </q-card>

        <div v-if="isUserEditing" class="flex justify-between items-center">
          <q-select
            filled
            v-model="selectedTeacher"
            use-input
            emit-value
            input-debounce="0"
            label="Search teacher"
            clearable
            :options="filterSchoolTeacherList"
            @filter="filterFn"
            style="width: 350px">
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <PubAvatar :src="scope.opt.avatar" />
                </q-item-section>
                <q-item-section class="flex justify-between">
                  <div>
                    <q-item-label>{{ scope.opt.email }}</q-item-label>
                    <q-item-label caption>{{ scope.opt.name[0] }} {{ scope.opt.name[1] || '' }}</q-item-label>
                  </div>
                </q-item-section>
                <q-item-section avatar>
                  <q-btn color="teal-5" label="Add" no-caps @click="onAddTeacher(scope.opt._id, props.selectedClass._id)" />
                </q-item-section>
              </q-item>
            </template>

            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey"> No results </q-item-section>
              </q-item>
            </template>
          </q-select>
          <div class="q-gutter-md row">
            <q-btn label="Add new teacher" unelevated color="primary" no-caps :to="`/account/teacher/add?classId=${props.selectedClass._id}`" />
          </div>
        </div>

        <q-table class="q-my-md" :rows="teacherList" :columns="currentColumns" row-key="name">
          <template v-slot:body-cell-schoolRole="props">
            <q-td :props="props">
              <div>
                <q-chip v-for="role in props.value.role" :key="role" square class="bg-green-3 text-green-9">{{ role }}</q-chip>
              </div>
            </q-td>
          </template>
          <template v-slot:body-cell-classRole="props">
            <q-td :props="props">
              <div>
                <q-btn-dropdown
                  v-if="!currentHeadTeacherId || currentHeadTeacherId === props.row?.uid"
                  unelevated
                  dense
                  class="bg-green-3 text-green-9"
                  no-caps
                  :label="`${currentHeadTeacherId === props.row?.uid ? 'Head teacher' : 'Normal teacher'}`"
                  :disable="!canEdit || isSubjectClass">
                  <q-list class="bg-teal-1">
                    <q-item clickable v-close-popup @click="onClassRoleClick(props.row, true)" :disable="currentHeadTeacherId === props.row?.uid">
                      <q-item-section>
                        <q-item-label
                          >Head teacher
                          <q-icon v-if="currentHeadTeacherId === props.row?.uid" name="check" color="green" />
                        </q-item-label>
                      </q-item-section>
                    </q-item>

                    <q-item clickable v-close-popup @click="onClassRoleClick(props.row, false)">
                      <q-item-section>
                        <q-item-label
                          >Normal teacher
                          <q-icon v-if="currentHeadTeacherId !== props.row?.uid" name="check" color="green" />
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-btn-dropdown>
                <div v-else>
                  <q-chip square class="bg-green-3 text-green-9">Normal teacher</q-chip>
                </div>
              </div>
            </q-td>
          </template>
          <template v-slot:body-cell-email="props">
            <q-td :props="props">
              <!-- <div v-if="!canEdit">/</div> -->
              <!-- <div v-else>{{ props?.row?.email || '-' }}</div> -->
              <div>{{ props?.row?.email || '-' }}</div>
            </q-td>
          </template>
          <template v-slot:body-cell-actions="props">
            <q-td :props="props">
              <div>
                <q-btn
                  v-if="!(isSubjectClass && selectedClass?.host === props.row?.uid)"
                  icon="delete"
                  flat
                  dense
                  class="text-red-4 q-ml-sm"
                  @click="onDeleteTeacher(props.value._id, selectedClass._id)">
                  <q-tooltip anchor="top middle" self="bottom middle">Delete</q-tooltip>
                </q-btn>
              </div>
            </q-td>
          </template>
        </q-table>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn class="bg-grey-7 text-white" unelevated label="Close" no-caps @click="setIsVisible(false)" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, watch, onMounted} from 'vue'
import {date} from 'quasar'
import useSchool from 'src/composables/common/useSchool'
import useClasses from 'src/composables/account/school/useClasses'
import nameFormatter from 'src/utils/formatters/nameFormatter'

const props = defineProps({
  isVisible: {
    type: Boolean,
  },
  setIsVisible: {
    type: Function,
  },
  selectedClass: {
    type: Object,
  },
  initAllData: {
    type: Function,
  },
})

const {schoolId, isUserEditing, userId} = useSchool()
const {patchOneById} = useClasses()

const isBannerShow = ref(true)
const isDialogShow = computed({
  get() {
    return props.isVisible
  },
  set(newValue) {
    props.setIsVisible(newValue)
  },
})

onMounted(async () => {
  // await getTeacherList()
  // await getSchoolTeacherList()
  // filterSchoolTeacherList.value = schoolTeacherList.value.filter((e) => !teacherEmails.includes(e.email))
})

const isSubjectClass = computed(() => props.selectedClass?.type === 'subject')
watch([isSubjectClass, isUserEditing], () => {
  if (isSubjectClass.value && !isUserEditing.value) {
    isBannerShow.value = false
  } else {
    isBannerShow.value = true
  }
})

watch(
  () => props.selectedClass,
  async () => {
    await getTeacherList()
    await getSchoolTeacherList()
    filterSchoolTeacherList.value = schoolTeacherList.value.filter((e) => !teacherEmails.includes(e.email))
  }
)

const schoolTeacherList = ref([])
const getSchoolTeacherList = async () => {
  const res = await App.service('school-user').find({query: {school: schoolId.value, del: false, status: 2, $limit: 1000}})
  schoolTeacherList.value = res.data.map((e) => ({...e, value: e.email, label: e.email}))
}

const selectedTeacher = ref(null)
const filterSchoolTeacherList = ref([])
const filterFn = (val, update) => {
  if (val === '') {
    update(() => {
      const emails = teacherList.value.map((e) => e.email)
      filterSchoolTeacherList.value = schoolTeacherList.value.filter((e) => !emails.includes(e.email))
    })
    return
  }
  update(() => {
    const emails = teacherList.value.map((e) => e.email)
    filterSchoolTeacherList.value = schoolTeacherList.value.filter((e) => !emails.includes(e.email))
    const needle = val.toLowerCase()
    filterSchoolTeacherList.value = filterSchoolTeacherList.value.filter((v) => v.email.includes(needle))
  })
}
const columns = [
  {name: 'name', require: true, label: 'Name', align: 'left', field: (row) => `${nameFormatter(row)}`},
  {name: 'schoolRole', require: true, label: 'School Role', align: 'left', field: (row) => row},
  {name: 'classRole', require: true, label: 'Class Role', align: 'left', field: (row) => row},
  {name: 'email', require: true, label: 'Email', align: 'left', field: (row) => `${row.email}`},
  {name: 'joinAt', require: true, label: 'Join at', align: 'left', field: (row) => `${date.formatDate(row.createdAt, 'YYYY/DD/MM HH:mm')}`},
  {name: 'actions', require: true, label: 'Actions', align: 'left', field: (row) => row},
]

const currentColumns = computed(() => {
  if (isUserEditing.value) {
    return columns
  } else {
    return columns.slice(0, -1)
  }
})

const teacherEmails = []
const teacherList = ref([])
const getTeacherList = async () => {
  const res = await App.service('school-user').find({
    query: {
      school: schoolId.value,
      $or: [{class: props.selectedClass._id}, {head: props.selectedClass._id}],
      status: 2,
      del: false,
      $limit: 1000,
      $sort: {createdAt: -1},
    },
  })
  const host = res.data.find((e) => e.uid === currentHeadTeacherId.value)
  if (host) {
    teacherList.value = [host, ...res.data.filter((e) => e.uid !== currentHeadTeacherId.value)]
  } else {
    teacherList.value = res.data
  }
  if (res?.data) res.data.forEach((e) => teacherEmails.push(e.email))
}

const onAddTeacher = async (teacherId, classId) => {
  await App.service('school-user').patch(teacherId, {$addToSet: {class: classId}})
  filterSchoolTeacherList.value = filterSchoolTeacherList.value.filter((e) => e._id !== teacherId)
  await getTeacherList()
  await props.initAllData()
}

const onDeleteTeacher = async (teacherId, classId) => {
  await App.service('school-user').patch(teacherId, {$pull: {class: classId}})
  const teacher = schoolTeacherList.value.find((e) => e._id === teacherId)
  if (teacher) filterSchoolTeacherList.value.push(teacher)
  await getTeacherList()
  await props.initAllData()
}

const currentHeadTeacherId = computed(() => props.selectedClass?.host || '')
const isHeadTeacher = computed(() => currentHeadTeacherId.value === userId.value)
const canEdit = computed(() => isUserEditing.value || isHeadTeacher.value)

async function onClassRoleClick(data, isHead) {
  const uid = data?.uid || ''
  const classId = props.selectedClass?._id || ''
  if (classId && uid) {
    if (!isHead && uid === currentHeadTeacherId.value) {
      await patchOneById(classId, {host: ''})
    } else {
      await patchOneById(classId, {host: uid})
    }
    await props.initAllData()
  }
}
</script>
