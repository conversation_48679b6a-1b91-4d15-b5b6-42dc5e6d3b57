<template>
  <div>
    <slide-verify
      ref="block"
      :slider-text="text"
      :accuracy="accuracy"
      @again="onAgain"
      @success="onSuccess"
      @fail="onFail"
      @refresh="onRefresh"
      :imgs="imgs"></slide-verify>
    <div class="text-grey-8">{{ msg }}</div>
    <div></div>
  </div>
</template>

<script setup>
import {defineComponent, ref} from 'vue'
import SlideVerify from 'vue3-slide-verify'
import 'vue3-slide-verify/dist/style.css'

const text = 'Slide to right ->'
const accuracy = 1
const imgs = [
  '/v2/img/slide-image/slide-image-1.jpg',
  '/v2/img/slide-image/slide-image-2.jpg',
  '/v2/img/slide-image/slide-image-3.jpg',
  '/v2/img/slide-image/slide-image-5.jpg',
  '/v2/img/slide-image/slide-image-6.jpg',
  '/v2/img/slide-image/slide-image-7.jpg',
  '/v2/img/slide-image/slide-image-8.jpg',
  '/v2/img/slide-image/slide-image-9.jpg',
  '/v2/img/slide-image/slide-image-10.jpg',
]

const emit = defineEmits(['success'])

const msg = ref('')
const block = ref(null)

const onAgain = () => {
  msg.value = 'Detecting non-human action! Try again.'
  block.value?.refresh()
}

const onSuccess = (times) => {
  msg.value = `Verified successfully, Timing: ${(times / 1000).toFixed(1)}s`
  emit('success')
}

const onFail = () => {
  msg.value = 'Verified unsuccessfully. Try again.'
}

const onRefresh = () => {
  msg.value = 'Refresh verification.'
}

const handleClick = () => {
  // 刷新
  block.value?.refresh()
  msg.value = ''
}
</script>
