<template>
  <TextList :isRow="true" :texts="texts" :useChip="true" :maxTextLength="10" :textStyle="`font-size: .75rem`" maxToolTipWidth="15rem" />
</template>

<script setup>
import {computed} from 'vue'
import useAcademicSetting from 'src/composables/account/academic/useAcademicSetting'
import TextList from 'src/components/utils/TextList.vue'

const {currentCurriculumMap, sysCurriculumMap} = useAcademicSetting()

const props = defineProps({
  curriculums: {
    type: Array,
    default: () => [],
  },
})

const texts = computed(() => props.curriculums.map((e) => currentCurriculumMap.value?.[e]?.label || sysCurriculumMap.value?.[e]?.label).filter((e) => !!e))
</script>
