<template>
  <NoData :message="`No Data`" messageColor="grey" />
  <div class="row flex-center">
    <!-- v-if="!isSchool" -->
    <q-btn
      flat
      rounded
      class="flex-center"
      style="border: 1px solid #aaa"
      text-color="teal"
      @click.stop="
        $router.push(`/${isSys ? 'sys' : 'account'}/academic-setting/subject/${subjectMenuMap.learningGoals.value}`, {
          query: {...$route.query, back: $route.path},
        })
      "
      :label="`Go to learning goals setting`"
      no-caps></q-btn>
    <!-- <div v-else>Please contact your admin.</div> -->
  </div>
</template>
<script setup>
import {useRoute, useRouter} from 'vue-router'
import useSchool from 'src/composables/common/useSchool'
import {subjectMenuMap} from 'src/pages/account/academic-setting/utils'

const $route = useRoute()
const $router = useRouter()
const {isSys, isSchool} = useSchool()
</script>
