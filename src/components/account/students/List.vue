<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <section class="q-pa-md" style="max-height: calc(100vh - 10rem)">
    <div class="flex justify-between">
      <q-input text-color="dark" v-model="searchText" dense autofocus outlined placeholder="Search">
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>

      <div v-if="canEdit" class="flex items-center">
        <span>
          <q-btn
            v-show="selectedStudents.length > 0"
            unelevated
            no-caps
            class="q-mx-xs"
            color="indigo-6"
            text-color="grey-1"
            icon-right="more_horiz"
            label="Multiple"
            :disabled="selectedStudents.length === 0">
            <q-menu>
              <q-list>
                <q-item v-if="isMoveClassBtnVisible">
                  <q-btn unelevated color="green-5" size="md" no-caps label="Move Class" @click="onBatchMoveClassClick" />
                </q-item>
                <q-item v-if="isResendBtnVisible">
                  <q-btn unelevated color="orange-5" size="md" no-caps label="Resend" @click="onResend" />
                </q-item>
                <q-item v-if="isDeleteBtnVisible">
                  <q-btn unelevated color="red-5" size="md" no-caps label="Delete" @click="onDelete" />
                </q-item>
                <q-item v-if="isArchiveBtnVisible">
                  <q-btn unelevated color="blue-5" size="md" no-caps label="Archive" @click="onArchive" />
                </q-item>
                <q-item v-if="isRestoreBtnVisible">
                  <q-btn unelevated color="blue-5" size="md" no-caps label="Restore" @click="onRestore" />
                </q-item>
              </q-list>
            </q-menu>
          </q-btn>
          <q-tooltip v-if="selectedStudents.length === 0" anchor="top middle" self="bottom middle">Please select one row at least</q-tooltip>
        </span>

        <q-btn unelevated color="teal-6" size="md" class="q-mx-xs" no-caps label="Add" @click="onAddClick" />
        <q-btn unelevated color="grey-8" size="md" class="q-mx-xs" no-caps label="Bulk Upload" @click="onBulkClick" />
      </div>
    </div>

    <q-table
      class="q-my-md"
      :columns="currentColumns"
      :rows="filteredList"
      row-key="id"
      :selection="canEdit ? 'multiple' : 'none'"
      v-model:selected="selectedStudents"
      v-model:pagination="pagination"
      @request="updateFilteredList"
      :rows-per-page-options="[5, 10, 20]">
      <template v-slot:body-cell-name="props">
        <q-td :props="props">
          <div :class="[props.row?.del ? archivedClass : '']">
            <div>
              <span v-if="props.row?.name?.[0]" class="q-mr-xs">{{ props.row.name[0] }}</span>
              <span v-if="props.row?.name?.[1]">{{ props.row.name[1] }}</span>
            </div>

            <pre>{{ props.id }}</pre>
            <pre>{{ props.password }}</pre>

            <!-- <div>{{ nameFormatter(props.value) }}</div> -->
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-email="props">
        <q-td :props="props">
          <div :class="[props.row?.del ? archivedClass : '']">
            <div v-if="!canEdit">/</div>
            <div v-else>
              <div v-if="isSchool">
                <div v-if="props.row?.email && !props.row?.email?.includes('@classcipe.com')">{{ props?.row?.email || '-' }}</div>
                <div v-else-if="props?.row?.id">{{ props?.row?.id }}</div>
              </div>
              <div v-else>
                <div v-if="props?.row?.mobile">{{ props?.row?.mobile }}</div>
                <div v-else-if="!props.value?.includes('@classcipe.com')">{{ props?.row?.email || '-' }}</div>
              </div>
            </div>
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-parent="props">
        <q-td :props="props">
          <div :class="[props.row?.del ? archivedClass : '']" class="flex q-gutter-sm">
            <div v-if="!canEdit">/</div>
            <div v-else>
              <div v-if="props.value.parent?.email">
                <q-icon v-if="props.value.parent?.status === 2" name="check" color="green" size="1rem">
                  <q-tooltip>Active</q-tooltip>
                </q-icon>
                <q-icon v-else name="close" color="red-4" size="1rem">
                  <q-tooltip>Inactive</q-tooltip>
                </q-icon>
              </div>
              <div>
                <div>{{ props.value.parent?.name?.[0] }} {{ props.value?.parent?.name?.[1] }}</div>
                <div v-if="props.value.parent?.email">{{ props.value.parent?.email }}</div>
              </div>
            </div>
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-class="props">
        <q-td :props="props">
          <div v-if="props.value?.class?.[0]" :class="[props.row?.del ? archivedClass : '']">
            <div>{{ getClassById(props.value.class[0])?.name }}</div>
          </div>
          <div v-else>-</div>
        </q-td>
      </template>

      <template v-slot:body-cell-status="props">
        <q-td :props="props">
          <div :class="[props.value?.del ? archivedClass : '']">
            <div v-if="props.value.del === true">Archived</div>
            <div v-else-if="props.value.status.toString() === '2'">Active</div>
            <!-- <div v-else-if="props.value.status.toString() === '1'">Pending</div> -->
            <div v-else-if="props.value.status.toString() === '0'">Inactive</div>
          </div>
        </q-td>
      </template>

      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <div :class="[props.row?.del ? archivedClass : '']">
            <q-btn v-if="isEditBtnVisible" icon="edit" flat dense class="text-grey-6" @click="onEdit(props.row)" :disable="tab !== 'archive' && props.row?.del">
              <q-tooltip anchor="top middle" self="bottom middle">Edit</q-tooltip>
            </q-btn>
            <q-btn
              v-if="isMoveClassBtnVisible"
              icon="low_priority"
              flat
              dense
              class="text-green-4"
              @click="onMoveClassClick(props.row)"
              :disable="tab !== 'archive' && props.row?.del">
              <q-tooltip anchor="top middle" self="bottom middle">Move Class</q-tooltip>
            </q-btn>
            <q-btn
              v-if="isResendBtnVisible"
              icon="send"
              flat
              dense
              class="text-orange-4"
              @click="onResend(props.row)"
              :disable="tab !== 'archive' && props.row?.del">
              <q-tooltip anchor="top middle" self="bottom middle">Resend</q-tooltip>
            </q-btn>
            <q-btn
              v-if="isArchiveBtnVisible"
              icon="class"
              flat
              dense
              class="text-blue-4"
              @click="onArchive(props.row)"
              :disable="tab !== 'archive' && props.row?.del">
              <q-tooltip anchor="top middle" self="bottom middle">Archive</q-tooltip>
            </q-btn>
            <q-btn
              v-if="isRestoreBtnVisible"
              icon="settings_backup_restore"
              flat
              dense
              class="text-blue-4"
              @click="onRestore(props.row)"
              :disable="tab !== 'archive' && props.row?.del">
              <q-tooltip anchor="top middle" self="bottom middle">Restore</q-tooltip>
            </q-btn>
            <q-btn
              v-if="isDeleteBtnVisible"
              icon="delete"
              flat
              dense
              class="text-red-4"
              @click="onDelete(props.row)"
              :disable="tab !== 'archive' && props.row?.del">
              <q-tooltip anchor="top middle" self="bottom middle">Delete</q-tooltip>
            </q-btn>
            <q-btn
              v-if="props.row?.email && props.row?.email?.includes('@classcipe.com') && isSchool"
              unelevated
              dense
              rounded
              no-caps
              class="q-mx-xs"
              text-color="grey-8"
              icon-right="more_horiz">
              <q-menu>
                <q-list>
                  <q-item>
                    <q-btn class="full-width" unelevated size="md" no-caps label="Show ID & Password" @click="onShowPassword(props.row)" />
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </div>
        </q-td>
      </template>

      <template v-slot:no-data>
        <div class="flex flex-center full-width">
          <NoData message="No student" messageColor="grey" />
        </div>
      </template>
    </q-table>

    <!-- dialogs -->
    <q-dialog v-model="isModalVisible" ref="dialogRef">
      <q-card class="q-dialog-plugin">
        <q-card-section>
          <div class="row justify-between items-center">
            <span>Select a grade</span>
            <PubSelect type="Grade" v-if="schoolIdOrUserId" v-model="selectedGradeId" :school="schoolIdOrUserId" />
          </div>
          <div class="row justify-between items-center">
            <span>Select a class</span>
            <PubSelect
              type="Class"
              v-if="schoolIdOrUserId"
              v-model="selectedClass"
              :grade="selectedGradeId"
              :school="schoolIdOrUserId"
              :classes="selectedClass" />
          </div>
        </q-card-section>
        <q-card-actions class="row justify-between items-start">
          <q-btn class="self-end q-ma-sm" color="negative" flat label="Cancel" @click="isModalVisible = false" />
          <q-btn class="self-end q-ma-sm" color="primary" label="Confirm" :disabled="!selectedClass" @click="onMoveClass()" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <q-dialog v-model="isPasswordDialogShow">
      <div class="q-dialog-plugin q-pa-md bg-teal-1 rounded-lg">
        <div class="q-my-lg">
          <div v-if="currentShowStudent?.id" class="text-h6">Student ID: {{ currentShowStudent.id }}</div>
          <div v-if="currentShowStudent?.password" class="text-h6">Initial password: {{ currentShowStudent.password }}</div>
          <div class="q-mt-lg text-subtitle1">If you have changed your password, please use the updated one.</div>
        </div>
        <div class="flex justify-end q-gutter-md">
          <q-btn rounded flat no-caps label="Cancel" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="isPasswordDialogShow = false" />
          <q-btn rounded flat no-caps label="Copy" icon="done" class="bg-teal-4 text-white" @click="onCopyStudentPassword()" />
        </div>
      </div>
    </q-dialog>
  </section>
</template>

<script setup>
import {useQuasar, copyToClipboard} from 'quasar'
import {computed, ref, watch, onMounted, watchEffect} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import useSchoolPlan from 'src/composables/account/school/useSchoolPlan'

import useClasses from 'src/composables/account/school/useClasses'
import useGrade from 'src/composables/account/academic/useGrade'
import useStudents from 'src/composables/account/school/useStudents'
import useSchool from 'src/composables/common/useSchool'
import {curriculumStore} from 'stores/curriculum'
import {pubStore} from 'stores/pub'

const curriculum = curriculumStore()
const pub = pubStore()
const $q = useQuasar()
const $route = useRoute()
const $router = useRouter()
const {isSchool, isAdmin, userId, schoolIdOrUserId, isUserEditing} = useSchool()
const {schoolPlanCount, getSchoolPlanCount} = useSchoolPlan()
const {list: classList} = useClasses()
const {list: gradeList, getList: getGradeList} = useGrade()
const {getListByPagination: getStudentList, setEditingStudent, patchOneById, deleteOneById, resendEmailById} = useStudents()

const isLoading = ref(false)
const isSchoolMode = ref(false)
const classListMap = computed(() => {
  const map = {}
  classList.value.forEach((e) => (map[e._id] = e))
  return map
})

const archivedClass = 'text-strike text-grey'

const props = defineProps({
  tab: {
    type: String,
    default: 'all',
  },
  tabOptions: {
    type: Array,
  },
})

const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
})

onMounted(async () => {
  await initAllData()
})

const studentList = ref([])
async function updateFilteredList(arg) {
  if (arg?.pagination) pagination.value = arg?.pagination
  const {page, rowsPerPage} = pagination.value
  const ext = {$skip: (page - 1) * rowsPerPage, $limit: rowsPerPage}
  if (classId.value) {
    ext.class = {$in: [classId.value]}
  }
  const res = await getStudentList({...ext})
  studentList.value = res?.data
  pagination.value.rowsNumber = res.total
}

async function initAllData() {
  try {
    $q.loading.show()
    await updateFilteredList()
    await getSchoolPlanCount(true)
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: `server error: ${error.message || 'request failed'}`})
  } finally {
    $q.loading.hide()
  }
}

const columns = [
  {name: 'name', label: 'Name', required: true, align: 'left'},
  {name: 'email', label: 'Email/Phone', required: true, align: 'left', field: (row) => row?.email || row?.mobile},
  {name: 'email', label: 'Email/Classcipe ID', required: true, align: 'left', field: (row) => row?.email || row?.id},
  // {name: 'id', label: 'Id', required: true, align: 'center', field: (row) => row.id},
  {name: 'parent', label: 'Parent', required: true, align: 'left', field: (row) => row},
  {name: 'class', label: 'Class', required: true, align: 'left', field: (row) => row},
  {name: 'status', label: 'Status', required: true, align: 'left', field: (row) => row},
  {name: 'actions', label: 'Actions', required: true, align: 'left'},
]
const currentColumns = computed(() => {
  let list = columns
  if (isSchool.value) {
    list = [list[0], list[2], ...list.slice(3)]
  } else {
    list = [list[0], list[1], ...list.slice(3)]
    list = list.filter((e) => e.name !== 'parent')
  }
  if (canEdit.value) {
    return list
  } else {
    return list.slice(0, -1)
  }
})

const classId = computed(() => $route.query?.classId || '')

const searchText = ref('')
const filteredList = computed(() => {
  let list = Acan.clone(studentList.value)
  if (searchText.value) {
    list = list.filter((e) => {
      const {
        name: [firstName, lastName],
      } = e
      let className = ''
      if (e.class.length) {
        const id = e.class?.[0] || ''
        const target = classListMap.value?.[id]
        if (target) className = target?.name
      }
      return [firstName, lastName, className].some((_) => _.toLowerCase().includes(searchText.value.toLowerCase()))
    })
  }
  // Ref:https://dev.classcipe.com/doc/#/fio/students
  list = list.filter((e) => {
    if (props.tab === 'all') return true
    if (props.tab === 'archive') return e?.del
    if (props.tab === 'active') return e.status === 2 && !e?.del
    if (props.tab === 'inactive') return e.status === 0 && !e?.del
    return true
  })
  return list
})

const isEditBtnVisible = computed(() => ['all', 'active', 'inactive'].includes(props.tab))
const isMoveClassBtnVisible = computed(() => ['all', 'active', 'inactive'].includes(props.tab))
const isResendBtnVisible = computed(() => ['all', 'inactive'].includes(props.tab))
const isArchiveBtnVisible = computed(
  // () => ['all', 'active', 'inactive'].includes(props.tab) && (isAdmin.value || (classId.value && getClassById(classId.value)?.host === userId.value))
  () => ['all', 'active', 'inactive'].includes(props.tab) && isUserEditing.value
)
const isRestoreBtnVisible = computed(() => ['archive'].includes(props.tab))
const isDeleteBtnVisible = computed(() => ['archive'].includes(props.tab))

const selectedStudents = ref([])
const selectedStudentsMap = computed(
  () =>
    selectedStudents.value?.reduce((acc, cur) => {
      acc[cur._id] = cur
      return acc
    }, {}) ?? {}
)

async function onEdit(student) {
  setEditingStudent(student)
  $router.push({path: `/account/student/edit/${student._id}`, query: {back: $route.path}})
}

const selectedGradeId = ref('')
watch(selectedGradeId, () => {
  selectedClass.value = ''
})

const selectedClass = ref('')

const userIds = ref([])
const oldClassId = ref('')
const isModalVisible = ref(false)
async function onMoveClassClick(student) {
  await curriculum.gradeOptions(pub.getSchoolOrUserId)
  userIds.value = [student._id]
  if (student.class?.[0]) {
    const id = student.class[0]
    oldClassId.value = id
    const target = classListMap.value?.[id]
    const existingGradeId = gradeList.value.map((e) => e._id) || []
    if (target) {
      const targetGrade = target?.grade || ''
      if (existingGradeId.includes(targetGrade)) {
        selectedGradeId.value = targetGrade
        selectedClass.value = id
      }
    }
  }
  isModalVisible.value = true
}

async function onBatchMoveClassClick() {
  await curriculum.gradeOptions(pub.getSchoolOrUserId)
  const ids = selectedStudents.value.map((e) => e._id)
  userIds.value = ids
  isModalVisible.value = true
}

async function onMoveClass() {
  $q.dialog({
    title: `Confirm Move Class`,
    message: `Please confirm that you want to move class`,
    cancel: true,
  }).onOk(async () => {
    try {
      for (const id of userIds.value) {
        const studentData = selectedStudentsMap.value?.[id] ?? {}
        const classId = studentData?.class?.[0] || oldClassId.value || ''
        // Note: Need send 2 request to service or it will throw API error
        const firstDto = {$pull: {class: classId}}
        const secondDto = {$addToSet: {class: selectedClass.value}}
        await patchOneById(id, firstDto)
        await patchOneById(id, secondDto)
      }
      $q.notify({type: 'positive', message: 'Student(s) moved successfully'})
      await initAllData()
      userIds.value = []
      selectedStudents.value = []
      isModalVisible.value = false
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Student(s) moved unsuccessfully'})
    } finally {
      selectedClass.value = ''
    }
  })
}

async function onResend(user) {
  $q.dialog({
    title: `Confirm Resend`,
    message: `Please confirm that you want to resend?`,
    cancel: true,
  }).onOk(async () => {
    try {
      let userIds = []
      if (selectedStudents.value.length) {
        userIds = selectedStudents.value.map((e) => e._id)
      } else {
        userIds = [user._id]
      }
      for (const id of userIds) {
        await resendEmailById(id)
      }
      $q.notify({type: 'positive', message: 'Message resend successfully'})
      await initAllData()
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Message resend unsuccessfully'})
    } finally {
      selectedStudents.value = []
    }
  })
}

async function onRestore(user) {
  if (!schoolPlanCount.value?.student) {
    console.error('No student count', schoolPlanCount.value)
    return
  }
  const studentCount = schoolPlanCount.value.student
  if (+studentCount.current >= +studentCount.max) {
    let message = ''
    if (!isSchool.value) {
      message = 'The maximum number of participants has been reached, you cannot restore.'
    }
    $q.dialog({
      title: 'Alert',
      message: 'You have reached the upper limit of the student',
    }).onOk(() => {
      // console.log('OK')
    })
    return
  }
  $q.dialog({
    title: `Confirm Restore`,
    message: `Please confirm that you want to restore student(s)? `,
    cancel: true,
  }).onOk(async () => {
    let userIds = []
    if (selectedStudents.value.length) {
      userIds = selectedStudents.value.map((e) => e._id)
    } else {
      userIds = [user._id]
    }
    try {
      for (const id of userIds) {
        await patchOneById(id, {del: false})
      }
      $q.notify({type: 'positive', message: 'Student(s) restored successfully'})
      await initAllData()
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Student(s) restored unsuccessfully'})
    } finally {
      selectedStudents.value = []
    }
  })
}

async function onArchive(user) {
  $q.dialog({
    title: `Confirm Archive`,
    message: `Please confirm that you want to archive student(s)? `,
    cancel: true,
  }).onOk(async () => {
    let userIds = []
    if (selectedStudents.value.length) {
      userIds = selectedStudents.value.map((e) => e._id)
    } else {
      userIds = [user._id]
    }
    try {
      for (const id of userIds) {
        await patchOneById(id, {del: true})
      }
      $q.notify({type: 'positive', message: 'Student(s) archived successfully'})
      await initAllData()
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Student(s) archived unsuccessfully'})
    } finally {
      selectedStudents.value = []
    }
  })
}

async function onDelete(user) {
  $q.dialog({
    title: `Confirm Delete`,
    message: `Please confirm that you want to delete student(s)? `,
    cancel: true,
  }).onOk(async () => {
    let userIds = []
    if (selectedStudents.value.length) {
      userIds = selectedStudents.value.map((e) => e._id)
    } else {
      userIds = [user._id]
    }
    try {
      for (const id of userIds) {
        await deleteOneById(id)
      }
      $q.notify({type: 'positive', message: 'Student(s) deleted successfully'})
      await initAllData()
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Student(s) deleted unsuccessfully'})
    } finally {
      selectedStudents.value = []
    }
  })
}

function getClassById(id) {
  const target = classList.value.find((e) => e._id === id)
  if (target) return target
  else null
}

async function onAddClick() {
  try {
    await getSchoolPlanCount(true)
    if (!schoolPlanCount.value?.student) {
      console.error('No student count', schoolPlanCount.value)
      return
    }
    const studentCount = schoolPlanCount.value.student
    if (+studentCount.current >= +studentCount.max) {
      $q.dialog({title: 'Alert', message: 'You have reached the upper limit of the student'})
      return
    }
    $router.push({path: '/account/student/add', query: {classId: classId.value, back: $route.path}})
  } catch (error) {
    console.error(error)
  }
}
async function onBulkClick() {
  try {
    await getSchoolPlanCount(true)
    if (!schoolPlanCount.value?.student) {
      console.error('No student count', schoolPlanCount.value)
      return
    }
    const studentCount = schoolPlanCount.value.student
    if (+studentCount.current >= +studentCount.max) {
      $q.dialog({title: 'Alert', message: 'You have reached the upper limit of the student'})
      return
    }
    $router.push({path: '/account/student/bulk', query: {classId: classId.value, back: $route.path}})
  } catch (error) {
    console.error(error)
  }
}

const isPasswordDialogShow = ref(false)
const currentShowStudent = ref(null)
function onShowPassword(data) {
  currentShowStudent.value = data
  isPasswordDialogShow.value = true
}
function onCopyStudentPassword() {
  copyToClipboard(currentShowStudent.value.password)
  $q.notify({type: 'positive', message: 'Student password copied successfully'})
  isPasswordDialogShow.value = false
}

const currentClass = computed(() => classList.value.find((e) => e._id === classId.value))
const currentHeadTeacherId = computed(() => currentClass.value?.host || '')
const isHeadTeacher = computed(() => currentHeadTeacherId.value === userId.value)
const canEdit = computed(() => isUserEditing.value || isHeadTeacher.value)
</script>

<style lang="scss" scoped>
.rounded-lg {
  border-radius: 0.5rem;
}
</style>
