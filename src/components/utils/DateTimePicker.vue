<template>
  <q-input
    filled
    :modelValue="modelValue"
    @update:modelValue="(v) => emit('update:modelValue', v)"
    :rules="[(value) => checkTimeValid(value) || 'Deadline can not be before start time']">
    <template v-slot:prepend>
      <q-icon name="event" class="cursor-pointer">
        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
          <q-date :modelValue="modelValue" @update:modelValue="onUpdateDate" :mask="TIME_FORMAT" :options="dateOptionsFn">
            <div class="row items-center justify-end">
              <q-btn v-close-popup label="Close" color="primary" flat />
            </div>
          </q-date>
        </q-popup-proxy>
      </q-icon>
    </template>
    <template v-slot:append>
      <q-icon name="access_time" class="cursor-pointer">
        <q-popup-proxy cover transition-show="scale" transition-hide="scale">
          <q-time :modelValue="modelValue" @update:modelValue="onUpdateTime" :mask="TIME_FORMAT" format24h :options="timeOptionsFn">
            <div class="row items-center justify-end">
              <q-btn v-close-popup label="Close" color="primary" flat />
            </div>
          </q-time>
        </q-popup-proxy>
      </q-icon>
    </template>
  </q-input>
</template>

<script setup>
import {ref, onMounted, watchEffect} from 'vue'
import {date, copyToClipboard} from 'quasar'
import {TIME_FORMAT} from 'src/boot/const'

const props = defineProps({
  modelValue: {
    type: String || null,
    default: null,
  },
  afterTime: {
    type: Date || String,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'update:isValid'])

function dateOptionsFn(value) {
  if (props.afterTime) {
    const formattedValue = new Date(value)
    const startDay = new Date(date.addToDate(new Date(props.afterTime), {days: -1}))
    return +formattedValue >= +startDay
  } else {
    return true
  }
}
function timeOptionsFn(hr, min, sec) {
  if (props.afterTime) {
    hr = hr || 24
    min = min || 60
    sec = sec || 60
    const inputDate = new Date(props.modelValue)
    const startDate = new Date(props.afterTime)
    if (inputDate.toISOString().split('T')[0] > startDate.toISOString().split('T')[0]) return true

    const startHr = startDate.getHours() || 0
    const startMin = startDate.getMinutes() || 0
    const startSec = startDate.getSeconds() || 0
    const startValue = startHr * 60 * 60 + startMin * 60 + startSec
    const value = hr * 60 * 60 + min * 60 + sec

    if (startHr === hr && startMin === min) return false
    return +new Date(startDate) - (+new Date(inputDate.toLocaleDateString()) + value * 1000) < 0
  } else {
    return true
  }
}

function checkTimeValid(value) {
  let isValid = true
  if (props.afterTime) {
    const formattedValue = new Date(value)
    const today = new Date()
    isValid = +formattedValue >= +today
  }
  emit('update:isValid', isValid)
  return isValid
}

function onUpdateDate(v) {
  console.log(v)
  emit('update:modelValue', v)
}

function onUpdateTime(v) {
  console.log(v)
  emit('update:modelValue', v)
}
</script>
