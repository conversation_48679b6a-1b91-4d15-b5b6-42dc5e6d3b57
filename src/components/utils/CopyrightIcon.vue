<template>
  <q-icon size="sm" color="primary" name="copyright" class="q-mr-sm">
    <q-tooltip anchor="bottom middle" self="top middle" class="text-subtitle1" style="font-size: 0.8rem">
      <div style="max-width: 480px">
        {{ generateCopyrightText(curriculum || 'default', subtitle) }}
      </div>
    </q-tooltip>
  </q-icon>
</template>
<script setup>
import {generateCopyrightText} from 'src/boot/const'

const props = defineProps({
  curriculum: {
    type: String,
    default: '',
  },
  subtitle: {
    type: String,
    default: '',
  },
})
</script>
