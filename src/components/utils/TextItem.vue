<template>
  <div v-if="useChip">
    <q-chip v-if="text.length > maxTextLength" :style="textStyle">
      {{ text.slice(0, maxTextLength) }}...
      <span v-if="count > 1">{{ count - 1 }}+</span>
      <q-tooltip class="text-subtitle1 tooltip-item" :style="[useTextarea ? 'white-space: pre-wrap' : '']">
        <div :style="`max-width: ${maxToolTipWidth}`">
          {{ text }}
        </div>
      </q-tooltip>
    </q-chip>
    <q-chip v-else :style="textStyle"
      >{{ text }}
      <span v-if="count > 1">{{ count - 1 }}+</span>
    </q-chip>
  </div>

  <div v-else>
    <div v-if="text.length > maxTextLength" :style="textStyle">
      {{ text.slice(0, maxTextLength) }}...
      <q-tooltip class="text-subtitle1 tooltip-item" :style="[useTextarea ? 'white-space: pre-wrap' : '']">
        <div :style="`max-width: ${maxToolTipWidth}`">
          {{ text }}
        </div>
      </q-tooltip>
    </div>
    <div v-else :style="textStyle">{{ text }}</div>
  </div>
</template>

<script setup>
const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  textStyle: {
    type: String,
    default: '',
  },
  maxTextLength: {
    type: Number,
    default: 1000,
  },
  maxToolTipWidth: {
    type: String,
    default: '10rem',
  },
  useChip: {
    type: Boolean,
    default: false,
  },
  useTextarea: {
    type: Boolean,
    default: false,
  },
  count: {
    type: Number,
    default: 0,
  },
})
</script>

<style lang="scss" scoped>
.tooltip-item {
  width: 100%;
  word-break: normal;
}
</style>
