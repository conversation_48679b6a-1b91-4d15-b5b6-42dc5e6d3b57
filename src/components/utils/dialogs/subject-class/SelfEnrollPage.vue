<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div>
    <q-card v-if="isBannerShow" class="q-pa-md bg-amber-2 flex justify-between items-center full-width" flat>
      <div class="flex items-center q-gutter-sm">
        <q-icon name="error" color="amber-7" size="sm" />
        <div class="text-grey-8">The students self-enrollment is disabled for this class</div>
      </div>
      <q-btn dense rounded flat size="sm" icon="close" @click.stop="isBannerShow = false" />
    </q-card>

    <PubTopBanner title="Self-enroll application" :isShowMenu="false" />

    <q-page class="column no-wrap pc-max">
      <!-- <NavBar :plist="['account', {label: 'Classes', icon: 'widgets'}]" /> -->
      <div class="flex justify-between items-center q-mt-sm">
        <AppTab :tab="classTab" :tabOptions="tabOptions" @change="(str) => (classTab = str)" />

        <q-btn
          v-if="classTab === 'standard' && isUserEditing"
          :disable="!isAdmin"
          flat
          rounded
          class="text-teal q-mr-sm"
          style="border: 1px solid #aaa"
          size="md"
          label="Set grade"
          no-caps
          @click="goToSetGrade">
          <q-tooltip v-if="!isAdmin">Please contact your school admin to set the data</q-tooltip>
        </q-btn>

        <!-- @click="openOldUrl('/manage/academic')" -->
        <q-btn
          v-if="classTab === 'subject' && isUserEditing"
          flat
          rounded
          class="text-teal q-mr-sm"
          style="border: 1px solid #aaa"
          size="md"
          no-caps
          label="Set term"
          @click="goToSetTerm" />
      </div>

      <!-- TODO: -->
      <q-table
        class="q-my-md"
        :columns="currentColumns"
        :rows="filteredList"
        row-key="id"
        :selection="canEdit ? 'multiple' : 'none'"
        v-model:selected="selectedStudents"
        v-model:pagination="pagination"
        @request="updateFilteredList"
        :rows-per-page-options="[5, 10, 20]">
        <template v-slot:body-cell-name="props">
          <q-td :props="props">
            <div :class="[props.row?.del ? '' : '']">
              <div>{{ nameFormatter(props.value) }}</div>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-email="props">
          <q-td :props="props">
            <div :class="[props.row?.del ? '' : '']">
              <div v-if="isSchool">
                <div v-if="!canEdit && false">/</div>
                <div v-else-if="props.row?.email && !props.row?.email?.includes('@classcipe.com')">{{ props?.row?.email || '-' }}</div>
                <div v-else-if="props?.row?.id">{{ props?.row?.id }}</div>
              </div>
              <div v-else>
                <div v-if="!canEdit && false">/</div>
                <div v-else-if="props?.row?.mobile">{{ props?.row?.mobile }}</div>
                <div v-else-if="!props.value?.includes('@classcipe.com')">{{ props?.row?.email || '-' }}</div>
              </div>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-approvedAt="props">
          <q-td :props="props">
            <div :class="[props.row?.del ? '' : '']">
              <div>{{ date.formatDate(props.row?.approvedAt || new Date(), TIME_FORMAT) }}</div>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-actions="props">
          <q-td :props="props">
            <div :class="[props.row?.del ? '' : '']">
              <q-btn icon="visibility" flat dense class="text-green-4" @click="onPreviewClick(props.row)" :disable="tab !== 'archive' && props.row?.del" />
            </div>
          </q-td>
        </template>

        <template v-slot:no-data>
          <div class="flex flex-center full-width">
            <NoData message="No student" messageColor="grey" />
          </div>
        </template>
      </q-table>
    </q-page>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {date} from 'quasar'
import {useRoute, useRouter} from 'vue-router'

import {TIME_FORMAT} from 'src/boot/const'
import AppTab from 'components/pub/AppTab.vue'

import nameFormatter from 'src/utils/formatters/nameFormatter.js'
import useSchool from 'src/composables/common/useSchool'
// import useClasses from 'src/composables/account/school/useClasses'
import useClassApply from 'src/composables/account/school/useClassApply.js'

const $router = useRouter()
const $route = useRoute()
const {isSchool, isUserEditing, isAdmin, isSys} = useSchool()
// const {list} = useClasses()
const {getAppliedStudents} = useClassApply()

const classId = computed(() => $route.query?.classId || '')
const isBannerShow = ref(true)
const tabOptions = computed(() => {
  return [
    {name: 'pending', label: `Pending`},
    {name: 'approved', label: `Approved`},
    {name: 'rejected', label: `Rejected`},
  ]
})

function goToSetGrade() {
  $router.push({
    path: `/${isSys.value ? 'sys' : 'account'}/academic-setting/subject/gradeSetting`,
    query: {back: $route.path},
  })
}

function goToSetTerm() {
  $router.push({
    path: '/account/term',
    query: {back: $route.path},
  })
}

const classTab = ref('')
// watch(classTab, (newValue) => {
//   if (!classTab.value) return
//   $router.replace(`/account/classes/${newValue}`)
// })
// watch($route, () => {
//   classTab.value = $route.params.classTab
// })

const canEdit = ref(true)

const columns = [
  {name: 'name', label: 'Name', required: true, align: 'left'},
  {name: 'email', label: 'Email/Phone', required: true, align: 'left', field: (row) => row?.email || row?.mobile},
  {name: 'approvedAt', label: 'Approved at', required: true, align: 'left', field: (row) => row},
  {name: 'actions', label: 'Actions', required: true, align: 'left'},
]
const currentColumns = computed(() => {
  let list = columns
  if (canEdit.value) {
    return list
  } else {
    return list.slice(0, -1)
  }
})

const selectedStudents = ref([])
// const selectedStudentsMap = computed(
//   () =>
//     selectedStudents.value?.reduce((acc, cur) => {
//       acc[cur._id] = cur
//       return acc
//     }, {}) ?? {}
// )

const tab = ref('pending')
const searchText = ref('')
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
})

const studentList = ref([])
async function updateFilteredList(arg) {
  console.warn('hi')
  if (arg?.pagination) pagination.value = arg?.pagination
  const {page, rowsPerPage} = pagination.value
  const ext = {$skip: (page - 1) * rowsPerPage, $limit: rowsPerPage}
  const res = await getAppliedStudents({class: classId.value, ...ext})
  console.warn(res)
  studentList.value = res?.data
  pagination.value.rowsNumber = res.total
}

const filteredList = computed(() => {
  let list = Acan.clone(studentList.value)
  if (searchText.value) {
    list = list.filter((e) => {
      const {
        name: [firstName, lastName],
      } = e
      return [firstName, lastName].some((_) => _.toLowerCase().includes(searchText.value.toLowerCase()))
    })
  }
  // Ref:https://dev.classcipe.com/doc/#/fio/students
  list = list
    .filter((e) => {
      // if (tab.value === 'all') return true
      // if (tab.value === 'archive') return e?.del
      if (tab.value === 'pending') return e.status === 2 && !e?.del
      if (tab.value === 'approved') return e.status === 0 && !e?.del
      if (tab.value === 'rejected') return e.status === -1 && !e?.del
    })
    .filter((e) => {
      if (classId.value) {
        return e.class.includes(classId.value)
      } else {
        return true
      }
    })
  return list
})

function onPreviewClick(item) {
  console.log(item)
}

onMounted(async () => {
  await updateFilteredList({})

  // const options = tabOptions.value.map((e) => e.name)
  // if (options.includes($route.params.classTab)) {
  //   classTab.value = $route.params.classTab
  // } else {
  //   classTab.value = options[0]
  // }
})
</script>

<style lang="scss"></style>
