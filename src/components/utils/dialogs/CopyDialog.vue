<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" class="rounded-xl">
    <div class="bg-teal-1 column no-wrap q-gutter-sm q-pa-lg justify-between" style="min-width: 300px; max-width: 360px; border-radius: 0.75rem">
      <div class="text-subtitle1 text-bold">{{ title }}</div>
      <div class="text-grey-9">{{ message }}</div>

      <div class="flex justify-end q-mt-lg">
        <q-btn class="q-ml-sm q-px-md bg-grey-6" rounded dense flat color="grey-1" no-caps :label="cancelButtonLabel" @click="onDialogCancel" />
        <q-btn v-if="!isCopied" class="q-ml-sm q-px-md bg-teal" rounded dense flat color="grey-2" no-caps :label="okButtonLabel" @click="onOKClick" />
        <q-btn
          v-else
          class="q-ml-sm q-px-md bg-teal"
          rounded
          dense
          flat
          color="grey-2"
          no-caps
          :label="isCopied ? 'Copied!' : okButtonLabel"
          :icon="isCopied ? 'check' : ''"
          :disable="isCopied"
          @click="onOKClick" />
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {ref} from 'vue'
import {useDialogPluginComponent, copyToClipboard} from 'quasar'

const props = defineProps({
  // ...your custom props
  title: {
    type: String,
    default: 'Confirm',
  },
  message: {
    type: String,
    default: 'Are you sure you want to delete this?',
  },
  okButtonLabel: {
    type: String,
    default: 'Copy',
  },
  cancelButtonLabel: {
    type: String,
    default: 'Cancel',
  },
  copyContent: {
    type: String,
    require: true,
  },
})

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const isCopied = ref(false)
async function onOKClick() {
  await copyToClipboard(props.copyContent)
  isCopied.value = true
  setTimeout(() => {
    onDialogOK()
  }, 1000)
}
</script>
