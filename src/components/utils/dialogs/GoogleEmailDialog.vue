<template>
  <q-dialog v-model="isDialogShow" persistent>
    <div class="column no-wrap flex-center bg-teal-1 q-pa-md rounded-lg">
      <div class="q-pa-lg">
        <div class="text-subtitle2 q-mb-md">Please ensure the email address below is the one you used to sign up for Classcipe.</div>
        <div>
          Your google's email: <span class="text-bold">{{ googleEmail || '-' }}</span>
        </div>

        <!-- <pre>{{ googleId }}</pre> -->
        <!-- <pre>{{ accountEmail }}</pre> -->
      </div>
      <div class="flex justify-end q-gutter-md full-width">
        <!-- <q-btn -->
        <!--   rounded -->
        <!--   flat -->
        <!--   no-caps -->
        <!--   label="Cancel" -->
        <!--   icon="arrow_back" -->
        <!--   class="text-grey-7" -->
        <!--   style="border: 1px solid" -->
        <!--   :disable="!googleEmail" -->
        <!--   @click="isDialogShow = false" /> -->

        <q-btn rounded flat no-caps label="Confirm" icon="done" class="bg-teal-4 text-white" @click="onSaveGoogleEmail" />
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {useQuasar} from 'quasar'
import {ref, computed, onMounted, watch} from 'vue'
import {useRoute} from 'vue-router'
import {pubStore} from 'stores/pub'
// import useUser from 'src/composables/common/useUser'
import useSchool from 'src/composables/common/useSchool'

const $q = useQuasar()
const pub = pubStore()
const $route = useRoute()
const {isLogin} = useSchool()
// const {patchOneById} = useUser()

const isAddOnPath = computed(() => $route?.path?.includes('/addon/'))
const accountEmail = computed(() => pub?.user?.email ?? '')
const googleId = computed(() => pub?.user?.google ?? '')

const isDialogShow = ref(false)
const googleEmail = ref('')
async function getGoogleEmail() {
  try {
    const res = await App.service('users').get('googleEmail')
    const email = res?.email ?? ''
    if (!email) {
      $q.notify({type: 'negative', message: "Can't get your goolge email! Please contact admin."})
      isDialogShow.value = false
      return
    }
    googleEmail.value = email
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: "Can't get your goolge email! Please contact admin."})
  }
}

async function onSaveGoogleEmail() {
  try {
    // const email = googleEmail.value
    // const res = await patchOneById({email})
    // pub.user = {...pub.user, ...res}
    const res = await App.service('users').get('googleEmailSync')
    if (!res) $q.notify({type: 'negative', message: 'Updated your goolge email unsuccessfully!'})
    pub.user = {...pub.user, email: res?.email || ''}
    $q.notify({type: 'positive', message: 'Updated your goolge email successfully!'})
  } catch (error) {
    console.error(error)
    $q.notify({type: 'negative', message: 'Updated your goolge email unsuccessfully!'})
  } finally {
    isDialogShow.value = false
  }
}

onMounted(async () => {
  if (!isLogin.value) return
  // console.log(isAddOnPath.value)
  if (isAddOnPath.value) return
  // console.warn('google', pub?.user?.google)
  if (googleId.value && !accountEmail.value) showDialog()
})

watch(isLogin, () => {
  if (isLogin.value && !isAddOnPath.value && googleId.value && !accountEmail.value) {
    setTimeout(() => {
      showDialog()
    }, 5500)
  }
})

async function showDialog() {
  const iframeUrl = document.getElementById('addon')?.contentWindow?.location?.href ?? ''
  if (iframeUrl && !iframeUrl.includes('/addon/')) return
  if (isAddOnPath.value) return
  await getGoogleEmail()
  isDialogShow.value = true
}
</script>
