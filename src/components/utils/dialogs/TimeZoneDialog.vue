<template>
  <q-dialog v-model="isDialogShow" persistent>
    <div class="column no-wrap flex-center bg-teal-1 q-pa-md rounded-lg">
      <div class="q-pa-lg">
        <div class="text-subtitle2 q-mb-md">Your current time zone is different from your account time zone. Do you want to change your time zone?</div>
        <div>
          Your current time zone: <span class="text-bold">{{ currentTimeZone }}</span>
        </div>
        <div>
          Your account time zone: <span class="text-bold">{{ accountTimeZone }}</span>
        </div>
        <TimeZoneInput class="full-width q-mt-md" v-model="timeZone" @update="onPatchTimeZone" />
      </div>
      <div class="flex justify-end q-gutter-md">
        <q-btn
          rounded
          flat
          no-caps
          label="Cancel"
          icon="arrow_back"
          class="text-grey-7"
          style="border: 1px solid"
          :disable="!timeZone"
          @click="isDialogShow = false" />
        <q-btn rounded flat no-caps label="Confirm" icon="done" class="bg-teal-4 text-white" @click="onSaveTimeZone" />
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted, watch} from 'vue'
import {useRoute} from 'vue-router'
import {pubStore} from 'stores/pub'
import useUser from 'src/composables/common/useUser'
import {isTimeZoneDialogShowed} from 'src/composables/useGlobalStates'
import useSchool from 'src/composables/common/useSchool'
import TimeZoneInput from 'src/components/utils/inputs/TimeZoneInput.vue'

const pub = pubStore()
const $route = useRoute()
const {isLogin} = useSchool()
const {patchOneById} = useUser()

const isDialogShow = ref(false)
const timeZone = ref('')
const tz = ref(0)

const isAddOnPath = computed(() => $route.path.includes('/addon/'))

function onPatchTimeZone(option) {
  timeZone.value = option.value
  tz.value = option.tz
}

async function onSaveTimeZone() {
  const res = await patchOneById({timeZone: timeZone.value, tz: tz.value})
  pub.user = {...pub.user, ...res}
  isDialogShow.value = false
}

const currentTimeZone = ref('')
const accountTimeZone = computed(() => pub?.user?.timeZone ?? '')
onMounted(() => {
  if (!isLogin.value) return
  currentTimeZone.value = Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone ?? ''
  if (!accountTimeZone.value || currentTimeZone.value !== accountTimeZone.value) {
    showDialog()
  }
  if (currentTimeZone.value || pub?.user?.timeZone) {
    timeZone.value = currentTimeZone.value || accountTimeZone.value || ''
  }
})

watch(isLogin, () => {
  if (!isLogin.value) return
  if (pub?.user?.guest || pub?.user?.anonymous) return
  if (!isTimeZoneDialogShowed.value && currentTimeZone.value !== accountTimeZone.value) {
    showDialog()
  }
})

let timer = null
function showDialog() {
  if (!currentTimeZone.value) return
  if (pub?.user?.guest || pub?.user?.anonymous) return
  if (timer) clearTimeout(timer)
  timer = setTimeout(() => {
    const iframeUrl = document.getElementById('addon')?.contentWindow?.location?.href ?? ''
    if (iframeUrl) {
      if (!iframeUrl.includes('/addon/')) isDialogShow.value = true
    } else {
      if (!isAddOnPath.value) isDialogShow.value = true
    }
    isTimeZoneDialogShowed.value = true
  }, 3000)
}
</script>
