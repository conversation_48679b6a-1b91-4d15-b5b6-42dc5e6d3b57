<template>
  <div v-if="texts.length <= maxTexts" :class="[isRow ? 'flex items-center full-width' : '']">
    <TextItem
      v-for="text in texts"
      :key="text"
      :text="text"
      :maxTextLength="maxTextLength"
      :useChip="useChip"
      :maxToolTipWidth="maxToolTipWidth"
      :textStyle="textStyle" />
  </div>

  <div v-else :class="[isRow ? 'flex items-center' : '']">
    <TextItem
      v-for="text in texts.slice(0, maxTexts)"
      :key="text"
      :text="text"
      :maxTextLength="maxTextLength"
      :useChip="useChip"
      :maxToolTipWidth="maxToolTipWidth"
      :textStyle="textStyle" />

    <q-btn v-if="useButton" class="text-teal" no-caps flat dense rounded :label="`+ ${texts.length - maxTexts} more`">
      <q-menu class="bg-grey-8 text-grey-2 text-subtitle1">
        <q-list>
          <q-item v-for="text in texts.slice(maxTexts, texts.length)" :key="text" v-close-popup>
            <q-item-section>{{ text }}</q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </q-btn>

    <div v-else>
      <div class="text-teal">+ {{ texts.length - maxTexts }} more</div>
      <q-tooltip class="text-subtitle1" :style="[`width: ${maxToolTipWidth}`, 'word-break: normal']">
        <div v-for="text in texts.slice(maxTexts, texts.length)" :key="text">
          <q-chip v-if="useChip">{{ text }}</q-chip>
          <div v-else>{{ text }}</div>
        </div>
      </q-tooltip>
    </div>
  </div>
</template>

<script setup>
import TextItem from 'src/components/utils/TextItem.vue'

const props = defineProps({
  texts: {
    type: Array,
    default: () => [],
  },
  textStyle: {
    type: String,
    default: '',
  },
  maxTexts: {
    type: Number,
    default: 2,
  },
  maxTextLength: {
    type: Number,
    default: 1000,
  },
  maxToolTipWidth: {
    type: String,
    default: '10rem',
  },
  useChip: {
    type: Boolean,
    default: false,
  },
  useButton: {
    type: Boolean,
    default: false,
  },
  isRow: {
    type: Boolean,
    default: false,
  },
})
</script>
