<template>
  <q-select
    outlined
    label="Timezone"
    :modelValue="modelValue"
    @update:modelValue="onUpdate"
    :options="currentTimeZoneOptions"
    option-value="value"
    :display-value="modelValue ? `${modelValue} (${timeZoneOptions?.find((e) => e.value === modelValue)?.differentHour})` : '-'"
    use-input
    hide-selected
    fill-input
    @filter="filterFn">
    <template v-slot:prepend>
      <q-icon name="search" />
    </template>

    <template v-slot:option="scope">
      <q-item v-bind="scope.itemProps">
        <q-item-section>
          <q-item-label>{{ scope.opt.label }}</q-item-label>
          <q-item-label caption>{{ scope.opt.differentHour }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:no-option>
      <q-item>
        <q-item-section class="text-grey"> No results </q-item-section>
      </q-item>
    </template>
  </q-select>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {convertTzValueByTimeZone} from 'src/utils/converters'
import useCountryCode from 'src/composables/common/useCountryCode'

const {timeZoneOptions} = useCountryCode()

const props = defineProps({
  modelValue: {
    type: [Object, String, null],
    default: null,
  },
})
const emit = defineEmits(['update:modelValue', 'update'])

const currentTimeZoneOptions = ref([])
onMounted(() => {
  currentTimeZoneOptions.value = timeZoneOptions.value
})

function filterFn(val, update) {
  if (val === '') {
    update(() => {
      currentTimeZoneOptions.value = timeZoneOptions.value
    })
  } else {
    update(() => {
      const needle = val.toLowerCase()
      currentTimeZoneOptions.value = timeZoneOptions.value.filter(
        (e) => e.value.toLowerCase().indexOf(needle) > -1 || e.differentHour.toLowerCase().indexOf(needle) > -1
      )
    })
  }
}

function onUpdate(option) {
  const timeZone = option?.value ?? option
  emit('update:modelValue', timeZone)
  const tz = convertTzValueByTimeZone(timeZone)
  const dto = {
    ...option,
    tz,
  }
  emit('update', dto)
}
</script>
