<template>
  <div>
    <MazPhoneNumberInput
      :modelValue="modelValue"
      @update:modelValue="(val) => emit('update:modelValue', val)"
      v-model:country-code="currentCountryCode"
      show-code-on-list
      :onlyCountries="onlyCountries"
      @update="onUpdateResult"
      :error="error"
      :class="[error ? 'm-error-outline' : '']"
      no-border />

    <!-- <pre style="text-align: start">{{ results }}</pre> -->
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import 'maz-ui/css/main.css'
import MazPhoneNumberInput from 'maz-ui/components/MazPhoneNumberInput'

import useCountryCode from 'src/composables/common/useCountryCode'

const {countryList} = useCountryCode()

const props = defineProps({
  modelValue: {
    type: String,
    require: true,
  },
  countryCode: {
    type: String,
    default: 'NZ',
  },
  error: {
    type: Boolean,
  },
})

const emit = defineEmits(['update:modelValue', 'update:countryCode', 'update:isValid'])

const currentCountryCode = ref('')
const results = ref(null)

onMounted(() => {
  console.log(props.countryCode)
  currentCountryCode.value = props.countryCode
  console.log(currentCountryCode.value)
  console.log(currentCountryCode.value)
})
const onlyCountries = computed(() => {
  if (countryList.value?.length) return countryList.value.map((e) => e.code)
  return ['NZ', 'AU']
})

// const preferredCountries = ['NZ', 'AU', 'TW', 'CN']

function onUpdateResult(e) {
  results.value = e
  emit('update:isValid', e.isValid)
  emit('update:modelValue', e.e164)
  emit('update:countryCode', e.countryCode)
}
</script>

<style lang="scss" scoped>
:global(:root) {
  --maz-border-radius: 0.3rem;
}
.m-error-outline {
  outline: 1.5px #e57373 solid;
  border-radius: 0.25rem;
}
</style>
