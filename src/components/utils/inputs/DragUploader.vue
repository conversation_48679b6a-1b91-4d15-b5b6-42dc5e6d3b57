<template>
  <div>
    <div
      ref="uploadZone"
      :class="wrapperClassString"
      :style="isReadonly ? wrapperDisableStyleObject : wrapperStyleObject"
      @drop.prevent="onDrop"
      @click.prevent="uploadFn">
      <q-btn
        flat
        round
        dense
        :icon="isReadonly ? 'o_motion_photos_off' : 'o_file_upload'"
        size="1.75rem"
        :color="isReadonly ? 'grey' : 'teal'"
        :disable="isReadonly" />
      <p v-if="isReadonly" class="q-mt-md text-grey">Upload disabled</p>
      <p v-else class="q-mt-md text-teal">Click to select or drag to upload</p>
    </div>

    <q-inner-loading :showing="isUploading" label-class="text-teal" class="z-max">
      <q-spinner-ball color="teal" size="4em" />
      <div style="color: teal">Please Wait...</div>
    </q-inner-loading>
  </div>
</template>

<script setup>
import {ref, computed, onMounted, onUnmounted} from 'vue'
import {DEFAULT_FILE_TYPE} from 'src/boot/const'

const props = defineProps({
  fileType: {
    type: String,
    default: DEFAULT_FILE_TYPE,
  },
  maxFileSize: {
    type: Number,
    // max size 100 MB
    default: 100 * 1000 * 1000,
  },
  isReadonly: {
    type: Boolean,
    default: false,
  },
  wrapperClassString: {
    type: String,
    default: 'rounded-lg column flex-center q-pa-md q-my-md bg-teal-1 cursor-pointer',
  },
  wrapperStyleObject: {
    type: Object,
    default: () => ({
      border: '2px dashed teal',
    }),
  },
  wrapperDisableStyleObject: {
    type: Object,
    default: () => ({
      border: '2px dashed #aaa',
    }),
  },
  hasVideo: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['uploaded'])

const fileSizeMb = computed(() => props.maxFileSize / 1000 / 1000)

const uploadZone = ref(null)
const isUploading = ref(false)

const events = ['dragenter', 'dragover', 'dragleave', 'drop']
onMounted(async () => {
  events.forEach((eventName) => {
    uploadZone.value.addEventListener(eventName, preventDefaults)
  })
})

onUnmounted(() => {
  events.forEach((eventName) => {
    if (uploadZone.value) uploadZone.value.removeEventListener(eventName, preventDefaults)
  })
})

function preventDefaults(e) {
  e.preventDefault()
}

async function onDrop(e) {
  if (props.isReadonly) return
  try {
    isUploading.value = true
    const file = e.dataTransfer?.files?.[0] ?? null
    if (!file) return
    if (file.size > props.maxFileSize) {
      $q.notify({type: 'negative', message: `The file is too large. The maximum file size is ${fileSizeMb.value}.`})
      return
    }
    const isFileTypeValid = checkFileTypeValid(file)
    if (!isFileTypeValid) {
      $q.notify({type: 'negative', message: `Invalid file type. Only ${props.fileType} files are allowed`})
      return
    }
    const rs = await Fn.fileUpLoadUi(file)
    if (props.hasVideo && rs?.mime?.includes('video')) {
      $q.notify({type: 'negative', message: `Only one video can be uploaded.`})
      return
    }
    emit('uploaded', rs)
  } catch (error) {
    console.error(error)
    if (error?.message) {
      $q.notify({type: 'negative', message: error?.message})
    }
  } finally {
    isUploading.value = false
  }
}
function checkFileTypeValid(file) {
  if (props?.fileType) {
    const reg = props?.fileType.replace(/,/g, '|')
    if (file.type.match(reg)) return true
    else return false
  }
  return false
}

async function uploadFn() {
  if (props.isReadonly) return
  try {
    isUploading.value = true
    const rs = await Fn.fileUpLoadUiX(props.fileType)
    if (!rs) return
    if (rs.size > props.maxFileSize) {
      $q.notify({type: 'negative', message: `The file is too large. The maximum file size is ${fileSizeMb.value}.`})
      return
    }
    if (props.hasVideo && rs?.mime?.includes('video')) {
      $q.notify({type: 'negative', message: `Only one video can be uploaded.`})
      return
    }
    if (rs.message) throw new Error(rs.message)
    emit('uploaded', rs)
  } catch (error) {
    console.error(error)
    if (error?.message) {
      $q.notify({type: 'negative', message: error?.message})
    }
  } finally {
    isUploading.value = false
  }
}
</script>
