<template>
  <q-input
    :modelValue="modelValue"
    @update:modelValue="onUpdate"
    type="textarea"
    rows="2"
    autogrow
    outlined
    :placeholder="placeholder"
    :counter="counter"
    :maxlength="maxlength"
    :class="{'border-error': modelValue?.length > maxlength}"
    :error="modelValue?.length > maxlength"
    error-message="Character limit exceeded!">
    <template v-slot:append>
      <q-btn round dense flat :color="isRecording ? 'primary' : 'grey-8'" icon="mic" @click="() => toggleRecording()" />
    </template>
  </q-input>
</template>

<script setup>
import {onMounted, ref, watch} from 'vue'
import {useQuasar} from 'quasar'

const $q = useQuasar()

const props = defineProps({
  modelValue: {
    type: String,
    require: true,
  },
  placeholder: {
    type: String,
    default: 'Enter your comments',
  },
  maxlength: {
    type: Number,
    default: 0,
  },
  counter: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

function onUpdate(e) {
  emit('update:modelValue', e)
}

let recognition = null
const isRecording = ref(false)
function toggleRecording() {
  if (isRecording.value) {
    recognition.stop()
  } else {
    try {
      recognition?.start()
      setTimeout(() => {
        if (!isRecording.value) $q.notify({type: 'negative', message: 'Your browser does not support speech recognition.'})
      }, 100)
    } catch (error) {
      console.error(error)
      $q.notify({type: 'negative', message: 'Your browser does not support speech recognition.'})
    }
  }
}
watch(
  () => props.modelValue,
  () => {
    if (!isRecording.value) _t = props.modelValue
  }
)

let _t = ''
function initRecognition() {
  recognition = new webkitSpeechRecognition() // For Chrome
  if (!recognition) {
    recognition = new SpeechRecognition() // For other browsers
  }
  recognition.continuous = true
  recognition.interimResults = true

  recognition.onstart = () => {
    console.log('Started')
    isRecording.value = true
  }

  recognition.onend = () => {
    console.log('Stopped')
    isRecording.value = false
    _t = props.modelValue
  }

  recognition.onresult = (evt) => {
    const t = Array.from(evt.results)
      .map((result) => result[0])
      .map((result) => result.transcript)
      .join('')

    for (let i = 0; i < evt.results.length; i++) {
      const result = evt.results[i]

      if (result.isFinal) {
        emit('update:modelValue', _t + t)
      }
    }
  }
}

onMounted(() => {
  initRecognition()
})
</script>

<style scoped>
.border-error {
  border: 2px solid red !important;
}
</style>
