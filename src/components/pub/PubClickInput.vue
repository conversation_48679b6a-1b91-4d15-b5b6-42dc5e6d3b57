<template>
  <div class="flex items-center">
    <div v-if="!isEditing" @click="isEditing = true" class="cursor-pointer flex justify-between full-width" style="margin: 9px 0 10px">
      <div>{{ typeof modelValue === 'number' ? modelValue : modelValue || `Please input ${label}` }}</div>
      <q-icon v-if="isIconShow" name="edit" color="teal" size="sm" class="q-ml-sx coursor-pointer" />
    </div>
    <q-input
      v-if="isEditing"
      :modelValue="modelValue"
      @update:modelValue="onInput"
      @blur="onInputUpdate"
      @keyup.enter="onInputUpdate"
      dense
      autofocus
      :style="`width: ${maxWidth}`">
    </q-input>
  </div>
</template>

<script setup>
import {ref} from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
    require: true,
  },
  label: {
    type: String,
    default: 'field',
  },
  maxWidth: {
    type: String,
    default: '8rem',
  },
  isIconShow: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'onUpdate'])

const isEditing = ref(false)
const onInput = (value) => emit('update:modelValue', value)
const onInputUpdate = () => {
  isEditing.value = false
  emit('onUpdate')
}
</script>
