<template>
  <q-dialog :modelValue="modelValue" @update:modelValue="emit('update:modelValue')" persistent :maximized="!$q.screen.gt.xs">
    <div class="bg-white column full-height" :class="{'mobile-dialog': $q.screen.gt.xs}">
      <q-toolbar class="shadow-2">
        <q-btn flat v-close-popup no-caps dense rounded icon="close" />
        <q-toolbar-title>{{ title }}</q-toolbar-title>
      </q-toolbar>

      <div class="min-height">
        <slot></slot>
      </div>

      <q-toolbar class="shadow-2">
        <q-toolbar-title></q-toolbar-title>
        <q-btn color="teal" :disable="disable" no-caps :label="doneLabel" @click="emit('update:modelValue', false), doneFn()" />
      </q-toolbar>
    </div>
  </q-dialog>
</template>

<script setup>
import {ref} from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  doneFn: {
    type: Function,
    default: () => ({}),
  },
  doneLabel: {
    type: String,
    default: 'Done',
  },
  title: {
    type: String,
    default: 'Dialog',
  },
  disable: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])
</script>

<style lang="scss">
.mobile-dialog {
  max-width: 60vw;
  min-width: 60vw;
  width: 60vw;
  max-height: 80vh;
  min-height: 80vh;
  height: 80vh;
}
.min-height {
  min-height: calc(100% - 100px);
  max-height: calc(100% - 100px);
  overflow: auto;
}
</style>
