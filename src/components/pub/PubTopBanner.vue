<template>
  <q-header unelevated class="bg-white text-black">
    <q-list class="bg-teal-1 row" :class="{'shadow-1 top-banner-border-bottom': !noElevated}" :style="{height: height}">
      <q-item clickable v-ripple class="full-width q-pa-none items-center q-pr-sm">
        <q-item-section side>
          <div class="flex items-center">
            <slot name="left" />
            <q-btn flat round icon="arrow_back" :loading="isLoading" @click.stop="goBack"></q-btn>
            <q-btn round flat>
              <q-img src="~assets/img/logo.png" width="1.5rem" fix="contain" @click.stop="goHome" />
            </q-btn>
          </div>
        </q-item-section>
        <q-item-section>
          <div class="flex items-center">
            <span class="text-h6 text-grey-9">{{ title }}</span>
            <slot name="beside-title" />
          </div>
        </q-item-section>

        <!-- buttons slot -->
        <q-item-section side>
          <slot />
        </q-item-section>

        <q-item-section v-if="showShare" side>
          <ShareDetailBtn v-if="isShareDetail" />
          <ShareBtn :shareUrl="shareUrl" v-else />
        </q-item-section>

        <q-item-section v-if="isShowMenu" side>
          <AccountMenu />
        </q-item-section>
      </q-item>
    </q-list>
  </q-header>
</template>

<script setup>
import {useRouter, useRoute} from 'vue-router'
import AccountMenu from 'components/AccountMenu.vue'
import ShareBtn from './Share/ShareBtn.vue'
import ShareDetailBtn from 'components/ShareBtn.vue'
import useSchool from 'src/composables/common/useSchool'

const $router = useRouter()
const $route = useRoute()
const {isSys} = useSchool()

const props = defineProps({
  title: {
    type: String,
    default: 'Go back',
  },
  isShowMenu: {
    type: Boolean,
    default: false,
  },
  height: {
    type: String,
    default: '3rem',
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  checkGoBackFn: {
    type: Function,
    default: () => true,
  },
  useBack: {
    type: Boolean,
    default: false,
  },
  noElevated: {
    type: Boolean,
    default: false,
  },
  back: {
    type: String,
    default: '',
  },
  showShare: {
    type: Boolean,
    default: false,
  },
  isShareDetail: {
    type: Boolean,
    default: false,
  },
  shareUrl: {
    type: String,
    default: '',
  },
})

function goBack() {
  const query = $route.query
  const back = props?.back || query?.back || ''
  if (!document?.referrer && !back && props.useBack) {
    if (isSys.value) $router.replace('/sys')
    else $router.replace('/')
    return
  }
  const canGoBack = props.checkGoBackFn()
  if (!canGoBack) return
  if (props.useBack && back) {
    try {
      if (back === $route.path) {
        $router.go(-1)
        return
      } else if (back) {
        delete query?.back
        $router.replace({path: back, query})
        return
      } else {
        $router.go(-2)
        return
      }
    } catch (error) {
      console.error(error)
      $router.go(-1)
      return
    }
  } else {
    $router.go(-1)
    return
  }
}
function goHome() {
  $router.push('/')
}
</script>
<style lang="sass" scope>
.top-banner-border-bottom
  border-bottom: 1px solid #dedede
</style>
