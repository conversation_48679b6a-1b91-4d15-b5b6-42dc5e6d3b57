<template>
  {{ new Intl.DateTimeFormat(options.locale, {timeZone: timeZone || options.timeZone}).format(new Date(dateISOString)) }}
</template>

<script setup>
import {ref} from 'vue'
const options = ref(Intl.DateTimeFormat().resolvedOptions())

const props = defineProps({
  dateISOString: {
    type: String,
    default: new Date().toISOString(),
  },
  timeZone: {
    type: String,
    default: null,
  },
})
</script>
