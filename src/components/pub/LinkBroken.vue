<template>
  <div class="full-width">
    <div class="flex flex-center full-width">
      <q-img src="~assets/img/linkbroken.png" fit="contain" style="max-width: 800px; height: 500px; max-height: 50vh"> </q-img>
      <div class="text-center text-grey">{{ message }}</div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  message: {
    type: String,
    default: '',
  },
})
</script>
