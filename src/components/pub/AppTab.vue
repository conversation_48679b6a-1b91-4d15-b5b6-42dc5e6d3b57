<template>
  <q-tabs v-model="currentTab" class="text-grey-9" dense active-color="teal" align="left" arrow-indicator no-caps>
    <q-tab v-for="item in tabOptions" :key="item.name" :name="item.name" :label="item.label" />
  </q-tabs>
</template>

<script setup>
import {ref, computed} from 'vue'

const props = defineProps({
  tab: {
    type: String,
    required: true,
  },
  tabOptions: {
    type: Array,
    required: true,
  },
})

const emit = defineEmits(['change'])

const currentTab = computed({
  get: () => props.tab,
  set: (newValue) => emit('change', newValue),
})
</script>
