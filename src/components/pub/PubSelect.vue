<template>
  <q-select v-if="isMultiple" :options="options" @filter="filterFn" multiple use-chips emit-value map-options use-input clearable :label="label || type" />
  <q-select v-else :options="options" @filter="filterFn" hide-selected fill-input emit-value map-options use-input clearable :label="label || type" />
</template>
<script setup>
import {ref, onMounted, watch, computed} from 'vue'
import {pubStore} from 'stores/pub'
import {curriculumStore} from 'stores/curriculum'
const curriculum = curriculumStore()

const props = defineProps({
  label: String,
  school: String, // schoolId
  create: Boolean,
  country: String, // for school
  city: String, // for school
  grade: String, // for class gradeId
  classes: String, // for class classId
  mode: String, // 'All', 'Stander', 'Subject'
  type: String, // 'School', 'Grade', 'Class'
  multiple: String,
})
const isMultiple = computed(() => {
  return props.multiple !== undefined
})
const emit = defineEmits(['init'])

const pub = pubStore()
const list = ref([])
const options = ref([])

if (props.type === 'Class') {
  if (props.school) watch(() => props.school, find)
  else watch(() => pub.user.school, find)
  watch(() => props.grade, find)
}

if (props.type === 'Grade') {
  if (props.school) watch(() => props.school, find)
  else watch(() => pub.user.school, find)
}
if (props.type === 'School') {
  if (props.country) watch(() => props.country, find)
  // watch(() => props.city, find)
}

onMounted(async () => {
  console.log(1111, props.type)
  list.value = options.value = await find()
})

async function find(query) {
  const type = props.type
  let rs
  if (type === 'Country') {
    rs = await App.service('conf').get('CountryCodes')
    emit('init', rs)
    return rs.map((v) => ({label: v.en, value: v.code}))
  } else if (type === 'City') {
    if (!query) return []
    const rs = await fetch(`/fio/maps/city/${props.country}?q=${query || ''}`).then((r) => r.json())
    if (!Array.isArray(rs) || Acan.isEmpty(rs)) return []
    return rs.map((v) => {
      v.terms.pop()
      return v.terms.join(' ')
    })
  } else if (type === 'School') {
    if (!query) query = {}
    if (props?.country) query.country = props.country
    if (props?.city) query.city = props.city
    query.personal = false
    rs = await App.service('school-plan').get('search', {query})
    return rs.map((v) => ({label: v.name, value: v._id}))
  } else if (type === 'Grade') {
    return await curriculum.gradeOptions(props.school || pub.user._id)
    console.log(list.value, 111111111)
  } else if (type === 'Class') {
    return await findClass()
  }
}

async function findClass() {
  let rs = await pub.getClassListData([props.school || pub.user.school])
  const classes = {}
  if (props.grade) rs = rs.filter((v) => v.grade === props.grade)
  const gradeList = (await curriculum.gradeOptions(props.school || pub.user._id)) || []
  const existingGradeId = gradeList.filter((e) => !e?.del).map((e) => e?._id || e?.value)
  list.value = rs
    .filter((e) => !e?.del)
    .filter((e) => existingGradeId.includes(e.grade))
    .map((v) => {
      classes[v._id] = v
      return {label: v.name, value: v._id}
    })
  emit('init', classes)
  return list.value
}

async function filterFn(val, update) {
  val = val.trim()
  if (!val) {
    options.value = list.value
    update()
    return
  }
  if (props.type === 'School') {
    options.value = await find({name: {$regex: val.toLowerCase(), $options: 'i'}})
    if (props.create && Acan.isEmpty(options.value)) options.value.push({label: val.toFirstUpperCase(), value: val.toFirstUpperCase()})
  } else if (props.type === 'City') {
    options.value = await find(val.toLowerCase())
  } else if (props.type === 'Country') {
    const needle = val.toLowerCase()
    options.value = list.value.filter((v) => v.label.toLowerCase().indexOf(needle) === 0)
  } else {
    const needle = val.toLowerCase()
    options.value = list.value.filter((v) => v.label.toLowerCase().indexOf(needle) > -1)
  }
  update()
}
</script>
