<template>
  <q-toolbar class="q-pr-sm flex" :class="$q.screen.gt.xs ? '' : 'column'" style="border-bottom: 1px solid #dedede">
    <q-btn v-if="hasBackButton" flat round dense icon="arrow_back" color="grey-8" class="q-mr-sm" @click.stop="$router.go(-1)" />
    <q-breadcrumbs :class="$q.screen.gt.xs ? '' : 'full-width q-py-md'">
      <q-breadcrumbs-el v-for="(o, i) in plist" :key="i" :icon="o.icon || list[o].icon" :label="o.label || list[o]?.label" :to="o.to || list[o]?.to" replace />
    </q-breadcrumbs>
    <q-space />
    <div class="row justify-end" :class="$q.screen.gt.xs ? '' : 'full-width'">
      <slot />
      <CartPage v-if="isAccountInfoPage" />
      <NoticePage v-if="isAccountInfoPage" />
      <AccountMenu v-if="isAccountInfoPage" class="q-ml-sm" />
    </div>
  </q-toolbar>
</template>
<script setup>
import {computed} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'

import AccountMenu from 'components/AccountMenu.vue'
import CartPage from 'components/CartPage.vue'
import NoticePage from 'components/NoticePage.vue'

const pub = pubStore()
const $route = useRoute()
const $router = useRouter()
const props = defineProps({
  plist: {
    type: Array,
    default: () => [],
  },
  hasBackButton: {
    type: Boolean,
    default: true,
  },
})

const isAccountInfoPage = computed(() => /account\/info/.test($route.path))

const list = computed(() => {
  return {
    home: {label: 'Home', icon: 'home', to: '/home/<USER>'},
    account: {label: pub.user.school ? 'School Account' : 'Account info', icon: 'manage_accounts', to: '/account/info'},
    teacherManage: {label: 'Teacher manage', icon: 'psychology', to: '/account/teacher'},
    // teacherAdd: {label: 'Teacher Add', icon: 'add', to: '/account/teacher/edit'},
    classesManage: {label: 'Classes', icon: 'widgets', to: '/account/classes'},
    studentManage: {label: 'Student manage', icon: 'school', to: '/account/student/all'},
    roleManage: {label: 'Role Manage', icon: 'vpn_key', to: '/account/roleManage'},

    // TODO: implement following manage pages
    spaceManage: {label: 'Space Manage', icon: 'workspaces', to: '/account/spaceManage'},
    // TODO: implement following setting pages
    academicTermSetting: {label: 'Academic Term', icon: 'history_edu', to: '/setting/academicTermSetting'},
    curriculumSetting: {label: 'Curriculum', icon: 'menu_book', to: '/setting/academicTermSetting'},
    planingFormatSetting: {label: 'Planing Format', icon: 'text_snippet', to: '/setting/planningFormatSetting'},
    tagsSetting: {label: 'Tags Setting', icon: 'sell', to: '/setting/tagsSetting'},
    attendanceSetting: {label: 'Attendance', icon: 'fact_check', to: '/setting/attendanceSetting'},
    teacherSetting: {label: 'Teacher & Service verification', icon: 'workspace_premium', to: '/setting/teacherSetting'},
  }
})
</script>
