<template>
  <q-avatar :size="size" class="relative-position">
    <img draggable="false" :src="src || '/v2/img/avatar.png'" />
    <div class="text-wrapper">
      <slot name="text"></slot>
    </div>
    <q-tooltip v-if="title">{{ title }}</q-tooltip>
    <slot></slot>
  </q-avatar>
</template>

<script setup>
const props = defineProps({
  src: {type: String, default: null},
  title: {type: String, default: null},
  size: {type: String, default: '2rem'},
})
</script>

<style lang="scss" scoped>
.text-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
