<template>
  <div class="q-pa-md text-grey column flex-center">
    <img class="q-pa-lg" :style="`width: ${size}`" src="~assets/icons/nodata.svg" alt="" />
    <div v-if="title" :class="`text-${titleColor} ${titleClass}`">{{ title }}</div>
    <div :class="`text-${messageColor} ${messageClass}`">{{ message }}</div>
    <slot />
  </div>
</template>

<script setup>
const props = defineProps({
  message: {
    type: String,
    default: 'No data',
  },
  messageColor: {
    type: String,
    default: 'red-4', // REF: https://quasar.dev/style/color-palette#color-list
  },
  messageClass: {
    type: String,
    // E.g. text-caption
    default: 'text-subtitle1',
  },
  title: {
    type: String,
    default: '',
  },
  titleColor: {
    type: String,
    default: 'grey-8', // REF: https://quasar.dev/style/color-palette#color-list
  },
  titleClass: {
    type: String,
    // E.g. text-subtitle text-weight-medium
    default: 'text-h6',
  },
  size: {
    type: String,
    default: '15rem',
  },
})
</script>
