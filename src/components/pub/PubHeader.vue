<template>
  <q-header elevated class="bg-white text-black">
    <q-toolbar>
      <slot name="left" />
      <q-toolbar-title class="row items-center">
        <q-avatar class="cursor-pointer" @click="onClick">
          <img src="~assets/img/logo2.png" />
        </q-avatar>
        <div class="q-pl-sm text-primary text-weight-medium cursor-pointer" @click="onClick">Classcipe</div>
      </q-toolbar-title>
      <ClaimPage v-if="showClaim" />
      <CartPage v-if="showCart" />
      <NoticePage v-if="showNotice" />
      <AccountMenu v-if="showAccount" />
      <template v-if="showShare">
        <ShareDetailBtn v-if="isShareDetail" />
        <ShareBtn v-else />
      </template>
    </q-toolbar>
  </q-header>
</template>

<script setup>
import {ref} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import AccountMenu from 'components/AccountMenu.vue'
import NoticePage from 'components/NoticePage.vue'
import {pubStore} from 'stores/pub'
import ShareBtn from './Share/ShareBtn.vue'
import ShareDetailBtn from 'components/ShareBtn.vue'
import CartPage from 'components/CartPage.vue'
import ClaimPage from 'components/ClaimPage.vue'

const pub = pubStore()
const $router = useRouter()
const $route = useRoute()
const isStudent = ref(pub.user?.roles?.includes('student'))

const props = defineProps({
  showAccount: {
    type: Boolean,
    default: false,
  },
  showClaim: {
    type: Boolean,
    default: false,
  },
  showNotice: {
    type: Boolean,
    default: false,
  },
  href: {
    type: String,
    default: '/',
  },
  showShare: {
    type: Boolean,
    default: false,
  },
  isShareDetail: {
    type: Boolean,
    default: false,
  },
  shareLink: {
    type: String,
    default: null,
  },
  showCart: {
    type: Boolean,
    default: false,
  },
})

const onClick = () => {
  let homepage
  if (props.href) {
    homepage = props.href
  } else {
    homepage = '/home/<USER>'
    if (isStudent.value) {
      homepage = '/study/index'
    }
  }

  $router.replace(homepage)
}
</script>
<style lang="sass" scope></style>
