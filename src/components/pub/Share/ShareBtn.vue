<template>
  <q-btn flat round dense size="0.75rem" icon="o_share" no-caps>
    <q-menu>
      <div class="q-pa-md">
        <div class="text-h6">Share options</div>
        <div class="">Please invite other teachers to access service verification.</div>
      </div>
      <div class="row q-mb-md full-width no-wrap overflow-auto">
        <div v-for="(o, i) in shareList" :key="i">
          <q-btn flat class="" no-caps @click="o.click" style="width: 40px; padding: 0">
            <q-icon size="2rem" :name="o.icon"></q-icon>
          </q-btn>
        </div>
      </div>
    </q-menu>
  </q-btn>
</template>
<script setup>
import {ref, computed, onMounted, inject} from 'vue'
import {copyToClipboard} from 'quasar'
import {pubStore} from 'stores/pub'
import AddShareDialog from 'src/pages/poster/AddShareDialog.vue'
import {downloadImg} from 'src/pages/poster/consts.js'
import {PATH_PREFIX} from 'src/boot/const.js'

const shortLink = ref('')
const posterUrl = ref('')
const pub = pubStore()
const props = defineProps({
  shareUrl: {
    type: String,
    default: '',
  },
})

const shareList = [
  {
    label: 'WhatsApp',
    icon: 'img:/v2/icons/whatsapp.svg',
    click: () => {
      toShare('WhatsApp')
    },
  },
  {
    label: 'Facebook',
    icon: 'img:/v2/icons/facebook.svg',
    click: () => {
      toShare('Facebook')
    },
  },
  {
    label: 'Twitter',
    icon: 'img:/v2/icons/twitter.svg',
    click: () => {
      toShare('Twitter')
    },
  },
  {
    label: 'Pinterest',
    icon: 'img:/v2/icons/pinterest.svg',
    click: () => {
      toShare('Pinterest')
    },
  },
  {
    label: 'Moments',
    icon: 'img:/v2/icons/poster/Moments.png',
    click: () => {
      toShare('Moments')
    },
  },
  {
    label: 'Instagram',
    icon: 'img:/v2/icons/poster/Instagram.png',
    click: () => {
      toShare('Instagram')
    },
  },
  {
    label: 'Copy link',
    icon: 'img:/v2/icons/poster/Link.png',
    click: () => {
      copyFn()
    },
  },
  {
    label: 'Save',
    icon: 'img:/v2/icons/poster/Save.png',
    click: () => {
      downFn()
    },
  },
]

const downFn = () => {
  $q.loading.show({
    message: 'Generating poster...',
  })
  App.service('poster').timeout = 100000
  App.service('poster')
    .get('mergeImage', {
      query: {
        image: Fn.hashToUrl(posterUrl.value),
        url: shortLink.value,
      },
    })
    .then((res) => {
      downloadImg(res, `${Date.now()}.png`)
    })
    .finally(() => {
      $q.loading.hide()
    })
}

const copyFn = () => {
  copyToClipboard(shortLink.value).then(() => {
    $q.notify({type: 'positive', message: 'Link copied, please paste and send to your friends.'})
  })
}

const getShortLink = async () => {
  const shareableUrl = props.shareUrl ? `${hostUrl + props.shareUrl}` : `${hostUrl}/v2/setting/postShare/verification/999?inviteCode=${pub?.user?.inviteCode}`
  const url = shareableUrl
  const res = await App.service('short-link').get('shortLink', {query: {url}})
  shortLink.value = res?.shortUrl
}

const main = async () => {
  getShortLink()
  getPoster()
}

const getPoster = async () => {
  const query = {
    $limit: 1000,
    $skip: 0,
    $sort: {updatedAt: -1},
    type: 'image',
    style: 'verified_teacher',
  }

  const res = await App.service('poster').find({query})
  posterUrl.value = res?.data?.[0]?.image
}

const toShare = (type) => {
  $q.loading.show({
    message: 'Generating poster...',
  })
  App.service('poster').timeout = 100000
  App.service('poster')
    .get('mergeImage', {
      query: {
        image: Fn.hashToUrl(posterUrl.value),
        url: shortLink.value,
      },
    })
    .then((res) => {
      $q.dialog({
        component: AddShareDialog,
        componentProps: {
          type,
          shareType: 'verified_teacher',
          shortLink: shortLink.value,
          posterUrl: res,
          isShowImage: true,
        },
      })
    })
    .finally(() => {
      $q.loading.hide()
    })
}

onMounted(main)
</script>
