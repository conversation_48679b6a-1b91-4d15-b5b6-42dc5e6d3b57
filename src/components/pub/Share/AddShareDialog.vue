<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide">
    <q-card class="q-pa-md" style="width: 360px">
      <q-card-section class="q-pt-none">
        <div class="relative-position" style="width: 280px; height: 500px; margin: 0 auto">
          <q-img class="full-width full-height" fit="fill" :src="hashToUrl(backgroundImg)"></q-img>
        </div>
        <div class="text-subtitle1 text-bold q-mt-md">Encourage your friend to like it!</div>
        <div v-if="!isEmpty(listData)" class="q-pa-md q-mt-md rounded-borders-sm" style="border: 2px solid #26a69a">
          {{ listData?.data?.[0]?.content }}
        </div>
      </q-card-section>
      <q-card-actions class="row q-col-gutter-md q-mt-md">
        <div class="col-6">
          <q-btn class="full-width" color="primary" label="Change to another" no-caps rounded @click="changeText" />
        </div>
        <div class="col-6">
          <q-btn class="full-width" color="primary" label="Copy and share" no-caps rounded @click="toShare" />
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {onMounted, ref, inject} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {sharePlatform} from 'src/pages/poster/consts.js'
import {pubStore} from 'stores/pub'
import {copyToClipboard, useDialogPluginComponent} from 'quasar'
import {PATH_PREFIX} from 'src/boot/const'

const pub = pubStore()
const route = useRoute()
const router = useRouter()

const listData = ref({})
const backgroundImg = ref(null)

const props = defineProps({
  type: String,
  imageCover: String,
  image: String,
  uploadImgUrlWidth: String,
  customize: Boolean,
  shareType: String,
})

defineEmits([...useDialogPluginComponent.emits])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

const main = async () => {
  await getImg()
  find()
}

const getImg = async () => {
  const query = {
    $select: ['image'],
    type: 'image',
    style: 'verified_teacher',
  }

  const res = await App.service('poster').find({query})
  if (res?.total > 0) {
    backgroundImg.value = res?.data?.[0]?.image
  }
}

const find = async ($skip) => {
  const query = {
    $limit: 1,
    $skip,
    $sort: {updatedAt: -1},
    type: 'text',
    $select: ['content'],
    style: 'verified_teacher',
  }

  $q.loading.show()
  listData.value = await App.service('poster').find({query})
  $q.loading.hide()
}

const changeText = async () => {
  const {limit = 1, skip = 0, total = 0} = listData.value
  const $skip = skip + limit >= total ? 0 : skip + limit
  await find($skip)
}

const toShare = async () => {
  copyToClipboard(listData.value?.data?.[0]?.content).then(() => {
    $q.notify({type: 'positive', message: 'Copy successfully, lets share with friends now!'})

    const typeList = ['Facebook', 'WhatsApp', 'Twitter', 'Pinterest']

    const tempWin = window.open('_blank')

    if (typeList.includes(props.type)) {
      App.service('share-info')
        .create({
          title: 'classcipe',
          desc: listData.value?.data?.[0]?.content,
          keywords: 'classcipe',
          image: Fn.hashToUrl(backgroundImg.value),
          // url: '',
        })
        .then((res) => {
          const url = encodeURIComponent(`${hostUrl}/v2/setting/postShare/verification/${res._id}`)
          tempWin.location = sharePlatform({
            type: props.type,
            url,
            title: 'classcipe',
          })
        })
    } else {
      $q.dialog({
        message: `The poster has been download and ready to be shared with friends in your ${props.type}!`,
      }).onOk(() => {
        onDialogHide()
      })
    }
    // onDialogOK()
  })
}

onMounted(main)
</script>
