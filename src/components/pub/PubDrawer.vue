<template>
  <q-drawer side="left" show-if-above :modelValue="modelValue" @update:modelValue="(bool) => emit('update:modelValue', bool)" bordered>
    <q-scroll-area class="fit">
      <q-list>
        <q-item
          v-for="item in list"
          :key="item?.value"
          :class="[item?.value === currentValue ? 'item-active text-white' : '']"
          clickable
          @click.stop="() => emit('click', item)">
          <q-item-section>
            <div class="q-pl-md rounded-md q-pa-sm" :class="[item?.value === currentValue ? 'bg-teal-1 text-bold text-grey-9 ' : 'bg-white text-grey-10']">
              <q-icon v-if="item?.icon" :name="item?.icon" size="sm" color="grey-8" />
              {{ item?.label || '-' }}
            </div>
          </q-item-section>
        </q-item>
      </q-list>
    </q-scroll-area>
  </q-drawer>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: Boolean,
    require: true,
  },
  list: {
    type: Array,
    default: () => [],
    require: true,
  },
  currentValue: {
    type: String,
    require: true,
  },
})

const emit = defineEmits(['update:modelValue', 'click'])
</script>
