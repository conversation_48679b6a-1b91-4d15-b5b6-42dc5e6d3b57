<template>
  {{ console.log('TaskList.vue', taskList) }}
  <div style="height: 80vh; overflow: auto">
    <div v-if="loading">
      <q-spinner-ball color="primary" size="2em" class="full-width" />
    </div>
    <div v-else-if="taskList.length" class="q-pa-md">
      <div class="q-gutter-md q-pl-md">
        <div v-for="(task, index) in taskList" :key="index" class="full-width card-style" style="display: flex; flex-direction: row" @click="handleClick(task)">
          <q-radio v-if="isSelect" v-model="servicePackage.associatedTask._id" :val="task._id" class="q-ml-md" color="primary" />
          <q-card class="q-pa-md full-width" :elevation="2" style="box-shadow: 0px 4px 4px 0px #00000040">
            <div class="q-pa-md row no-wrap items-center">
              <q-img :src="hashToUrl(task.cover)" style="width: 222px; height: 180px; border-radius: 8px" class="q-mr-md" />

              <div class="col">
                <div class="row items-center justify-between">
                  <div v-if="task.name" class="text-h5 text-bold">{{ task.name }}</div>
                  <div v-else class="text-h5">Untitled</div>
                </div>
                <div class="q-mt-xs">
                  <span class="text-amber-10" style="font-size: 14px">Associated task</span>
                  <span class="text-teal-5 q-ml-sm" style="font-size: 16px; font-weight: 500">{{
                    servicePackage.mentoringTypeList.find((e) => e.value === task.mentoringType)?.label
                  }}</span>
                </div>

                <div class="text-teal-3 q-mt-sm ellipsis-2-lines" style="font-size: 14px; font-weight: 500">
                  {{ task.description }}
                </div>

                <div class="row items-end">
                  <div style="font-size: 14px; font-weight: 500">No. of Sections</div>
                  <div class="q-ml-lg" style="color: #787579; font-size: 16px">{{ task.sections?.length ?? (task.numberSections || 0) }}</div>
                </div>
                {{ console.log('fkkfkf', task) }}
                <div v-if="(currentTab === 'ongoing' || currentTab === 'completed') && task.servicer" class="row items-center q-mt-sm">
                  <q-avatar size="28px">
                    <img :src="task.servicer.avatar" />
                  </q-avatar>
                  <div class="text-subtitle2 text-grey-6 q-ml-sm">
                    {{ toTitleCase(task.servicer.name) }}
                  </div>
                </div>
                <div class="row justify-end q-mt-sm">
                  <div class="text-teal-4" style="font-size: 22px">
                    {{ console.log('sfkkfk', task?.deficient) }}
                    <div v-if="isStudent && currentTab === 'unassigned' && task?.deficient > 0">
                      <span class="text-bold text-red-4">Deficit: USD {{ task.deficient }} </span>
                    </div>
                    <div v-else-if="isStudent">
                      <span class="text-bold">USD {{ task.price ? (task.price / 100).toFixed(2) : 0 }}</span>
                    </div>
                    <div v-else>
                      <span class="text-bold"
                        >USD {{ task.costPrice ?? task.sections?.reduce((sum, section) => sum + section.costPrice, 0).toFixed(2) ?? 0 }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="text-body2 text-grey-9 q-mt-sm">Last updated on {{ formatDate(task.updatedAt) }}</div>
          </q-card>
        </div>
      </div>
    </div>
    <NoData v-else />
  </div>
</template>

<script setup>
import {computed, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {servicePackageStore} from 'stores/service-package'

const route = useRoute()
const router = useRouter()
const servicePackage = servicePackageStore()

const props = defineProps({
  listData: {
    type: Array,
    default: () => [],
  },
  isStudent: {
    type: Boolean,
    default: false,
  },
  isSelect: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  booking: {
    type: String,
    default: null,
  },
})

const currentTab = computed(() => route.query.subtab || 'ongoing')

const taskList = computed(() => {
  console.log('fjjfjf', props.listData)
  if (route.query.tab === 'taskManagement') {
    return props.listData.map((item) => {
      return {...item.servicePackUser.snapshot, id: item.servicePackUser._id, booker: item.bookerInfo, updatedAt: item.servicePackUser.updatedAt}
    })
  }
  return props.listData.map((item) => {
    if (item.associatedServicer?.uid) {
      console.log('fkfkkg', item.sectionCreditDeficit?.points)
      const deficient = item.sectionCreditDeficit?.points || 0
      return {...item.snapshot, id: item._id, servicer: item.associatedServicer, updatedAt: item.updatedAt, deficient}
    }
    return {...item.snapshot, id: item._id, updatedAt: item.updatedAt}
  })
})

function toTitleCase(input) {
  if (!input) return ''
  const str = Array.isArray(input) ? input.join(' ') : input
  return str
    .toLowerCase()
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

function handleClick(task) {
  if (props.isStudent) {
    router.push({
      path: `purchased/view/${task.id}`,
      query: {...route.query, back: route.fullPath},
    })
  } else {
    router.push({
      path: `services/view/${task.id}`,
      query: {...route.query, back: route.fullPath},
    })
  }
}

function formatDate(isoString) {
  const date = new Date(isoString)

  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear()

  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`
}

onMounted(async () => {
  // await servicePackage.findServiceTypes()
})
</script>

<style scoped>
.q-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-style {
  cursor: pointer;
}
</style>
