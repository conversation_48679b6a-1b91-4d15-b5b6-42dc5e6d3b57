<template>
  <div v-if="isLoading" class="flex flex-center q-py-xl">
    <q-spinner color="primary" size="3em" />
  </div>
  <div v-else-if="Uploads.length" class="q-py-md q-mt-lg column q-mb-md" style="border: 1px solid teal; border-radius: 10px">
    <div v-if="Uploads.length > 0">
      <q-card flat class="q-ma-sm">
        <div class="relative-position">
          <div ref="scrollContainer" style="max-width: 670px; height: 65px; overflow-x: auto; overflow-y: hidden">
            <div class="row no-wrap q-gutter-sm">
              <div
                v-for="file in Uploads"
                :key="file._id"
                class="relative-position"
                style="width: 95px; min-width: 95px; border-radius: 10px; padding-top: 14px"
                @mouseenter="hoveredId = file._id"
                @mouseleave="hoveredId = null">
                <q-avatar v-if="file.mime.includes('image')" rounded text-color="primary" style="height: 45px; width: 60px">
                  <img
                    :src="hashToUrl(file.cover)"
                    alt="file.cover"
                    @click="currentUpload(file._id)"
                    style="height: 100%; width: 100%; object-fit: cover; border-radius: 8px" />
                </q-avatar>

                <q-avatar v-else-if="file.mime.includes('video')" rounded text-color="primary">
                  <VideoPlayer :url="hashToUrl(file.key)" :isPreviewBig="false" :controls="true" />
                  <!-- <video class="full-width vertical-middle" muted preload="metadata" @click="currentUpload(file._id)">
                    <source :src="hashToUrl(file.key)" type="video/mp4" />
                  </video> -->
                </q-avatar>

                <q-avatar v-else-if="file.mime.includes('audio')" rounded text-color="primary">
                  <audio :src="hashToUrl(file.cover)" preload="meta" :controls="controls" playsinline style="width: 100%" @click="currentUpload(file._id)">
                    <source :src="hashToUrl(file.cover)" type="audio/mp3" />
                  </audio>
                </q-avatar>

                <q-avatar v-else-if="file.mime.includes('pdf')" rounded text-color="primary" icon="o_picture_as_pdf" @click="currentUpload(file._id)" />

                <q-btn
                  v-if="hoveredId === file._id && canDelete(file)"
                  round
                  dense
                  size="10px"
                  icon="close"
                  color="red"
                  class="absolute"
                  style="top: 1px; right: 30px; z-index: 100"
                  @click.stop="handleDeleteUploadObject(file._id)" />

                <q-btn
                  style="width: 25px; top: 15px; padding-right: 0px; padding-left: 0px; padding-bottom: 0px; padding-top: 0px; margin: 0px"
                  flat
                  icon="download"
                  size="sm"
                  color="primary"
                  @click="downloadImage(hashToUrl(file.cover))" />
              </div>
            </div>
          </div>
          {{ console.log('isHorizontalOverflowing', isHorizontalOverflowing) }}
          <div v-if="isHorizontalOverflowing" @click="scrollRight" class="text-teal text-caption q-mt-sm cursor-pointer">More</div>

          <div v-if="getUploaderDetails.name" class="row items-center q-mt-lg">
            <q-avatar size="24px" class="q-mr-xs">
              <img :src="hashToUrl(getUploaderDetails.avatar)" />
            </q-avatar>
            <div class="text-caption text-weight-medium q-ml-xs">
              {{ Array.isArray(getUploaderDetails.name) ? getUploaderDetails.name.join(' ') : getUploaderDetails.name }}
            </div>
            <div class="text-caption text-grey-6 q-ml-lg">uploaded on {{ formatDate(getUploaderDetails.uploadedTime) }}</div>
          </div>
        </div>
      </q-card>

      <div v-if="comments.length > 0" class="q-mx-sm q-mt-lg">
        <div ref="commentContainer" class="scroll-area">
          <div
            v-for="commentItem in comments"
            :key="commentItem._id"
            class="q-pa-sm q-mb-sm"
            :style="{background: commentItem.uid === servicerInfo._id ? '#e0f2f1' : '#e3f2fd', borderRadius: '10px'}">
            <div class="row">
              <q-avatar size="24px" class="q-mr-xs">
                <img :src="commentItem.uid === servicerInfo._id ? servicerInfo.avatar : bookerInfo.avatar" />
              </q-avatar>
              <div class="text-caption text-weight-medium q-ml-xs q-mt-xs">
                {{ commentItem.uid === servicerInfo._id ? servicerInfo.name.join(' ') : bookerInfo.name.join(' ') }}
              </div>
              <div class="text-caption text-grey-6 q-ml-lg q-mt-xs">
                {{ formatDate(commentItem.createdAt) }}
              </div>
            </div>
            <div class="q-mt-xs q-mx-xs">{{ commentItem.comment }}</div>
          </div>
        </div>
        <div v-if="isOverflowing" @click="scrollToBottom" class="text-teal text-caption q-mt-sm cursor-pointer">More</div>
      </div>
    </div>

    <div v-if="Uploads.length > 0 && canComment" class="q-mt-md q-mx-sm row items-center">
      <RecordInput v-model="comment" :counter="true" :maxlength="1000" outlined placeholder="Write a comment..." class="col" />
      <q-btn label="Send" color="primary" class="q-ml-sm" no-caps style="border-radius: 25px" @click="handleComment" :disabled="!comment.trim()" />
    </div>
  </div>
  <NoData v-else message="No files uploaded" />
</template>

<script setup>
import {ref, computed, nextTick, onMounted, onUnmounted, watch} from 'vue'
import {useRoute} from 'vue-router'
import {downloadImg} from '../../pages/poster/consts'
import RecordInput from 'src/components/utils/inputs/RecordInput.vue'
import NoData from '../pub/NoData.vue'
import VideoPlayer from 'src/components/material/layouts/VideoPlayer.vue'

const props = defineProps({
  currentSection: {type: Object, required: true},
  isReadOnly: {type: Boolean, default: false},
  isReadonlyforProvider: {type: Boolean, default: false},
  bookerInfo: {type: Object, default: () => ({})},
  servicerInfo: {type: Object, default: () => ({})},
})

const route = useRoute()
const isLoading = ref(false)
const Data = ref([])
const selectedUploadId = ref(null)
const comment = ref('')

const commentContainer = ref(null)
const scrollContainer = ref(null)
const isOverflowing = ref(false)
const isHorizontalOverflowing = ref(false)
const hoveredId = ref(null)

const isStudent = computed(() => route.query.tab === 'myAssociatedTask')
const isServiceProvider = computed(() => route.query.tab === 'taskManagement')
const canComment = computed(() => (isStudent.value && !props.isReadOnly) || (isServiceProvider.value && !props.isReadonlyforProvider))
const canDelete = (file) => {
  const item = Data.value.find((item) => item.files?._id === file._id)
  if (!item) return false

  const uploaderId = item.files?.uid

  if (isStudent.value && !props.isReadOnly) {
    return uploaderId === props.bookerInfo?._id
  } else if (isServiceProvider.value && !props.isReadonlyforProvider) {
    return uploaderId === props.servicerInfo?._id
  }
  return false
}

const selectedItem = computed(() => {
  if (!selectedUploadId.value || !Data.value) return null
  return Data.value.find((item) => item.files?._id === selectedUploadId.value)
})

const Uploads = computed(() => {
  return Data.value.flatMap((item) => item.files)
})
const comments = computed(() => selectedItem.value?.comments || [])
const getUploaderDetails = computed(() => selectedItem.value?.files || {})

async function fetchSectionData() {
  if (!props.currentSection?._id || !props.bookerInfo?._id || !props.servicerInfo?._id) {
    Data.value = []
    return
  }

  isLoading.value = true
  try {
    const result = await App.service('section-comments').find({
      query: {
        sectionId: props.currentSection._id,
        'files.uid': {$in: [props.bookerInfo._id, props.servicerInfo._id]},
        $limit: 50,
      },
    })
    Data.value = result.data
    if (result.data.length > 0 && !selectedUploadId.value) {
      selectedUploadId.value = result.data[0]?.files?._id
    }
  } catch (error) {
    console.error('Failed to load section comments:', error)
    Data.value = []
  } finally {
    isLoading.value = false
  }
}

async function handleComment() {
  if (!comment.value.trim() || !selectedItem.value) return
  try {
    await App.service('section-comments').patch(selectedItem.value._id, {comment: comment.value})
    comment.value = ''
  } catch (error) {
    console.error('Failed to post comment:', error)
  }
}

async function handleDeleteUploadObject(fileId) {
  const itemToDelete = Data.value.find((item) => item.files._id === fileId)
  if (!itemToDelete) {
    console.warn('Item to delete not found')
    return
  }
  try {
    await App.service('section-comments').remove(itemToDelete._id)
  } catch (error) {
    console.error('Failed to delete upload object:', error)
  }
}

const formatDate = (dateStr) => new Date(dateStr).toLocaleString()
const downloadImage = (url) => downloadImg(url, `${Date.now()}.png`)
const scrollToBottom = () => commentContainer.value?.scrollBy({top: 100, behavior: 'smooth'})
const scrollRight = () => scrollContainer.value?.scrollBy({left: 100, behavior: 'smooth'})

const checkOverflow = () => {
  nextTick(() => {
    const el = commentContainer.value
    isOverflowing.value = el ? el.scrollHeight > el.clientHeight : false
    const scrollEl = scrollContainer.value
    isHorizontalOverflowing.value = scrollEl ? scrollEl.scrollWidth > scrollEl.clientWidth : false
  })
}

function currentUpload(fileId) {
  console.log('currentUpload', fileId)
  selectedUploadId.value = fileId
}

watch(() => props.currentSection._id, fetchSectionData, {immediate: true})

watch([Data, comments], checkOverflow, {deep: true})

function setupSocketListeners() {
  const service = App.service('section-comments')
  service.on('patched', (patchedItem) => {
    Data.value = Data.value.map((item) => (item._id === patchedItem._id ? patchedItem : item))
  })

  service.on('created', (createdItem) => {
    if (createdItem.sectionId === props.currentSection._id) {
      Data.value.push(createdItem)
    }
  })

  service.on('removed', (removedItem) => {
    Data.value = Data.value.filter((item) => item._id !== removedItem._id)
    if (selectedUploadId.value === removedItem.files?._id) {
      selectedUploadId.value = Data.value.length > 0 ? Data.value[0].files._id : null
    }
  })
}

function cleanupSocketListeners() {
  console.log('socket called')

  const service = App.service('section-comments')
  service.removeAllListeners('patched')
  service.removeAllListeners('created')
  service.removeAllListeners('removed')
}

watch(selectedUploadId, async (newId) => {
  if (!newId) return
  try {
    await App.service('section-comments').find({
      query: {'files._id': newId},
    })
  } catch (e) {
    console.error('Socket channel join failed:', e)
  }
})

onMounted(() => {
  checkOverflow()
  setupSocketListeners()
})

onUnmounted(() => {
  cleanupSocketListeners()
})
</script>

<style scoped>
.scroll-area {
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
  transition: max-height 0.3s ease;
}
</style>
