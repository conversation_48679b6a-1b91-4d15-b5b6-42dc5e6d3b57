<template>
  <div>
    {{ console.log('curSections', curSections) }}
    {{ console.log('complain', complain) }}
    {{ console.log('serviceTask.currentSection', serviceTask.currentSection) }}
    <div class="row">
      <!-- for left part -->
      <div class="col q-ma-md" style="min-width: 0">
        <q-tab-panels v-model="tab" animated swipeable vertical transition-prev="jump-up" transition-next="jump-up" style="z-index: 9; width: 100%">
          <q-tab-panel v-for="(section, index) in curSections" :key="section._id" :name="section._id" style="padding: 0; margin: 0">
            <div
              v-if="sectionDetailsLoading"
              style="display: flex; background-color: #f5f5f5; justify-content: center; align-items: center; height: 100%; overflow: hidden">
              <q-spinner color="primary" size="2em" />
            </div>
            <q-card
              v-else
              class="q-px-md q-py-sm rounded-xl"
              :style="{
                border:
                  '1px solid ' + (section.status === 'pending' || section.status === 'completed' || complain?.status === 'rejected' ? '#4496f5' : '#6b6b6b'),
              }"
              style="min-height: 350px">
              <!-- chat box icon, completed, complain and section slide no.  for students-->
              <div v-if="section && isMyAssociatedTask" class="row items-center q-mt-sm q-pl-na q-gutter-x-na">
                <div v-if="section.status !== 'completed'">
                  <q-btn
                    v-if="canShowConfirmBtn(section, complain)"
                    label="Confirm as completed"
                    no-caps
                    :style="{
                      borderRadius: '25px',
                      backgroundColor: isActive ? '#f4f7f9' : 'rgba(38, 166, 154, 0.05)',
                      color: isActive ? 'var(--q-primary)' : 'grey',
                    }"
                    :disable="!isActive"
                    @click="handleConfirmCompletion(true)" />

                  <!-- complain button -->

                  <q-btn
                    v-if="canShowComplainBtn(section, complain)"
                    label="Complain"
                    class="q-ml-sm"
                    no-caps
                    :style="{
                      borderRadius: '25px',
                      backgroundColor: '#F55C44',
                      color: 'white',
                    }"
                    @click="studentComplain = true" />
                  <!-- view complain button -->
                  <q-btn
                    v-if="canShowViewComplainBtn(section, complain)"
                    label="View Complain"
                    class="q-ml-sm"
                    no-caps
                    :style="{
                      borderRadius: '25px',
                      backgroundColor: '#F55C44',
                      color: 'white',
                    }"
                    @click="complain.status === 'pending' ? (viewComplain = true) : complain.status === 'approved' ? (viewRejectReason = true) : null" />
                  <ViewComplainPopUp v-if="viewComplain" @close="viewComplain = false" :complain="complain" />

                  <ComplainPopUp v-if="studentComplain" :setComplainByStudent="setComplainByStudent" @close="handleComplainPopup" />
                </div>
                <!-- view complain when section is completed and complain is rejected only single time it will appear -->

                <q-btn
                  v-if="canShowViewRejectedOnlyOnce(section, complain)"
                  label="View Complain"
                  class="q-ml-sm"
                  no-caps
                  :style="{
                    borderRadius: '25px',
                    backgroundColor: '#F55C44',
                    color: 'white',
                  }"
                  @click="viewRejectReason = true" />
                <RejectReason v-if="viewRejectReason" @close="handleViewRejectReason" :complain="complain" />

                <q-icon
                  v-if="canShowChatIcon(section, complain, isUnassigned)"
                  name="mail_outline"
                  size="24px"
                  :color="section.status === 'completed' ? 'grey' : 'primary'"
                  class="q-ml-sm"
                  :class="{'cursor-not-allowed': section.status === 'completed'}"
                  @click="() => section.status !== 'completed' && openChatbox()" />

                <div v-if="isComplainPending(section, complain)" style="color: #f55c44; font-weight: bold; font-size: 12px" class="q-ml-md">
                  Complain Pending
                </div>
                <q-space />
                <div class="text-right q-ml-sm text-teal">
                  {{ `${index + 1}/${curSections.length}` }}
                </div>
              </div>
              <!-- div for teacher -->
              <div v-else-if="section && isServiceProvider" class="row items-center q-mt-sm q-pl-na q-gutter-x-na">
                <!-- Button group: only renders when there’s no “rejected” appeal -->
                <div v-if="complain?.status !== 'rejected'" class="row items-center">
                  <!-- mark as completed -->

                  <q-btn
                    v-if="section.status === 'ongoing'"
                    label="Mark as completed"
                    no-caps
                    :style="{
                      borderRadius: '25px',
                      backgroundColor: !isMarkable ? 'rgba(28, 27, 31, 0.12)' : '#f4f7f9',
                      color: !isMarkable ? 'grey' : 'var(--q-primary)',
                    }"
                    :disable="!isMarkable"
                    @click="handleMarkAsCompletion(true)" />

                  <!-- appeal button -->
                  {{ console.log('status', canShowAppealBtn(section, complain)) }}
                  <q-btn
                    v-if="canShowAppealBtn(section, complain)"
                    label="Appeal"
                    class="q-ml-sm"
                    no-caps
                    :style="{
                      borderRadius: '25px',
                      backgroundColor: '#F55C44',
                      color: 'white',
                    }"
                    @click="isAppeal = true" />

                  <!-- view appeal button -->

                  <q-btn
                    v-if="canShowViewAppealBtn(section, complain) && isPending"
                    label="View Appeal"
                    class="q-ml-sm"
                    no-caps
                    :style="{
                      borderRadius: '25px',
                      backgroundColor: '#F55C44',
                      color: 'white',
                    }"
                    @click="viewAppeal = true" />

                  <AppealPopUp v-if="isAppeal" :complain="complain" :setComplain="setComplain" @close="handleAppealPopup" />

                  <ViewAppealPopup v-if="viewAppeal" :complain="complain" @close="handleViewAppealPopup" />

                  <!-- chat icon -->

                  <q-icon
                    v-if="section.status !== 'pending'"
                    name="mail"
                    size="24px"
                    :color="section.status === 'completed' ? 'grey' : 'primary'"
                    class="q-ml-sm"
                    :class="{'cursor-not-allowed': section.status === 'completed'}"
                    :style="{pointerEvents: section.status === 'completed' ? 'none' : 'auto'}"
                    @click="section.status !== 'completed' && openChatbox()" />

                  <q-space />
                </div>

                <div
                  v-if="section.status === 'ongoing' && complain?.status === 'pending'"
                  style="color: #f55c44; font-weight: bold; font-size: 12px"
                  class="q-ml-md">
                  Complain Pending
                </div>
                <q-space />
                <div class="text-teal">
                  {{ `${index + 1}/${curSections.length}` }}
                </div>
              </div>

              <div class="flex text-h6 q-my-lg items-center">
                {{ section.name }}
              </div>
              <!-- teacher name -->
              <div v-if="isMyAssociatedTask && !isUnassigned" class="row">
                <div class="coloumn items-center q-mx-na q-my-md">
                  <div class="row">
                    <q-avatar size="32px"> <img :src="section.servicerInfo?.avatar" /> </q-avatar>
                    <div class="text-subtitle2 text-weight-medium q-ml-sm q-mt-sm">{{ section.servicerInfo?.name?.join(' ') }}</div>
                  </div>
                  <div class="row">
                    <div class="text-caption text-weight-medium text-black-6 q-mt-sm">Assigned to</div>
                    <div class="text-caption text-weight-medium text-grey-6 q-ml-md q-mt-sm">
                      {{ section.lastAssignedTime ? formatDate(section.lastAssignedTime) : 'N/A' }}
                    </div>
                  </div>
                </div>
                <div v-if="section.status === 'completed'" class="coloumn items-center q-mx-xl q-my-md">
                  <div class="row">
                    <q-avatar size="32px"> <img :src="section.servicerInfo?.avatar" /> </q-avatar>
                    <div class="text-subtitle2 text-weight-medium q-ml-sm q-mt-sm">{{ section.servicerInfo?.name?.join(' ') }}</div>
                  </div>
                  <div class="row">
                    <div class="text-caption text-weight-medium text-black-6 q-mt-sm">Completed by</div>
                    <div class="text-caption text-weight-medium text-grey-6 q-ml-md q-mt-sm">
                      {{ section.completedTime ? formatDate(section.completedTime) : 'N/A' }}
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="isServiceProvider" class="coloumn items-center q-mx-na q-my-md">
                <div class="row" v-if="section.status === 'pending' || section.status === 'ongoing'">
                  <div class="text-caption text-weight-medium text-black-6 q-mt-sm">Assigned to me</div>
                  <div class="text-caption text-weight-medium text-grey-6 q-ml-md q-mt-sm">
                    {{ section.lastAssignedTime ? formatDate(section.lastAssignedTime) : 'N/A' }}
                  </div>
                </div>
                <div class="row" v-if="section.status === 'completed'">
                  <div class="text-caption text-weight-medium text-black-6 q-mt-sm">completed by me on</div>
                  <div class="text-caption text-weight-medium text-grey-6 q-ml-md q-mt-sm">
                    {{ section.completedTime ? formatDate(section.completedTime) : 'N/A' }}
                  </div>
                </div>
              </div>

              <q-card
                ><q-expansion-item
                  expand-separator
                  default-opened
                  header-class="flex items-center q-ml-none q-pl-none justify-between text-subtitle1 text-weight-medium "
                  content-class="q-pa-none"
                  class="q-ma-none q-pa-none">
                  <template #header>
                    <div>
                      <span>Prompt</span>
                    </div>
                  </template>

                  <pre style="white-space: pre-wrap; word-wrap: break-word; max-height: 200px; overflow-y: auto; min-height: 100px">{{ section.prompt }}</pre>
                </q-expansion-item></q-card
              >
              <div v-if="!section.status && !isMyAssociatedTask && !isBooking" class="text-right q-mr-md q-mb-sm">
                <q-btn
                  class="q-py-sm"
                  icon="edit"
                  size="sm"
                  color="white"
                  text-color="primary"
                  rounded
                  no-caps
                  style="
                    width: 140px;
                    background: #fffbfe;
                    font-weight: 500;
                    box-shadow:
                      0px 1px 3px 1px #00000026,
                      0px 1px 2px 0px #00000026;
                  "
                  @click="editSectionClick(section._id)">
                  <span style="font-size: 14px">&nbsp;Edit</span>
                </q-btn>
              </div>

              <div v-if="((isMyAssociatedTask || isServiceProvider) && section.status !== 'pending') || isUnassigned" class="q-mt-md">
                <UploadFile
                  :sectionId="section._id"
                  :servicerInfo="section.servicerInfo"
                  :bookerInfo="section.bookerInfo"
                  :isReadOnly="checkReadOnly(section, isUnassigned)"
                  :isReadonlyforProvider="checkReadonlyForProvider(section, complain, isUnassigned)" />
              </div>
            </q-card>
          </q-tab-panel>
        </q-tab-panels>
      </div>
      <!-- right sections part -->
      <div style="width: 225px">
        <div style="position: relative; z-index: 999; width: 100%; height: 100%">
          <div class="flex justify-end text-bold text-teal q-mr-xl text-subtitle1 q-mb-sm">Total {{ curSections.length }}</div>
          <div style="max-height: 500px; overflow-y: auto; position: relative">
            <div style="position: absolute; top: 0; bottom: 0; left: 7px; border-left: 2px solid grey; height: auto"></div>
            <q-tabs
              v-model="tab"
              @update:model-value="handleTabChange"
              vertical
              indicator-color="transparent"
              stretch
              no-caps
              style="height: 100%; width: 100%">
              <q-tab v-for="(section, index) in curSections" :key="index" :name="section._id" class="q-py-sm q-mb-lg">
                <div v-if="isUnassigned && section.status !== 'completed'" class="row q-ml-md">
                  <span style="position: absolute; left: -8px; top: 12px; z-index: 999">
                    <q-avatar color="teal" text-color="white" size="16px" style="z-index: 999">
                      {{ index + 1 }}
                    </q-avatar>
                  </span>

                  <div
                    :style="{
                      width: '3px',
                      borderTopLeftRadius: '8px',
                      borderBottomLeftRadius: '8px',
                      backgroundColor: section._id === tab ? '#04766B' : 'transparent',
                    }" />

                  <div class="coloumn">
                    <div
                      class="row items-center justify-between q-py-sm"
                      :style="{
                        backgroundColor: '#E1EEEC',
                        width: '190px',
                      }">
                      <div class="text-caption q-ml-sm" :style="{color: '#04766B'}">
                        <q-tooltip v-if="section.name.length > 8">
                          {{ section.name }}
                        </q-tooltip>
                        {{ truncate(section.name) }}
                      </div>
                    </div>

                    <div class="row q-mx-sm" style="align-items: center; justify-content: space-between">
                      <div
                        :style="{
                          height: '37px',
                          fontSize: '14px',
                          textAlign: 'left',
                          textTransform: 'none',
                          display: 'flex',
                          alignItems: 'center',
                          color: 'black',
                        }">
                        Credit: {{ getCredits(section) }}/{{ isServiceProvider ? section.costPrice : section.salesPrice }}
                      </div>

                      <div
                        v-if="!isServiceProvider && getCredits(section) < section.salesPrice"
                        :style="{
                          color: 'red',
                          fontSize: '14px',
                          fontWeight: 'bold',
                        }">
                        Deficit: {{ section.salesPrice - getCredits(section) }}
                      </div>
                    </div>
                  </div>
                </div>

                <div v-else class="row q-ml-md">
                  <span style="position: absolute; left: -8px; top: 12px; z-index: 999">
                    <q-avatar color="teal" text-color="white" size="16px" style="z-index: 999">
                      {{ index + 1 }}
                    </q-avatar>
                  </span>

                  <div
                    :style="{
                      width: '3px',
                      borderTopLeftRadius: '8px',
                      borderBottomLeftRadius: '8px',
                      backgroundColor: section._id === tab ? getTextColor(section.status) : 'transparent',
                    }" />
                  <div class="coloumn">
                    <div
                      class="row items-center justify-between q-py-sm"
                      :style="{
                        backgroundColor: getBackgroundColor(section.status),
                        width: '185px',
                      }">
                      <div class="text-caption q-ml-sm" :style="{color: getTextColor(section.status)}">
                        <q-tooltip v-if="section.name.length > 8">
                          {{ section.name }}
                        </q-tooltip>
                        {{ truncate(section.name) }}
                      </div>

                      <q-chip
                        square
                        :ripple="false"
                        :label="section.status"
                        :style="{
                          width: '61px',
                          height: '20px',
                          gap: '10px',
                          borderRadius: '4px',
                          padding: '2px 4px',
                          backgroundColor: getChipColor(section.status),
                          color: getTextColor(section.status),
                          fontWeight: 500,
                          fontSize: '10px',
                          textTransform: 'capitalize',
                        }" />
                    </div>
                    <div
                      :style="{
                        height: '37px',
                        fontSize: '14px',
                        textTransform: 'none',
                        display: 'flex',
                        alignItems: 'center',
                        color: 'black',
                      }">
                      <div class="row" v-if="section.availableCredits" style="width: 185px">
                        <div class="q-ml-sm">Credit: {{ getCredits(section) }}/{{ isServiceProvider ? section.costPrice : section.salesPrice }}</div>
                        <q-space />
                        <div v-if="!isServiceProvider && getCredits(section) < section.salesPrice" class="q-mx-sm" style="color: #dc361d">
                          Deficit: {{ section.salesPrice - getCredits(section) }}
                        </div>
                      </div>
                    </div>

                    <div
                      :style="{
                        borderBottom: section.status !== 'refunded' ? '1px solid lightgray' : 'none',
                      }" />
                    <div v-if="section.status !== 'refunded' || isUnassigned" class="row q-mx-sm items-center justify-between q-py-sm">
                      <div class="text-subtitle2" style="padding: 0; color: black; text-transform: none; margin: 0; font-weight: normal">
                        {{ section.servicerInfo?.name?.join(' ') }}
                      </div>
                      <q-avatar size="24px">
                        <img :src="section.servicerInfo?.avatar" />
                        <q-tooltip>
                          {{ section.servicerInfo?.name[0] }}
                        </q-tooltip>
                      </q-avatar>
                    </div>
                  </div>
                </div>
              </q-tab>
            </q-tabs>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, isReadonly, onUnmounted, onMounted, ref, watch} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import UploadFile from './UploadFile.vue'
import ComplainPopUp from './PopUps/ComplainPopUp.vue'
import ViewComplainPopUp from './PopUps/ViewComplainPopUp.vue'
import RejectReason from './PopUps/RejectReason.vue'
import AppealPopUp from './PopUps/AppealPopUp.vue'
import ViewAppealPopup from './PopUps/ViewAppealPopup.vue'
import {serviceTaskStore} from 'stores/serviceTask'
import Index from 'src/components/ChatMessage/Index.vue'
import useUser from 'src/composables/common/useUser.ts'
import {pubStore} from 'stores/pub'

const serviceTask = serviceTaskStore()

const router = useRouter()

const showPopup = ref(true)

const isMyAssociatedTask = computed(() => route.query.tab === 'myAssociatedTask')
const isServiceProvider = computed(() => route.query.tab === 'taskManagement')
const isUnassigned = computed(() => route.query.subtab === 'unassigned')

const complain = ref(null)
const isPending = ref(true)
const sectionDetailsLoading = ref(false)

const isBooking = computed(() => route.path.includes('/booking/'))
const {getOneById: getUserById} = useUser()

const props = defineProps({
  currentTaskSections: {
    type: Array,
    default: () => [],
  },
})

const curSections = computed(() => {
  if (serviceTask.isSectionsAvaialable) {
    console.log('isSectionsAvaialable: true', serviceTask.currentSection)
    return serviceTask.sections
  } else {
    console.log('isSectionsAvaialable: false, taking sections from props')
    return props.currentTaskSections
  }
})

const route = useRoute()
const pub = pubStore()
const isStudent = ref(pub.user?.roles?.includes('student'))
const currentTab = route.query.tab

const defaultSection = computed(() => {
  return curSections.value?.find((section) => section.status === 'ongoing')
})

const tab = ref(props.currentTaskSections[0]?._id || '')

const hasComplained = ref(false)
const isActive = computed(() => complain?.value?.status !== 'pending')
const isMarkable = computed(() => !serviceTask.currentSection.markAsCompleted && (!complain.value || complain.value?.status === 'approved'))
const viewComplain = ref(false)
const studentComplain = ref(false)
const viewRejectReason = ref(false)
const isAppeal = ref(false)
const viewAppeal = ref(false)
const isCollapsed = ref(false)
function toggle() {
  isCollapsed.value = !isCollapsed.value
}

const isWithin24Hours = (updatedTime) => {
  const now = new Date()
  const diffMs = now - new Date(updatedTime)
  return diffMs <= 24 * 60 * 60 * 1000
}

const checkReadOnly = (section, isUnassigned) => {
  return (section.status === 'ongoing' && section.markAsCompleted) || section.status === 'completed' || isUnassigned.value
}
const checkReadonlyForProvider = (section, complain, isUnassigned) => {
  return (
    (section.status === 'ongoing' && section.markAsCompleted) ||
    (section.status === 'ongoing' && complain?.status === 'pending') ||
    complain?.status === 'rejected' ||
    section.status === 'completed' ||
    isUnassigned
  )
}

async function fetchComplaint(section) {
  try {
    console.log('fetching complaint for section:', section, section.bookingId, section._id)

    if (!section?.bookingId || !section?._id) {
      console.warn('Missing bookingId or session id, skipping complaint fetch')
      complain.value = null
      return
    }

    const result = await App.service('teaching-accident').find({
      query: {
        serviceBooking: section.bookingId,
        session: section._id,
        $limit: 1,
      },
    })

    console.log('result of fetch complaint:', result.data)
    complain.value = result.data[0] || null
  } catch (error) {
    console.error('Error fetching complaint:', error)
    complain.value = null
  }
}

const canShowAppealBtn = (section, complain) => {
  console.log(
    'cnsShowAppealBtn:',
    section?.status === 'ongoing' && complain?.status === 'pending' && (!complain?.evidencesTeacher || complain.evidencesTeacher.length === 0),
    section,
    complain,
    !complain?.evidencesTeacher || complain.evidencesTeacher.length === 0,
    complain?.evidencesTeacher
  )

  return section?.status === 'ongoing' && complain?.status === 'pending' && (!complain?.evidencesTeacher || complain.evidencesTeacher.length === 0)
}

const canShowViewAppealBtn = (section, complain) => {
  console.log('canShownfcb', complain?.status)
  if (complain?.status === 'pending') {
    return section?.status === 'ongoing' && complain?.evidencesTeacher?.length
  }
  return section?.isViewAppeal
}

const canShowConfirmBtn = (section, complain) => {
  return section?.status === 'ongoing' && section?.markAsCompleted && complain?.status !== 'rejected'
}

const canShowComplainBtn = (section, complain) => {
  return section?.markAsCompleted && (!complain || complain.status === 'approved')
}

const canShowViewComplainBtn = (section, complain) => {
  return section?.markAsCompleted && complain && (complain.status === 'pending' || complain.status === 'rejected')
}

const canShowViewRejectedOnlyOnce = (section, complain) => {
  return section?.status === 'completed' && complain?.status === 'rejected' && section?.isViewAppeal
}

const canShowChatIcon = (section, complain, isUnassigned) => {
  return !isUnassigned && section?.status !== 'pending' && complain?.status !== 'rejected'
}

const isComplainPending = (section, complain) => {
  return section?.status === 'ongoing' && complain?.status === 'pending'
}

function formatDate(dateStr) {
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hours = date.getHours()
  const minutes = date.getMinutes().toString().padStart(2, '0')

  return `${year}/${month}/${day} ${hours}:${minutes}`
}

function handleAppealPopup() {
  isAppeal.value = false
}

function setComplain(complainData) {
  complain.value = {...complain.value, ...complainData}
}

function setComplainByStudent(complainData) {
  complain.value = complainData
}

function handleComplainPopup() {
  studentComplain.value = false
}

async function handleTabChange(newTab) {
  sectionDetailsLoading.value = true
  try {
    serviceTask.currentSection = await serviceTask.getSection(newTab)
  } catch (error) {
    console.error('Error fetching section:', error)
  } finally {
    sectionDetailsLoading.value = false
  }
}

watch(
  defaultSection,
  (newVal) => {
    if (newVal) {
      tab.value = newVal._id
    }
  },
  {immediate: true}
)

onMounted(() => {
  if (!serviceTask.currentSection?.length) {
    serviceTask.currentSection = serviceTask.sections.find((section) => section.status === 'ongoing') || serviceTask.sections[0]
  }
})

watch(
  () => serviceTask.currentSection,
  (newSection) => {
    complain.value = null

    if (newSection) {
      console.log('newVal', newSection)
      fetchComplaint(newSection)
    }
  },
  {immediate: true}
)

const splitterModel = ref(70)

function getBackgroundColor(status) {
  if (status === 'pending') return '#FFFBE6'
  if (status === 'ongoing') return '#E6F8FF'
  if (status === 'completed') return '#D5F0D8'
  if (status === 'refunded') return '#FFEEEF'
  return '#ffffff'
}

function getTextColor(status) {
  if (status === 'pending') return '#DFA208'
  if (status === 'ongoing') return '#10A4DF'
  if (status === 'completed') return '#0F8D49'
  if (status === 'refunded') return '#E2452D'
  return '#000000'
}

function getChipColor(status) {
  if (status === 'pending') return '#F8EEBD'
  if (status === 'ongoing') return '#BDE8F8'
  if (status === 'completed') return '#A0E5A7'
  if (status === 'refunded') return '#FEDEDC'
  return '#000000'
}

function truncate(text, length = 8) {
  return text.length > length ? text.slice(0, length) + '…' : text
}

function openChatbox() {
  $q.dialog({
    component: Index,
    componentProps: {
      rid: serviceTask.currentSection.serviceTaskId,
      currentSection: serviceTask.currentSection,
      type: 'section',
    },

    persistent: true,
    maximized: false,
    noEscClose: true,
  })
}

async function handleViewAppealPopup() {
  console.log('handleViewAppealPopup checking complain status', complain.value?.status)
  if (serviceTask.currentSection?.isViewAppeal) {
    try {
      await App.service('section').patch(serviceTask.currentSection._id, {isViewAppeal: false})
      isPending.value = false
    } catch {
      console.log('error in updating isViewAppeal')
    }
  }

  viewAppeal.value = false
}

async function handleViewRejectReason() {
  if (serviceTask.currentSection?.isViewAppeal) {
    try {
      await App.service('section').patch(serviceTask.currentSection._id, {isViewAppeal: false})
    } catch {
      console.log('error in updating isViewAppeal')
    }
  }
  viewRejectReason.value = false
}

async function handleConfirmCompletion(isMarked) {
  try {
    const nextSection = curSections.value?.find((section) => section.sectionNo === serviceTask.currentSection.sectionNo + 1)
    console.log('gkgkglkglgl', nextSection, curSections.value)
    await serviceTask.handleConfirmAsCompleted(serviceTask.currentSection._id, isMarked)
    await serviceTask.updateNextSectionStatus(nextSection, 'ongoing')
    if (!nextSection) {
      if (isStudent.value) {
        router.replace({path: '/study/purchased', query: {tab: 'myAssociatedTask', subtab: 'completed'}})
      } else {
        router.replace({path: '/home/<USER>', query: {tab: 'myAssociatedTask', subtab: 'completed'}})
      }
    }
    isActive.value = false
  } catch (error) {
    console.error('Failed to confirm completion:', error)
  }
  // } finally(() => {
  //   userClicked.value = true
  // })
}

async function handleMarkAsCompletion(isMarked) {
  console.log('qwert', serviceTask.currentSection._id, isMarked)
  isMarkable.value = false
  await serviceTask.handleMarkAsCompleted(serviceTask.currentSection._id, isMarked)
}

const editSectionClick = (id) => {
  const fullQueryString = Object.entries(route.query)
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
  const currentPath = route.path
  router.push({
    path: `${currentPath}/${id}`,
    query: {
      ...route.query,
      back: `/sys/package/edit?${fullQueryString}`,
    },
  })
}

const showComplainBtn = computed(() => section.markAsCompleted && (!complain.value || complain.value.status === 'approved'))

const showViewComplainBtn = computed(
  () => section.markAsCompleted && complain.value && (complain.value.status === 'pending' || complain.value.status === 'rejected')
)

const getCredits = (section) => {
  if (!section) return 0

  let userIdToMatch = null

  if (isMyAssociatedTask.value) {
    return section.availableCredits
  } else if (isServiceProvider.value) {
    return section.costPrice
  }
  return 0
}

function setupSectionSocketListeners() {
  App.service('section').on('patched', async (data) => {
    console.log('Socket Data Updated', data)
    serviceTask.currentSection = data
    await serviceTask.getSections(data.serviceTaskId)
  })
}
function cleanupSectionSocketListeners() {
  App.service('section').removeAllListeners('patched')
}
onMounted(() => {
  if (defaultSection.value) {
    tab.value = defaultSection.value._id
  }

  if (route.query.openChat === '1') {
    openChatbox()
  }

  if (route.query.openComplain === '1') {
    viewComplain.value = true
  }

  if (route.query.openAppeal === '1') {
    isAppeal.value = true
  }

  if (route.query.openReject === '1') {
    viewRejectReason.value = true
  }

  setupSectionSocketListeners()
})

onUnmounted(() => {
  cleanupSectionSocketListeners()
})
</script>

<style scoped>
.q-tabs .q-tab__content {
  width: calc(100% - 20px) !important;
  align-items: stretch;
}

.q-tabs--left .q-tab__indicator {
  left: 0 !important;
  right: auto !important;
  width: 3px !important;
}

::v-deep(.q-splitter__separator) {
  width: 3px !important;
  z-index: 1 !important;
  pointer-events: none;
  position: relative;
  left: 10px;
}

pre {
  all: unset;
  white-space: pre-wrap;
}
</style>
