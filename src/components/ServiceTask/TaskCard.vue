<template>
  {{ console.log('task-card', task) }}

  <q-item v-if="task" class="q-pa-none" :clickable="clickAllowed" @click="onClick">
    <q-card
      class="q-pa-md full-width"
      :class="{'remove-bg': clickAllowed}"
      :elevation="noElevation ? 0 : 2"
      :style="noElevation ? 'border-radius: 0px; box-shadow: none' : 'box-shadow: 0px 4px 4px 1px #00000040; border-radius: 16px'">
      <div class="q-pa-md row no-wrap items-center">
        <q-img :src="hashToUrl(task?.cover)" style="width: 150px; height: 100px; border-radius: 8px" class="q-mr-md" />

        <div class="col">
          <div v-if="deleteEnable" class="text-right" style="position: relative; bottom: 12px; left: 12px">
            <q-btn v-if="!isPublished" round text-color="red" size="sm" icon="delete" @click="handleDelte" />
          </div>
          <div class="row items-center justify-between">
            <div v-if="isDeficient">
              <div class="text-h6 text-bold">{{ task.section.name }}</div>
              <div class="text-weight-medium">under the task- {{ task.name }}</div>
            </div>
            <div v-else-if="task.name" class="text-h5 text-bold">{{ task.name }}</div>
            <div v-else class="text-h5">Untitled</div>
            <q-chip
              v-if="!isBuy && !isPurchased && !isMyAssociatedTask && !isFacilitate"
              square
              :color="task.status ? 'green-2' : 'red-2'"
              :text-color="task.status ? 'green-7' : 'red-7'"
              style="font-weight: 500"
              :ripple="false"
              :label="task.status ? 'Published' : 'Unpublished'"></q-chip>
            <div v-if="isDeficient">{{ task.section.sectionNo }} / {{ task.sections?.length }}</div>
          </div>
          <div class="q-mt-xs">
            <span class="text-amber-10" style="font-size: 14px">Associated task</span>
            <span class="text-teal-5 q-ml-sm" style="font-size: 16px; font-weight: 500">{{
              servicePackage.mentoringTypeList.find((e) => e.value === task.mentoringType)?.label
            }}</span>
          </div>

          <div class="text-teal-3 q-mt-sm ellipsis-2-lines" style="font-size: 14px; font-weight: 500">
            {{ task.description }}
          </div>

          <div class="row items-end">
            <div style="font-size: 14px; font-weight: 500">No. of Sections</div>
            <div class="q-ml-lg" style="color: #787579; font-size: 16px">{{ task.sections?.length ?? 0 }}</div>
          </div>
          <div class="row justify-end q-mt-sm">
            <div v-if="isDeficient" class="text-bold text-red text-h6">Deficit: USD {{ (deficientValue / 100).toFixed(2) }}</div>
            <div v-else-if="isPremium" class="text-teal-4 text-bold text-body1 column items-end">
              <s v-if="associateTaskDiscount" class="text-grey-6 text-body2">USD {{ (task.price / 100).toFixed(2) ?? 0 }}</s>
              USD {{ ((task.price / 100) * (1 - (associateTaskDiscount || 0) / 100)).toFixed(2) ?? 0 }}
            </div>
            <div v-else-if="isBuy" class="text-teal-4 text-bold text-body1 column items-end">
              <s v-if="associateTaskDiscount" class="text-grey-6 text-body2"
                >$ {{ task.sections?.reduce((sum, section) => sum + section.salesPrice, 0).toFixed(2) ?? 0 }}</s
              >
              $ {{ ((orderPrice || task.price) / 100) * (1 - (associateTaskDiscount || 0) / 100).toFixed(2) ?? 0 }}
            </div>
            <div v-else-if="isMyAssociatedTask" class="text-teal-4 text-bold text-h6 column items-end">
              <span v-if="creditDeficit" class="text-bold text-red-4">Deficit: USD {{ creditDeficit }} </span>
              <span v-else class="text-bold">USD {{ task.price ? (task.price / 100).toFixed(2) : 0 }}</span>
            </div>
            <div v-else-if="isFacilitate" class="text-teal-4 text-bold text-h6 column items-end">
              <span class="text-bold">USD {{ task.sections?.reduce((sum, section) => sum + section.costPrice, 0).toFixed(2) ?? 0 }}</span>
            </div>
            <div v-else-if="!isPurchased">
              <div class="text-teal-4" style="font-size: 16px">
                <div>
                  Total sales price:
                  <span class="text-bold">USD {{ (task.price / 100).toFixed(2) ?? 0 }}</span>
                </div>
                <div>
                  Total cost of Sections:
                  <span class="text-bold">USD {{ task.sections?.reduce((sum, section) => sum + section.costPrice, 0).toFixed(2) ?? 0 }}</span>
                </div>
              </div>
              <q-btn class="bg-teal-5 text-white q-ml-sm" rounded outline no-caps label="View details" @click="handleServiceTask(task)" />
            </div>
          </div>

          <div v-if="!isSelect && !isView" class="row justify-end q-mt-sm items-center">
            <q-btn label="Sales statistic" no-caps outline color="primary" rounded :to="`/order/packageSalesStatistic/${task?._id}`" />
          </div>
        </div>
      </div>
      <div v-if="!isBuy" class="text-body2 text-grey-9 q-mt-sm">Last updated on {{ formatDate(task.updatedAt) }}</div>
    </q-card>
  </q-item>
</template>

<script setup>
import {servicePackageStore} from 'stores/service-package'
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'

const route = useRoute()
const router = useRouter()
const servicePackage = servicePackageStore()

const props = defineProps({
  task: {
    type: Object,
    required: true,
  },
  isView: {
    type: Boolean,
    default: false,
  },
  isSelect: {
    type: Boolean,
    default: false,
  },
  isPublished: {
    type: Boolean,
    default: false,
  },
  deleteEnable: {
    type: Boolean,
    default: false,
  },
  isBuy: {
    type: Boolean,
    default: false,
  },
  clickAllowed: {
    type: Boolean,
    default: false,
  },
  noElevation: {
    type: Boolean,
    default: false,
  },
  associatePackUserId: {
    type: String,
    default: null,
  },
  isDeficient: {
    type: Boolean,
    default: false,
  },
  deficientValue: {
    type: Number,
    default: 0,
  },
  associateTaskDiscount: {
    type: Number,
    default: null,
  },
  isPremium: {
    type: Boolean,
    default: false,
  },
  orderPrice: {
    type: Number,
    default: null,
  },
  isPurchased: {
    type: Boolean,
    default: false,
  },
  isMyAssociatedTask: {
    type: Boolean,
    default: false,
  },
  creditDeficit: {
    type: Number,
    default: 0,
  },
  isFacilitate: {
    type: Boolean,
    default: false,
  },
})

const isSys = computed(() => route.path.includes('sys'))

function onClick() {
  if (isSys.value) {
    router.push({
      path: '/sys/package/edit',
      query: {
        tab: 'service-task',
        back: route.fullPath,
        id: props.task._id,
      },
    })
  } else if (props.isMyAssociatedTask) {
    if (!props.associatePackUserId) {
      return
    }
    router.push({
      path: `purchased/view/${props.associatePackUserId}`,
      query: {...route.query, back: route.fullPath},
    })
  } else if (props.isFacilitate) {
    if (!props.associatePackUserId) {
      return
    }
    router.push({
      path: `services/view/${props.associatePackUserId}`,
      query: {...route.query, back: route.fullPath},
    })
  } else if (route.query.tab === 'purchased' || props.isPurchased) {
    if (!props.associatePackUserId) {
      return
    }
    router.push({
      path: `/detail/booked/associatedTask/${props.associatePackUserId}`,
      query: {
        back: route.fullPath,
        id: props.task._id,
      },
    })
  } else {
    router.push({
      path: `/detail/booking/associatedTask/${props.task._id}`,
      query: {
        back: route.fullPath,
        id: props.task._id,
      },
    })
  }
}

function handleServiceTask(task) {
  console.log('jjjvmvm', task)
  if (props.isSelect || !servicePackage._id) {
    const url = router.resolve({
      path: '/sys/package/edit',
      query: {
        tab: 'service-task',
        back: `/sys/package?tab='service-task'`,
        id: task._id,
      },
    }).href
    window.open(url, '_blank')
  } else {
    router.push({
      path: '/sys/package/edit',
      query: {
        tab: 'service-task',
        back: route.fullPath,
        id: task._id,
      },
    })
  }
}

const handleDelte = () => {
  servicePackage.unSetAssociatedTask()
  servicePackage.associatedTask = {}
  servicePackage.addedAssociatedTask = null
  $q.notify({
    message: 'Deleted successfully',
    type: 'positive',
  })
}

function formatDate(isoString) {
  const date = new Date(isoString)

  const day = String(date.getDate()).padStart(2, '0')
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const year = date.getFullYear()

  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`
}
onMounted(() => {
  if (!isSys.value) {
    servicePackage.init()
  }
})
</script>

<style scoped>
.remove-bg:hover {
  background: transparent !important;
  box-shadow: none;
}
</style>
