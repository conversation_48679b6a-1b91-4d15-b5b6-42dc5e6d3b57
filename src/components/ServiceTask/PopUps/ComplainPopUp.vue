<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card class="q-pa-md" style="width: 450px; max-width: 90vw; border-radius: 12px; background-color: #e0f2f1">
      <!-- Title Section -->
      <q-card-section>
        <div class="text-subtitle1 q-mb-md">Please provide reason why you’d like to complain about the service of this Unit *</div>
        <RecordInput v-model="complaintReason" :counter="true" :maxlength="1000" outlined placeholder="Please enter the reason" />
      </q-card-section>

      <!-- Evidence preview Section -->
      <q-card-section v-if="files?.length">
        <div class="text-subtitle2 q-my-xs">Evidences</div>
        <div class="row q-gutter-sm">
          {{ console.log('fhruf', files) }}
          <div v-for="(file, index) in files" :key="index" style="border-radius: 10px">
            <div class="q-pa-xs" style="display: inline-block; border-radius: 10px">
              <div style="position: relative; display: inline-block">
                <q-img
                  v-if="file.mime === 'image/jpeg'"
                  :src="hashToUrl(file.cover)"
                  style="width: 60px; height: 45px; border-radius: 10px; overflow: hidden" />
                <q-video v-else :src="hashToUrl(file.cover)" style="width: 60px; height: 45px; border-radius: 10px" :controls="true" autoplay="false" />
                <q-btn round dense color="negative" icon="close" size="10px" class="absolute" style="top: -6px; right: -6px" @click="removeFile(index)" />
              </div>
            </div>
          </div>
        </div>
      </q-card-section>

      <q-card-section>
        <!-- Evidence upload button -->

        <q-btn
          unelevated
          color="primary"
          label="Upload evidences"
          class="q-mt-sm text-white"
          style="height: 40px; width: 100%; border-radius: 100px; padding: 0 24px"
          @click="handleFileChange"
          :disable="files.length >= 3" />

        <!-- <input type="file" ref="fileInput" multiple accept="image/*,video/*" style="display: none" @click="handleFileChange" /> -->

        <div class="text-caption text-grey q-my-md">
          You can provide screenshot, or video clips to prove the insufficiency/poor quality of the service (Maximum 3 files)
        </div>

        <q-separator />
      </q-card-section>

      <!-- Actions button -->
      <q-card-actions class="q-pa-none">
        <div class="row q-col-gutter-sm full-width">
          <div class="col-6">
            <q-btn label="Confirm" color="primary" class="full-width" style="border-radius: 100px" @click="submitComplaint" />
          </div>
          <div class="col-6">
            <q-btn flat label="Cancel" color="primary" class="full-width" style="border-radius: 100px; border: 1px solid black" @click="showDialog = false" />
          </div>
        </div>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, watch, computed} from 'vue'
import {serviceTaskStore} from 'stores/serviceTask'
import {pubStore} from 'stores/pub'
import RecordInput from 'src/components/utils/inputs/RecordInput.vue'

const emit = defineEmits(['close'])

const showDialog = ref(true)
const complaintReason = ref('')
const fileInput = ref(null)
const serviceTask = serviceTaskStore()
const pub = pubStore()
const files = ref([])

const props = defineProps({
  setComplainByStudent: {
    type: Function,
    required: false,
  },
})

async function handleFileChange() {
  const rs = await Fn.fileUpLoadUiX('image/*,video/*,audio/*,.pdf')
  if (!rs) {
    return
  }
  console.log('value of rs:', rs)
  if (rs.message) {
    return $q.notify({type: 'negative', message: rs.message})
  }
  console.log(pub.user)
  // const isVideo = rs.mime.includes('video')

  // console.log('dbfyh', isVideo)

  files.value.push({
    cover: rs._id, // your “hash”
    mime: rs.mime,
    filename: rs.originalName || rs.filename || 'unknown',
  })
}

function removeFile(index) {
  files.value.splice(index, 1)
}

async function submitComplaint() {
  if (!complaintReason.value.trim()) {
    $q.notify({type: 'negative', message: 'Please provide complain reason'})
    return
  }

  const data = {
    student: serviceTask.currentSection.uid,
    teacher: serviceTask.currentSection.servicer,
    session: serviceTask.currentSection._id,
    sessionName: serviceTask.currentSection.name,
    sessionPrompt: serviceTask.currentSection.prompt,
    booking: serviceTask.currentSection.bookingId,
    servicePackUser: serviceTask.currentSection.serviceTaskId,
    tags: 'Associated task complains',
    evidencesStudent: [
      {
        content: complaintReason.value,
        attachments: files.value.map((f) => ({
          filename: f.filename,
          mime: f.mime,
          hash: f.cover,
        })),
      },
    ],
    status: 'pending',
  }

  console.log('data', data)

  const accident = await App.service('teaching-accident').create(data)

  $q.notify({type: 'positive', message: 'Complain submitted successfully'})
  props.setComplainByStudent(data)
  emit('close')
}

watch(showDialog, (val) => {
  if (!val) emit('close')
})
</script>

<style scoped>
.absolute-top-right {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 1;
}
</style>
