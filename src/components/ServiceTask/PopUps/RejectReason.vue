<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card class="q-pa-md" style="width: 450px; max-width: 90vw; border-radius: 12px; background-color: #e0f2f1">
      <!-- Section: Reason of rejection by Classcipe -->

      <q-card-section class="q-pa-na">
        <div class="text-subtitle1 q-mb-md"><strong>Reason of approval by Classcipe</strong></div>
        <div class="text-body2 q-mb-na">{{ complain?.checkReason }}</div>
        <div class="q-my-md" style="height: 1px; background-color: #ccc"></div>
      </q-card-section>

      <!-- Section: Reason provided by you -->
      <q-card-section>
        <div class="text-subtitle1 q-pa-none q-mb-sm"><strong>Reason provided by you</strong></div>
        <div class="text-body2">{{ studentReason }}</div>
        <div v-if="files?.length" class="q-mt-sm text-subtitle2 q-mt-lg">Evidences provided by the student</div>

        <div v-if="files?.length">
          {{ console.log('files by student', files) }}
          <q-scroll-area ref="scrollRef" class="q-mt-sm" style="max-width: 100%; height: 60px" horizontal>
            <div class="row no-wrap q-gutter-sm">
              <div v-for="(file, i) in files" :key="i">
                <q-img
                  v-if="file.mime === 'image/jpeg'"
                  :src="hashToUrl(file.hash)"
                  style="width: 60px; height: 45px; border-radius: 10px; object-fit: cover" />
                <q-video v-else :src="hashToUrl(file.hash)" style="width: 60px; height: 45px; border-radius: 10px" controls />
              </div>
            </div>
          </q-scroll-area>

          <q-btn label="More" no-caps flat size="sm" color="primary" icon-right="chevron_right" class="q-pa-none q-mt-xs" @click="scrollMore" />
        </div>
        <q-separator class="q-my-sm" />
      </q-card-section>

      <!-- Section: Appealing reason provided by service provider -->
      <q-card-section>
        <div class="text-subtitle1 q-pa-none q-mb-sm"><strong>Appealing reason provided by service provider</strong></div>
        <div class="text-body2">{{ providerReason }}</div>
        <div v-if="providerFiles?.length" class="q-mt-sm text-subtitle2">Appealing evidence</div>
        <div v-if="providerFiles?.length" class="row q-gutter-sm q-my-sm">
          {{ console.log('files by serviceProvidor', providerFiles) }}
          <div v-for="(file, i) in providerFiles" :key="i">
            <q-img v-if="file.mime === 'image/jpeg'" :src="hashToUrl(file.hash)" style="width: 60px; height: 45px; border-radius: 10px; object-fit: cover" />
            <q-video v-else :src="hashToUrl(file.hash)" style="width: 60px; height: 45px; border-radius: 10px" controls />
          </div>
        </div>
      </q-card-section>

      <!-- Cancel button -->
      <q-card-actions class="q-pa-none q-mt-md">
        <q-btn flat label="Cancel" color="primary" class="full-width" style="border-radius: 100px; border: 1px solid #ccc" @click="showDialog = false" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, watch} from 'vue'

const showDialog = ref(true)
const scrollRef = ref(null)

const emit = defineEmits(['close'])

const props = defineProps({
  complain: {
    type: Object,
    required: true,
    default: () => ({}),
  },
})

const studentReason = computed(() => props.complain?.evidencesStudent?.[0]?.content)
const files = computed(() => props.complain?.evidencesStudent?.[0]?.attachments)
const providerReason = computed(() => props.complain?.evidencesTeacher?.[0]?.content)
const providerFiles = computed(() => props.complain?.evidencesTeacher?.[0]?.attachments)

function scrollMore() {
  scrollRef.value?.setScrollPosition('horizontal', scrollRef.value.getScrollPosition().left + 100)
}

watch(showDialog, (val) => {
  if (!val) emit('close')
})
</script>
