<template>
  <div class="flex column full-width" :class="{'no-scrollbar': $q.platform.is.desktop}">
    <div class="row q-pt-md q-pb-sm q-pl-md full-width">
      <div class="flex col text-h6">Reviews</div>
      <!-- <q-btn flat text-color="teal" label="More" no-caps/> -->
    </div>
    <q-list class="q-pl-md">
      <template v-for="(o, i) in list.data" :key="i">
        <q-separator />
        <q-item clickable class="q-px-none">
          <q-item-section>
            <q-list>
              <q-item class="q-pb-none">
                <q-item-section avatar class="q-pr-sm" style="min-width: 2rem">
                  <PubAvatar :src="o.user?.avatar" />
                </q-item-section>
                <q-item-section>
                  <div>{{ nameFormatter(o.user) }}</div>
                  <div class="text-grey">
                    <small>{{ new Date(o.createdAt).toLocaleString() }}</small>
                  </div>
                </q-item-section>
                <q-item-section side>
                  <div class="flex-center">
                    Overall
                    <q-icon name="expand_more" size="1.6rem"></q-icon>
                  </div>
                  <q-rating class="q-px-sm" v-model="o.rate" readonly size="1.5rem"></q-rating>
                  <q-menu>
                    <q-list style="min-width: 100px">
                      <q-item clickable v-close-popup v-for="(title, key) in rateOpts" :key="key">
                        <q-item-section>{{ title }}</q-item-section>
                        <q-item-section side><q-rating v-model="o.rates[key]" readonly size="1.5rem"></q-rating></q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-item-section>
              </q-item>
              <q-item-label class="q-pl-xl row">
                <q-chip v-for="(tag, ti) in o.tags" :key="ti" :label="tags[tag]" size="sm" text-color="white" :color="tagsColor[ti]"></q-chip>
                <q-space></q-space>
              </q-item-label>
              <q-item-label class="q-pl-xl q-py-sm text-grey-8">{{ o.note }}</q-item-label>
              <q-btn v-if="pub.user._id === o.uid" label="Edit" color="teal" class="absolute-bottom-right q-ma-sm" flat no-caps>
                <q-popup-proxy v-if="pub.user._id === o.uid" ref="EditProxy">
                  <ReviewEdit
                    :id="content.id"
                    :data="o"
                    :close="
                      (rs) => {
                        EditProxy[0].hide(), Object.assign(o, rs)
                      }
                    " />
                </q-popup-proxy>
              </q-btn>
            </q-list>
          </q-item-section>
        </q-item>
      </template>
    </q-list>
    <ReviewEdit class="q-ml-md" v-if="isBuy && list.isReview === 0" :content="content" />
  </div>
</template>
<script setup>
import {ref, watch, onMounted} from 'vue'
import ReviewEdit from 'components/detail/ReviewEdit.vue'
import {pubStore} from 'stores/pub'
import nameFormatter from 'src/utils/formatters/nameFormatter.js'
const pub = pubStore()
const tags = ref([])
const EditProxy = ref(false)
window.EditProxy = EditProxy
const list = ref({})
const rateOpts = {se: 'Students engagement', et: 'Effectiveness of teaching & learning', qc: 'Quality of the content'}
const tagsColor = ['deep-orange', 'teal', 'pink']
const props = defineProps(['content'])
const isBuy = props.content.order?._id || props.content.reg

async function main() {
  if (!props.content?.id) return
  find()
  tags.value = await pub.conf('reviewsTags')
  console.log('get:', tags, props.content)
}
watch(() => props.content, main)
onMounted(main)

async function find() {
  list.value = await App.service('reviews').find({query: {rid: props.content?.id}})
  console.log(list.value)
}
</script>
