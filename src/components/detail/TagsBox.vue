<template>
  <div class="flex full-width q-px-md q-pb-md">
    <div class="row q-pb-sm full-width">
      <div class="text-h6">Tags</div>
    </div>
    <q-list>
      <q-item v-for="(arr, cg) in tags" :key="cg">
        <q-item-section>
          <q-item-label>{{ cg }}</q-item-label>
          <q-item-label>
            <q-chip color="primary" text-color="white" v-for="(o, i) in arr" :key="i" :label="o.name"></q-chip>
          </q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </div>
</template>
<script setup>
import {ref, onMounted} from 'vue'
const props = defineProps(['data'])
const tags = ref({})
onMounted(() => {
  props.data.map((v) => {
    if (!tags.value[v.cg]) tags.value[v.cg] = []
    tags.value[v.cg].push(v)
  })
})
</script>
