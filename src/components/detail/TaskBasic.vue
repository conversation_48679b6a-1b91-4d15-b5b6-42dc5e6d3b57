<template>
  <div>
    <div class="row q-pl-md full-width">
      <template v-if="content.task_type">
        <div class="row flex-center">
          <div class="text-h6">Task type</div>
          <q-chip :label="content.task_type" :color="TaskTypeColors[content.task_type]" text-color="white"></q-chip>
        </div>
        <q-space></q-space>
      </template>
      <div class="row flex-center">
        <div class="text-h6">Pages</div>
        <div class="col q-px-md q-py-sm text-subtitle1 text-grey-8">{{ content.pageNum || 0 }} pages</div>
      </div>
    </div>
    <template v-if="content.overview && content.overview.trim()">
      <div class="row q-pl-md q-pt-md full-width">
        <div class="flex col text-h6">Overview</div>
      </div>
      <div class="row full-width q-px-md q-py-sm text-body2 text-grey-8">{{ content.overview }}</div>
    </template>
    <template v-if="content.inquiry">
      <div class="row q-pt-md q-pl-md full-width">
        <div class="flex col text-h6">Big Idea/ Statement of Inquiry/ Central Idea</div>
      </div>
      <div class="row full-width q-px-md q-py-sm text-body2 text-grey-8">{{ content.inquiry }}</div>
    </template>

    <template v-if="!isEmpty(content.question)">
      <div class="row q-pt-md q-pl-md full-width">
        <div class="flex col text-h6">Key question(s) / Line(s) of inquiry</div>
      </div>
      <div class="row full-width q-px-md q-py-sm text-body2 text-grey-8" v-for="(question, index) in content.question" :key="index">
        {{ question.name }}
      </div>
    </template>
    <TagsBox v-if="!isEmpty(content.tags)" :data="content.tags" />
  </div>
</template>
<script setup>
import TagsBox from 'components/detail/TagsBox.vue'
const props = defineProps(['content'])
</script>
