<template>
  <q-form class="q-pa-sm bg-grey-2 column" @submit="onSubmit" :class="$q.screen.gt.xs ? '' : 'full-width'">
    <div class="row">
      <q-field class="col-6" :model-value="form.rate" dense borderless hide-bottom-space no-error-icon :rules="[(val) => val > 0 || false]">
        <template v-slot:control>
          <div style="min-width: 15rem">Overall review</div>
          <q-rating v-model="form.rate" size="2rem" />
        </template>
      </q-field>
      <div class="col-6 flex" v-for="(o, i) in rateOpts" :key="i">
        <div class="flex items-center" style="min-width: 15rem">{{ o }}</div>
        <q-rating v-model="form.rates[i]" size="2rem" />
      </div>
    </div>
    <div class="col full-width">
      <q-input
        label="Review note"
        v-model="form.note"
        autogrow
        type="textarea"
        :rules="[(val) => val.length > 6 || false]"
        placeholder="Enter your review content"></q-input>
      <div class="row q-pt-md">
        <q-checkbox v-for="(name, key) in tags" :key="key" v-model="form.tags" :label="name"></q-checkbox>
        <q-space></q-space>
        <q-btn type="submit" :label="form?._id ? 'Update' : 'Submit review'" no-caps></q-btn>
      </div>
    </div>
  </q-form>
</template>
<script setup>
import {ref, onMounted} from 'vue'
import {pubStore} from 'stores/pub'
const pub = pubStore()
const props = defineProps(['id', 'data', 'close'])
const rateOpts = {se: 'Students engagement', et: 'Effectiveness of teaching & learning', qc: 'Quality of the content'}
const form = ref({rid: props.id, rate: 0, rates: {se: 0, et: 0, qc: 0}, note: '', tags: []})
const tags = ref([])

onMounted(async () => {
  if (!props.id) return
  if (props.data) Object.assign(form.value, Acan.clone(props.data))
  tags.value = await pub.conf('reviewsTags')
  console.log('get:', tags, form.value)
})

async function onSubmit() {
  console.log('submit', form.value, props.close)
  if (form.value._id) form.value = await App.service('reviews').patch(form.value._id, form.value)
  else form.value = await App.service('reviews').create(form.value)
  $q.notify({type: 'positive', message: 'Review edited successfully'})
  if (props.close) props.close(form.value)
}
</script>
