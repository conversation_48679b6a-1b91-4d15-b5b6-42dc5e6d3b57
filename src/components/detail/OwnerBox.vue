<template>
  <q-list>
    <q-item class="q-pa-none text-left">
      <q-item-section avatar class="q-pr-sm" style="min-width: 2rem">
        <PubAvatar :src="owner?.avatar" :title="schoolInfo?.name" />
      </q-item-section>
      <q-item-section>
        <q-item-label v-if="title" lines="1" class="text-subtitle1 text-bold" :class="theme === 'dark' ? 'text-grey-2' : 'text-grey-9'">
          {{ title }}
        </q-item-label>
        <q-item-label class="text-subtitle2 ellipsis-1-line" style="max-width: 200px" :class="theme === 'dark' ? 'text-grey-3' : 'text-grey-8'">
          <span>
            {{ nameFormatter(owner) }}
            <q-tooltip max-width="200px">
              {{ nameFormatter(owner) }}
            </q-tooltip>
          </span>
        </q-item-label>
        <q-item-label v-if="updatedAt" :class="theme === 'dark' ? 'text-grey-4' : 'text-grey-6'" caption lines="1">
          {{ prepend }}
          {{ date.formatDate(new Date(updatedAt), timeFormat + ' (ddd)') }}
        </q-item-label>
      </q-item-section>
    </q-item>
  </q-list>
</template>
<script setup>
import {inject} from 'vue'
import nameFormatter from 'src/utils/formatters/nameFormatter.js'
import {date} from 'quasar'
const timeFormat = inject('TIME_FORMAT_NZ')
const props = defineProps(['owner', 'prepend', 'schoolInfo', 'title', 'updatedAt', 'theme'])
</script>
