<template>
  <div id="search" :class="showList ? 'fixed-full' : 'fixed-top'" class="column" style="background-color: #00000088; z-index: 2001">
    <q-input class="q-py-none q-px-sm bg-white" v-model="search" placeholder="Search" autofocus @update:model-value="findChange" @keyup.enter="findTo">
      <template v-slot:before>
        <q-icon name="arrow_back" @click="backFn" />
      </template>
      <template v-slot:append>
        <q-icon v-if="search !== ''" name="close" @click="search = ''" class="cursor-pointer" />
        <!-- <q-icon name="search" @click="find" /> -->
      </template>
    </q-input>
    <q-list v-show="showList" class="bg-white">
      <q-item v-show="search" clickable @click="hideList" :replace="route.path === '/search'" :to="`/search?q=${search}`">
        <q-item-section side>
          <span class="text-h6 text-center" style="width: 1.3rem">#</span>
        </q-item-section>
        <q-item-section>Search "{{ search }}"</q-item-section>
      </q-item>
      <q-item v-if="loading" class="flex-center">
        <q-inner-loading :showing="loading" label-class="text-teal" label-style="font-size: 1.1em" />
      </q-item>
      <template v-else>
        <!-- <q-item clickable v-for="(o, i) in data.similarList" :key="i" @click="hideList" :to="`/search?q=${o.name}`">
        <q-item-section side>
          <q-icon name="search" />
        </q-item-section>
        <q-item-section>{{o.name}}</q-item-section>
      </q-item> -->
        <q-item clickable v-for="(o, i) in data.workshop" :key="i" @click="hideFn" :to="`/workshop/${o._id}`">
          <q-item-section side>
            <q-icon name="workspaces" />
            <q-tooltip>Workshop</q-tooltip>
          </q-item-section>
          <q-item-section side>
            <img :src="o.image" style="height: 2rem" />
          </q-item-section>
          <q-item-section>{{ o.name }}</q-item-section>
        </q-item>
        <q-item clickable v-for="(o, i) in data.pd" :key="i" @click="hideFn" :to="`/pd/${o.id}`">
          <q-item-section side>
            <q-icon name="live_tv" />
            <q-tooltip>Service</q-tooltip>
          </q-item-section>
          <q-item-section side>
            <img :src="o.image" style="height: 2rem" />
          </q-item-section>
          <q-item-section>{{ o.name }}</q-item-section>
        </q-item>
        <q-item clickable v-for="(o, i) in data.unit" :key="i" @click="hideFn" :to="`/unit/${o.id}`">
          <q-item-section side>
            <q-icon name="list_alt" />
            <q-tooltip>Unit plan</q-tooltip>
          </q-item-section>
          <q-item-section side>
            <img :src="o.image" style="height: 2rem; width: 3.6rem" />
          </q-item-section>
          <q-item-section>{{ o.name }}</q-item-section>
        </q-item>
        <q-item clickable v-for="(o, i) in data.task" :key="i" @click="hideFn" :to="`/task/${o.id}`">
          <q-item-section side>
            <q-icon name="task" />
            <q-tooltip>Task</q-tooltip>
          </q-item-section>
          <q-item-section side>
            <img :src="o.image" style="height: 2rem" />
          </q-item-section>
          <q-item-section>{{ o.name }}</q-item-section>
        </q-item>
      </template>
    </q-list>
    <div class="col" @click="hideFn"></div>
  </div>
</template>
<style>
#search,
#search a,
#search input {
  font-size: 1rem;
  font-weight: 500;
  color: rgb(128, 134, 139);
}
#search .q-item {
  height: 3.5rem;
}
</style>
<script setup>
import {ref, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const router = useRouter()
const route = useRoute()
import {pubStore} from 'stores/pub'
const pub = pubStore()
const setId = ref(null)
const loading = ref(false)
const showList = ref(false)
const search = ref('')
const data = ref({})
const props = defineProps(['hideFn'])
function backFn() {
  props.hideFn()
  pub.search = ''
  if (route.path === '/search') {
    if (history.state.back) history.back()
  }
}
function hideList() {
  showList.value = false
  pub.search = search.value
}
async function findChange(e) {
  // 延迟搜索
  clearTimeout(setId.value)
  console.log(setId.value, search.value)
  if (search.value) showList.value = true
  data.value = {}
  if (search.value.length < 3) return // 少于3个字符不自动搜索
  setId.value = setTimeout(find, 500)
}
async function findTo() {
  // 回车直接搜索关键词
  hideList()
  clearTimeout(setId.value)
  pub.search = search.value
  console.log('searchTo', search.value)
  router[route.path === '/search' ? 'replace' : 'push']({path: '/search', query: {q: search.value}})
}
async function find() {
  console.log('find', search.value)
  clearTimeout(setId.value)
  loading.value = true
  // data.value = await this.apiPost('api/v2/library/search', { key: search.value })
  data.value = await App.service('unit').get('search', {query: {key: search.value}})
  console.log(data.value)
  loading.value = false
}
onMounted(async () => {
  await sleep(50)
  console.log('mounted search bar', route.path, route.query)
  search.value = route.query.q ?? ''
  if (!route.query.q) showList.value = true // 详情页隐藏搜索条
  if (route.path === '/search') showList.value = false
})
</script>
