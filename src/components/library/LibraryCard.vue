<template>
  <div class="rounded-borders shadow-1 overflow-hidden" :style="{width: `${width}rem`}">
    <div class="card-wrapper relative-position">
      <span v-if="content.price === 0" class="free-tag">Free</span>
      <q-img :src="hashToUrl(content.cover)" :ratio="16 / 9" />
      <div v-if="innerDesc" class="absolute-bottom text-subtitle1 text-left" style="background: rgba(0, 0, 0, 0.47); padding: 0.5rem 0.5rem">
        <OwnerBox :owner="content.owner" :title="content.name" theme="dark" />
      </div>
    </div>
    <OwnerBox class="q-pa-sm" v-if="!innerDesc" :owner="content.owner" :title="content.name" />
  </div>
</template>

<script setup>
import OwnerBox from 'components/detail/OwnerBox.vue'
const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  width: {
    type: Number,
    default: 20,
  },
  innerDesc: {
    type: Boolean,
    default: false,
  },
})
</script>

<style lang="scss" scoped>
.card-wrapper {
  &:hover .free-tag {
    color: #f33;
    background: rgba(50, 220, 150, 0.8);
  }
}
.free-tag {
  position: absolute;
  top: 18px;
  right: -5rem;
  width: 6rem;
  transform: translate(-50%, -50%) rotate(45deg);
  color: #fcc;
  font-weight: bold;
  font-size: 1.1rem;
  background: rgba(50, 220, 150, 0.6);
  padding: 1rem 0 0.5rem;
  z-index: 1;
  transition: 0.3s;
}
</style>
