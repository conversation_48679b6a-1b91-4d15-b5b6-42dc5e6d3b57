<template>
  <div class="content-item q-pa-md">
    <div class="cc-card row justify-between items-center">
      <div class="content-info row">
        <div class="cover">
          <q-img :src="content.image" :ratio="16 / 9" class="cover-img" />
        </div>
        <div class="info q-ml-md col">
          <div class="text-h6 text-bold">
            {{ content.name }}
          </div>
          <div class="text-caption q-py-sm">
            {{ content.updateTime }}
          </div>
          <div class="tag"></div>
          <div class="owner">
            <div class="row items-center justify-start q-gutter-sm">
              <PubAvatar :src="content.owner.avatar" />
              <div class="card-owner-name">
                {{ nameFormatter(content.owner) }}
              </div>
              <div class="star-rating row items-center justify-start">
                <q-rating :model-value="5" size="1em" readonly />
                <span class="rating-num"> 10</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-buy q-pr-md">
        <div class="buy-info row items-center">
          <div class="price text-bold text-red-7 q-px-sm">$ {{ content.price }}</div>
          <div class="buy-action">
            <q-btn color="red-7" class="q-px-md" label="Buy" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {defineComponent} from 'vue'
import nameFormatter from 'src/utils/formatters/nameFormatter.js'

export default defineComponent({
  name: 'ContentItem',
  props: {
    content: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {}
  },
  created() {},
  methods: {},
})
</script>

<style lang="scss" scoped>
.content-item {
  background-color: #fff;
  border-radius: 5px;
}

.cover-img {
  width: 15rem;
}
</style>
