<template>
  <q-dialog :persistent="persistent" ref="dialogRef" @hide="onDialogHide" @before-show="beforeDialogShow">
    <q-card style="width: 40vw; max-width: 50vw" class="bg-teal-1">
      <q-card-section>
        <q-toolbar v-if="!isServiceTask" class="text-h6"> Basic setting for the service package </q-toolbar>
        <q-toolbar v-else class="text-h6"> Creating associated task </q-toolbar>
      </q-card-section>
      <q-card-section v-if="!isServiceTask">
        <q-toolbar class="text-subtitle1 text-weight-medium"> Service role</q-toolbar>
        <div>
          <template v-for="(role, index) in servicePackage.serviceRolesList" :key="index">
            <q-btn
              v-if="role.main && (role.value === 'mentoring' ? actionAuthMap.add_mentor : actionAuthMap.add_other)"
              class="q-ma-sm"
              :class="servicePackage.serviceRoles === role.value ? 'bg-teal-4 text-white' : 'text-teal'"
              outline
              no-caps
              :label="role.label"
              @click="categoryClick(role.value)"></q-btn>
          </template>
        </div>
      </q-card-section>
      <q-card-section v-if="servicePackage.serviceRoles === 'consultant'">
        <q-toolbar class="text-subtitle1 text-weight-medium">Consultant type</q-toolbar>
        {{ console.log('consultantTypeList', servicePackage.consultantTypeList) }}
        <q-btn
          v-for="(item, key) in servicePackage.consultantTypeList"
          :key="key"
          class="q-ma-sm"
          :class="servicePackage.consultant?.type == item.value ? 'bg-teal-4 text-white' : 'text-teal'"
          outline
          no-caps
          :label="item.label"
          @click="consultantTypeClick(item.value)"></q-btn>
      </q-card-section>
      <q-card-section>
        <q-toolbar v-if="!isServiceTask" class="text-subtitle1 text-weight-medium"> Service type</q-toolbar>
        <q-select
          v-if="servicePackage.type && (servicePackage.serviceRoles || isServiceTask)"
          v-model="servicePackage.mentoringType"
          :options="mentoringTypeOptions"
          color="teal"
          class="q-mx-sm"
          label-color="teal"
          emit-value
          map-options
          outlined
          label="Please choose the service type"
          icon="search"
          @update:model-value="onMentoringChange"></q-select>
      </q-card-section>
      <!-- NOT IN USE: use first topic layer -->
      <q-card-section v-if="servicePackage.countryCodeRequired">
        <q-toolbar class="text-subtitle1 text-weight-medium">Choose a country</q-toolbar>
        <q-btn
          v-for="(code, i) in overseasStudyList"
          :key="i"
          class="q-ma-sm"
          :class="servicePackage.countryCode?.[0] === code.value ? 'bg-teal-4 text-white' : 'text-teal'"
          outline
          no-caps
          :label="code.label"
          @click="servicePackage.countryCode = [code.value]"></q-btn>
      </q-card-section>
      <q-card-section>
        <q-toolbar class="text-subtitle1 text-weight-medium">Choose a qualification for the service provider</q-toolbar>

        <template v-if="isServiceTask">
          <q-btn
            class="q-ma-sm"
            :class="servicePackage.qualification === 'consultant' ? 'bg-teal-4 text-white' : 'text-teal'"
            outline
            no-caps
            label="Consultant"
            @click="qualificationClick('consultant')"></q-btn>
          <q-btn
            class="q-ma-sm"
            :class="servicePackage.qualification === 'seniorConsultant' ? 'bg-teal-4 text-white' : 'text-teal'"
            outline
            no-caps
            label="Senior Consultant"
            @click="qualificationClick('seniorConsultant')"></q-btn>
          {{ console.log(servicePackage.qualification) }}
        </template>

        <!-- For other roles -->
        <template v-else>
          <q-btn
            v-if="servicePackage.isEducatorServiceType || (servicePackage.serviceRoles == 'consultant' && servicePackage.consultant.type !== 'carer')"
            class="q-ma-sm"
            :class="servicePackage.qualification === 'workshopLeader' ? 'bg-teal-4 text-white' : 'text-teal'"
            outline
            no-caps
            label="Workshop leader/Senior lecturer"
            @click="qualificationClick('workshopLeader')"></q-btn>
          <q-btn
            class="q-ma-sm"
            :class="servicePackage.qualification === 'experiencedTeacher' ? 'bg-teal-4 text-white' : 'text-teal'"
            outline
            no-caps
            label="Experienced teacher"
            @click="qualificationClick('experiencedTeacher')"></q-btn>
          <q-btn
            v-if="!servicePackage.isEducatorServiceType && servicePackage.serviceRoles !== 'consultant'"
            class="q-ma-sm"
            :class="servicePackage.qualification === 'studentTutor' ? 'bg-teal-4 text-white' : 'text-teal'"
            outline
            no-caps
            label="Student tutor"
            @click="qualificationClick('studentTutor')"></q-btn>
        </template>
      </q-card-section>

      <q-card-section>
        <q-toolbar class="text-subtitle1 text-weight-medium">Content orientated</q-toolbar>
        <q-btn
          v-if="!isServiceTask && servicePackage.serviceRoles === 'mentoring'"
          class="q-ma-sm"
          :class="servicePackage.contentOrientatedEnable ? 'bg-teal-4 text-white' : 'text-teal'"
          outline
          no-caps
          label="Yes"
          @click="contentOrientatedClick(true)"></q-btn>
        <q-btn
          class="q-ma-sm"
          :class="!servicePackage.contentOrientatedEnable ? 'bg-teal-4 text-white' : 'text-teal'"
          outline
          no-caps
          label="No"
          @click="contentOrientatedClick(false)"></q-btn>
      </q-card-section>
      <q-card-section v-if="servicePackage.serviceRoles === 'substitute'">
        <q-toolbar class="text-subtitle1 text-weight-medium">Service scenario</q-toolbar>
        <q-btn
          class="q-ma-sm"
          :class="!servicePackage.isOnCampus ? 'bg-teal-4 text-white' : 'text-teal'"
          outline
          no-caps
          label="Online"
          @click="onCampusClick(false)"></q-btn>
        <q-btn
          class="q-ma-sm"
          :class="servicePackage.isOnCampus ? 'bg-teal-4 text-white' : 'text-teal'"
          outline
          no-caps
          label="On campus"
          @click="onCampusClick(true)"></q-btn>
        <div class="q-ma-sm">
          <q-select
            v-if="servicePackage.isOnCampus"
            v-model="servicePackage.country"
            :options="countryOptions"
            color="teal"
            label-color="teal"
            emit-value
            map-options
            outlined
            label="Service area"
            icon="search" />
        </div>
      </q-card-section>
      <q-card-section>
        <q-separator />
        <q-item class="row">
          <q-item-section class="col-6" :class="{invisible: persistent}">
            <q-btn class="text-teal" outline rounded label="Cancel" no-caps @click="onDialogCancel"></q-btn>
          </q-item-section>
          <q-item-section class="col-6">
            <q-btn class="text-white bg-teal" :disabled="confirmDisabled" rounded label="Confirm" no-caps @click="onOKClick"></q-btn>
          </q-item-section>
        </q-item>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {useDialogPluginComponent} from 'quasar'
import {servicePackageStore} from 'stores/service-package'
import {useRoute, useRouter} from 'vue-router'
import {pubStore} from 'stores/pub'
import useSubject from 'src/composables/account/academic/useSubject'
// import {OverseasStudyList} from 'src/pages/teacher-verification/utils'
import {getActionAuthMap} from 'src/pages/sys/Manager/const.js'
import {countryOptions} from 'src/pages/account/OnCampusVerify/consts'

const {sysOverseasStudyData} = useSubject()

console.log('sysOverseasStudyData', sysOverseasStudyData.value?.topic)

const servicePackage = servicePackageStore()
const router = useRouter()
const route = useRoute()
const pub = pubStore()
const loading = ref(false)

const props = defineProps({persistent: Boolean, role: String, isServiceTask: Boolean})

const overseasStudyList = computed(() => {
  return sysOverseasStudyData.value?.topic?.map((e) => ({label: e.name, value: e?._id}))
})

// Auth
// authList:['add', 'add_mentor', 'add_other', 'view_mentor', 'view_other', 'edit', 'publish', 'view', 'delete', 'sales_statistic', 'sales_history']
const actionAuthMap = getActionAuthMap(route.path, pub.user?.managerRoles)

defineEmits([
  // REQUIRED; need to specify some events that your
  // component will emit through useDialogPluginComponent()
  ...useDialogPluginComponent.emits,
])

const {dialogRef, onDialogHide, onDialogOK, onDialogCancel} = useDialogPluginComponent()

/*
  computeds
*/
const confirmDisabled = computed(() => {
  return (
    // (servicePackage.countryCodeRequired && !servicePackage.countryCode?.length) ||
    !(servicePackage.type && (servicePackage.serviceRoles || props.isServiceTask) && servicePackage.mentoringType && servicePackage.qualification)
  )
})

const mentoringTypeOptions = computed(() => {
  if (props.isServiceTask) {
    let filteredList = servicePackage.mentoringTypeList.filter((e) => e.value !== 'academic')
    return filteredList
  }
  if (servicePackage.serviceRoles === 'substitute') {
    return servicePackage.mentoringTypeList.filter((e) => e.value === 'steam' || e.value === 'academic')
  }
  if (servicePackage.serviceRoles === 'consultant' && servicePackage.consultant?.type === 'interviewThesisDefense') {
    return servicePackage.mentoringTypeList.filter(
      (e) => e.value === 'academic' || e.value === 'steam' || e.value === 'essay' || e.value === 'teacherTraining' || e.value === 'teacherTrainingSubject'
    )
  }
  return servicePackage.mentoringTypeList.filter(
    (e) => !(servicePackage.serviceRoles == 'consultant' && servicePackage.consultant?.type == 'carer' && e.educator) && e.value !== 'thesis-defense'
  )
})

/*
  methods
*/
const categoryClick = (type) => {
  servicePackage.serviceRoles = type
  servicePackage.mentoringType = null
  servicePackage.countryCode = []
  servicePackage.qualification = null
  servicePackage.contentOrientatedEnable = false
  servicePackage.isOnCampus = false
  if (type == 'consultant') {
    servicePackage.qualification = 'experiencedTeacher'
  }
}

const qualificationClick = (type) => {
  servicePackage.qualification = type
}

const contentOrientatedClick = (value) => {
  servicePackage.contentOrientatedEnable = value
}

const consultantTypeClick = (val) => {
  servicePackage.qualification = null
  servicePackage.mentoringType = null
  servicePackage.consultant.type = val
}

const carerServiceTypeClick = (val) => {
  servicePackage.consultant.carerService = val
}

const onCampusClick = (value) => {
  servicePackage.isOnCampus = value
}

const onMentoringChange = () => {
  if (servicePackage.mentoringType === 'academic') {
    servicePackage.curriculum = {value: 'au', label: 'AU curriculum'}
  } else {
    servicePackage.curriculum = {value: 'pd', label: 'Service'}
  }

  servicePackage.countryCode = []
}

const onOKClick = async () => {
  let rs = null
  if (servicePackage.serviceRoles == 'consultant') {
    servicePackage.points =
      servicePackage.consultant?.type == 'interview'
        ? [
            'You can book an experienced teacher for one interview session after purchasing this service package. The interview is to determine if the candidate is suitable for enrolling in the relevant program.',
          ]
        : ["You can book an experienced teacher to analyse your child's learning data and provide suggestions and planning for your child. "]
    servicePackage.duration = 15
    servicePackage.break = 5
    servicePackage.freq = servicePackage.consultant?.type == 'interview' ? 14 : 30
    servicePackage.salesTarget = 'personal'
    servicePackage.price = servicePackage.consultant?.type == 'interview' ? 20 * 100 : 30 * 100
    servicePackage.discount =
      servicePackage.consultant?.type == 'carer'
        ? [
            {
              count: 9,
              discount: 0,
              gifts: 0,
              orderCount: 0,
            },
            {
              count: 18,
              discount: 0,
              gifts: 0,
              orderCount: 0,
            },
            {
              count: 36,
              discount: 0,
              gifts: 0,
              orderCount: 0,
            },
          ]
        : [
            {
              count: 1,
              discount: 0,
              gifts: 0,
              orderCount: 0,
            },
          ]
    if (servicePackage.mentoringType == 'academic') {
      servicePackage.name = servicePackage.consultant?.type == 'interview' ? 'Interview for enrolment-Academic' : 'Carer service Package-Academic'
    } else {
      servicePackage.name =
        servicePackage.consultant?.type == 'interview'
          ? `Interview for enrolment-${servicePackage.mentoringTypeList.find((e) => e.value == servicePackage.mentoringType)?.label}`
          : 'Carer Service Package-Career/Interests development'
    }
  } else if (props.isServiceTask) {
    try {
      rs = await servicePackage.packageSubmit(props.isServiceTask)
      if (!rs) {
        console.error('Package submission failed: No response returned.')
        return
      }
    } catch (error) {
      console.error('Error during package submission:', error)
      return
    }
  }

  router.push({
    path: '/sys/package/edit',
    query: {
      ...route.query,
      ...(props.isServiceTask ? {id: rs._id} : {}),
      back: `/sys/package?tab=${props.isServiceTask ? 'service-task' : props.role}`,
    },
  })

  onDialogOK()
}

const beforeDialogShow = async () => {
  servicePackage.initData()

  if (props.isServiceTask) {
    servicePackage.type = 'serviceTask'
  }
  if (props.role) {
    let role = props.role
    if (role == 'history') {
      role = 'mentoring'
    }
    categoryClick(role)
  } else {
    if (props.isServiceTask) {
      servicePackage.serviceRoles = ''
    } else {
      servicePackage.serviceRoles = route.query.tab
    }
  }
}
</script>

<style lang="sass" scoped>
.create-new-dialog
  body.screen--md &
   min-width: 30rem
</style>
