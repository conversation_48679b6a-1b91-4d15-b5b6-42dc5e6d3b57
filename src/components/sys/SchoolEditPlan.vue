<template>
  <div v-if="form">
    <q-banner inline-actions rounded class="text-white" :class="`bg-${statusColor[form.status]}`">
      <div>
        Status: <span class="text-h6">{{ statusList[form.status] }}</span>
        <div class="row q-gutter-md items-center">
          <div v-if="form.start">Start at: {{ new Date(form.start).toLocaleDateString() }} - {{ new Date(form.end).toLocaleDateString() }}</div>
        </div>
      </div>
      <template v-slot:action>
        <Auth :action="'plan_page'">
          <q-btn label="Start a free trial" no-caps>
            <q-popup-edit v-model="formTrail" buttons v-slot="scope" @save="trialFn">
              <div class="text-h6">Start a free trial</div>
              <q-input
                type="date"
                :rules="[(v) => !isEmpty(v)]"
                label="Start date"
                stack-label
                autofocus
                v-model="scope.value.start"
                @keyup.enter="scope.set"></q-input>
              <q-input type="number" label="Number of days" v-model.number="scope.value.day" dense @keyup.enter="scope.set" />
              <div class="q-pt-md" v-if="scope.value.start">
                End date: {{ new Date(new Date(scope.value.start).getTime() + scope.value.day * 86400000).toLocaleDateString() }}
              </div>
            </q-popup-edit>
          </q-btn>
        </Auth>
      </template>
    </q-banner>
    <div class="q-px-md" v-if="form._id && !isView">
      <div class="row q-gutter-md items-center q-mt-md">
        <div><span class="text-grey">Number of teachers: </span>{{ form?.teacher ?? 0 }}</div>
        <div><span class="text-grey">Number of students: </span>{{ form?.student ?? 0 }}</div>
        <Auth :action="'plan_page'">
          <q-btn rounded flat dense icon="edit" color="teal" @click="onEditClick" />
        </Auth>
      </div>
      <div class="q-my-md"><span class="text-grey">Space: </span>{{ humanStorageSize(space) }}</div>
      <div class="row q-gutter-md items-center">
        <Auth :action="'plan_page'">
          <q-toggle label="Pilot school" v-model="form.pilot" @update:model-value="upFn({pilot: $event})" />
        </Auth>
      </div>
    </div>

    <!-- dialogs -->
    <q-dialog v-model="isEditingDialogShow" persistent>
      <div class="bg-teal-1 q-pa-md rounded-lg">
        <div class="q-px-lg q-pb-lg">
          <q-input label="Number of teachers" type="text" v-model.number="currentTeacher" min="1" lazy-rules="ondemand" :rules="[(v) => v > 0]"></q-input>
          <q-input label="Number of students" type="text" v-model.number="currentStudent" min="1" lazy-rules="ondemand" :rules="[(v) => v > 0]"></q-input>
        </div>
        <div class="flex justify-end q-gutter-md">
          <q-btn rounded flat no-caps label="Cancel" icon="arrow_back" class="text-grey-7" style="border: 1px solid" @click="isEditingDialogShow = false" />
          <q-btn rounded flat no-caps label="Confirm" icon="done" class="bg-teal-4 text-white" @click="onSave" />
        </div>
      </div>
    </q-dialog>
  </div>
</template>
<script setup>
import {computed, ref, watch, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {date} from 'quasar'
import Auth from '../../pages/sys/Auth.vue'

const route = useRoute()
const router = useRouter()

const emits = defineEmits(['update'])
const props = defineProps({
  isView: Boolean,
  data: Object,
})

const statusList = ['Unpaid', 'Trial', 'Paid']
statusList[-1] = 'Expired'
statusList[undefined] = 'Unopened'

const statusColor = ['grey', 'blue', 'green']
statusColor[-1] = 'red'
statusColor[undefined] = 'grey'

const model = 'school-plan',
  formTrail = ref({start: null, day: 7}),
  g1 = 1024 * 1024 * 1024,
  day = ref(7),
  startDate = ref(null),
  form = computed(() => props.data),
  space = computed(() => {
    return (form.value.teacher || 0) * g1 + (form.value.student || 0) * g1
  })

onMounted(main)
function main() {
  if (!form.value) return setTimeout(main, 100)
  // console.log('edit plan')
  // const rs = await App.service(model).get(route.params.id)
  // emits('update', rs)
  // Object.assign(form.value, rs)

  if (form.value.start) {
    const start = date.formatDate(form.value.start, 'YYYY-MM-DD')
    formTrail.value = {...formTrail.value, start}
    if (form.value.end) {
      const day = date.getDateDiff(form.value.end, form.value.start, 'days')
      formTrail.value = {...formTrail.value, day}
    }
  }
}
async function trialFn(o) {
  const end = new Date(new Date(o.start).getTime() + o.day * 86400000)
  // console.log(o.start, o.day, end)
  const post = {start: o.start, end, status: 1}
  if (!form.value._id) {
    // const rs = await App.service(model).create({...post, school: props.school, teacher: 5, student: 100})
    // Object.assign(form.value, rs)
  } else upFn(post)
  o.start = null
  o.day = 7
  // $q.notify('Start Successfully')
}

async function upFn(post) {
  if (!form.value._id) return
  await sleep(500)
  post.space = space.value
  const rs = await App.service(model).patch(form.value._id, post)
  Object.assign(form.value, rs)
  $q.notify({type: 'positive', message: 'Saved successfully'})
}

const isEditingDialogShow = ref(false)
const currentStudent = ref(0)
const currentTeacher = ref(0)
function onEditClick() {
  currentStudent.value = form.value?.student ?? 0
  currentTeacher.value = form.value?.teacher ?? 0
  isEditingDialogShow.value = true
}

async function onSave() {
  if (currentTeacher.value !== form.value.teacher) {
    await upFn({teacher: currentTeacher.value})
    form.value.teacher = currentTeacher.value
  }
  if (currentStudent.value !== form.value.student) {
    await upFn({student: currentStudent.value})
    form.value.student = currentStudent.value
  }
  isEditingDialogShow.value = false
}
</script>
