<template>
  <div class="col">
    <q-toolbar>
      <div>Log list</div>
      <q-space></q-space>
      <!-- <q-btn label="Add" @click="dialog = true"></q-btn> -->
    </q-toolbar>
    <q-table class="col" virtual-scroll :rows="list?.data ?? []" row-key="_id" :columns="columns">
      <template v-slot:body-cell-ext="{row}">
        <q-td class="q-gutter-sm">
          {{ `${row.ext.name.join(' ')}, ${row.ext.email}, ${row.ext.phone}` }}
          <div v-if="row.ext.refName">{{ `Refer: ${row.ext.refName.join(' ')}, ${row.ext.refEmail}` }}</div>
          <div v-if="row.ext.position">{{ `Position: ${row.ext.position} planFor: ${row.ext.for}` }}</div>
          <div>{{ `Notes: ${row.ext.notes}` }}</div>
        </q-td>
      </template>
    </q-table>
  </div>
</template>
<script setup>
import {ref, watch, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
const router = useRouter()
const model = 'school-log'
const list = ref({}),
  dialog = ref(false),
  statusList = {refer: 'Refer Principal', quote: 'Quote'},
  statusColor = {refer: 'orange', quote: 'teal'},
  columns = [
    {
      align: 'left',
      name: 'method',
      label: 'Method',
      sortable: true,
      field: (row) => statusList[row.method],
      classes: (row) => `text-${statusColor[row.method]}`,
    },
    {align: 'left', name: 'createdAt', label: 'CreatedAt', sortable: true, field: (row) => new Date(row.createdAt).toLocaleString()},
    {align: 'left', name: 'ext', label: 'Form', sortable: true, field: (row) => row.ext},
    // {name: 'action', label: 'Action'},
  ]

onMounted(find)

async function find() {
  list.value = await App.service(model).find({query: {school: route.params.id}})
}
</script>
