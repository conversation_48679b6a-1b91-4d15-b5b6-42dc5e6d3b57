<template>
  <div>
    <SchoolEditPlan :isView="true" :school="school" :data="data" />
    <div class="q-pa-md">
      <q-list separator>
        <!-- <q-item clickable v-ripple>
          <q-item-section>Curriculum:</q-item-section>
          <q-item-section side>{{ stats?.curriculum }}</q-item-section>
        </q-item> -->
        <template v-if="data">
          <q-item clickable v-ripple>
            <q-item-section>Class:</q-item-section>
            <q-item-section side>{{ data.count?.class }}</q-item-section>
          </q-item>
          <q-item clickable v-ripple>
            <q-item-section>Teacher:</q-item-section>
            <q-item-section side>
              <q-icon v-if="data.teacher <= data.count?.teacher" color="red" name="error"><q-tooltip>Exceed the limit</q-tooltip></q-icon>
            </q-item-section>
            <q-item-section side>{{ data.count?.teacher }}/{{ data?.teacher }}</q-item-section>
          </q-item>
          <q-item clickable v-ripple>
            <q-item-section>Student:</q-item-section>
            <q-item-section side>
              <q-icon v-if="data.student <= data.count?.student" color="red" name="error"><q-tooltip>Exceed the limit</q-tooltip></q-icon>
            </q-item-section>
            <q-item-section side>{{ data.count?.student }}/{{ data?.student }}</q-item-section>
          </q-item>
          <q-item clickable v-ripple>
            <q-item-section>Space:</q-item-section>
            <q-item-section side>
              <!-- <q-icon v-if="data.space <= stats.space" color="red" name="error"><q-tooltip>Exceed the limit</q-tooltip></q-icon> -->
            </q-item-section>
            <q-item-section side>0/{{ humanStorageSize(data.space || 0) }}</q-item-section>
          </q-item>
        </template>
      </q-list>
      <br />
      <q-list>
        <q-item class="row">
          <q-item-section class="col-2">
            <q-item-label class="text-weight-medium">Pipeline:</q-item-label>
          </q-item-section>
          <q-item-section class="col-1" :class="data.pipelineStatus === 1 ? 'text-red' : 'text-teal'">
            {{ data.pipelineStatus ? (data.pipelineStatus === 1 ? 'Inactive' : 'Active') : '' }}
          </q-item-section>
          <q-item-section class="col-1">
            <Auth :action="'pipeline_invite'">
              <q-avatar v-if="!data.pipelineStatus" size="2.5rem" class="text-grey cursor-pointer" icon="send" @click="inviteClick('pipeline')"> </q-avatar>
            </Auth>
          </q-item-section>
        </q-item>
        <q-item class="row">
          <q-item-section class="col-2">
            <q-item-label class="text-weight-medium">Valid:</q-item-label>
          </q-item-section>
          <q-item-section class="col-4">
            <Auth :action="'pipeline_valid'">
              <q-toggle v-model="pipelineEnable" :disable="!pipelineEnable" @update:model-value="validOff('pipeline')"></q-toggle>
            </Auth>
          </q-item-section>
        </q-item>
        <q-item class="row">
          <q-item-section class="col-2">
            <q-item-label class="text-weight-medium">Pipeline Agreement:</q-item-label>
          </q-item-section>
          <q-item-section class="col-4"> Signed on {{ pipelineDate }} </q-item-section>
        </q-item>
        <q-item class="row">
          <q-item-section class="col-2">
            <q-item-label class="text-weight-medium">Content provider:</q-item-label>
          </q-item-section>
          <q-item-section class="col-1" :class="data.contentProviderStatus === 1 ? 'text-red' : 'text-teal'">
            {{ data.contentProviderStatus ? (data.contentProviderStatus === 1 ? 'Inactive' : 'Active') : '' }}
          </q-item-section>
          <q-item-section class="col-1">
            <Auth :action="'content_invite'">
              <q-avatar v-if="!data.contentProviderStatus" size="2.5rem" class="text-grey cursor-pointer" icon="send" @click="inviteClick('content')">
              </q-avatar>
            </Auth>
          </q-item-section>
        </q-item>
        <q-item class="row">
          <q-item-section class="col-2">
            <q-item-label class="text-weight-medium">Valid:</q-item-label>
          </q-item-section>
          <q-item-section class="col-4">
            <Auth :action="'content_valid'">
              <q-toggle v-model="contentEnable" :disable="!contentEnable" @update:model-value="validOff('content')"></q-toggle>
            </Auth>
          </q-item-section>
        </q-item>
        <q-item class="row">
          <q-item-section class="col-2">
            <q-item-label class="text-weight-medium">Content Agreement:</q-item-label>
          </q-item-section>
          <q-item-section class="col-4"> Signed on {{ contentDate }} </q-item-section>
        </q-item>
      </q-list>
      <br />
      <q-list>
        <div class="q-ma-md text-weight-medium">Teaching content</div>
        <div class="row q-ma-md">
          <div class="col-2 q-ml-md" v-for="(material, i) in data.attachmentsTeaching" :key="i">
            <q-icon
              v-if="material.mime.includes('pdf')"
              color="teal"
              name="picture_as_pdf"
              size="4rem"
              @mouseover="material.showbtn = true"
              @mouseleave="material.showbtn = false">
              <q-btn
                v-if="material.showbtn"
                dense
                size="xs"
                class="cursor-pointer bg-teal-4 absolute-top-left text-white"
                round
                icon="visibility"
                @click="openPDF(material)"></q-btn>
            </q-icon>
            <q-icon
              v-if="material.mime.includes('image')"
              size="4.5rem"
              :name="`img:${hashToUrl(material.hash)}`"
              @mouseover="material.showbtn = true"
              @mouseleave="material.showbtn = false">
              <q-btn
                v-if="material.showbtn"
                dense
                size="xs"
                class="cursor-pointer bg-teal-4 absolute-top-left text-white"
                round
                icon="visibility"
                @click="openPDF(material)"></q-btn>
            </q-icon>

            <div class="overflow-hidden">
              {{ material.filename }}
            </div>
          </div>
        </div>
        <q-separator />

        <div class="q-ma-md text-weight-medium">Evaluation content</div>
        <div class="row q-ma-md">
          <div class="col-2 q-ml-md" v-for="(material, i) in data.attachmentsEvaluation" :key="i">
            <q-icon
              v-if="material.mime.includes('pdf')"
              color="teal"
              name="picture_as_pdf"
              size="4rem"
              @mouseover="material.showbtn = true"
              @mouseleave="material.showbtn = false">
              <q-btn
                v-if="material.showbtn"
                dense
                size="xs"
                class="cursor-pointer bg-teal-4 absolute-top-left text-white"
                round
                icon="visibility"
                @click="openPDF(material)"></q-btn>
            </q-icon>
            <q-icon
              v-if="material.mime.includes('image')"
              size="4.5rem"
              :name="`img:${hashToUrl(material.hash)}`"
              @mouseover="material.showbtn = true"
              @mouseleave="material.showbtn = false">
              <q-btn
                v-if="material.showbtn"
                dense
                size="xs"
                class="cursor-pointer bg-teal-4 absolute-top-left text-white"
                round
                icon="visibility"
                @click="openPDF(material)"></q-btn>
            </q-icon>

            <div class="overflow-hidden">
              {{ material.filename }}
            </div>
          </div>
        </div>
        <q-separator />
        <div class="q-ma-md text-weight-medium">Digital certificates</div>
        <div class="row q-ma-md">
          <div class="col-2 q-ml-md" v-for="(material, i) in data.attachmentsDigital" :key="i">
            <q-icon
              v-if="material.mime.includes('pdf')"
              color="teal"
              name="picture_as_pdf"
              size="4rem"
              @mouseover="material.showbtn = true"
              @mouseleave="material.showbtn = false">
              <q-btn
                v-if="material.showbtn"
                dense
                size="xs"
                class="cursor-pointer bg-teal-4 absolute-top-left text-white"
                round
                icon="visibility"
                @click="openPDF(material)"></q-btn>
            </q-icon>
            <q-icon
              v-if="material.mime.includes('image')"
              size="4.5rem"
              :name="`img:${hashToUrl(material.hash)}`"
              @mouseover="material.showbtn = true"
              @mouseleave="material.showbtn = false">
              <q-btn
                v-if="material.showbtn"
                dense
                size="xs"
                class="cursor-pointer bg-teal-4 absolute-top-left text-white"
                round
                icon="visibility"
                @click="openPDF(material)"></q-btn>
            </q-icon>

            <div class="overflow-hidden">
              {{ material.filename }}
            </div>
          </div>
        </div>
      </q-list>
    </div>
  </div>
</template>
<script setup>
import SchoolEditPlan from 'components/sys/SchoolEditPlan.vue'
import {computed, ref, watch, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import ConfirmDialog from 'src/components/utils/dialogs/ConfirmDialog.vue'
import Auth from '../../pages/sys/Auth.vue'

const route = useRoute()
const router = useRouter()
const props = defineProps({
  school: String,
  data: Object,
})

const emit = defineEmits(['closeValid', 'inviteClick'])

const pipelineEnable = computed(() => props.data.pipelineEnable)
const contentEnable = computed(() => props.data.contentProviderEnable)
const pipelineDate = computed(() => {
  let dateString = makeDateString(props.data?.pipelineAt)
  if (dateString.includes('NaN')) {
    dateString = ''
  }
  return dateString
})

const contentDate = computed(() => {
  let dateString = makeDateString(props.data?.contentProviderAt)
  if (dateString.includes('NaN')) {
    dateString = ''
  }
  return dateString
})

function openPDF(data) {
  window.open(Fn.hashToUrl(data.hash), '_blank')
}

function makeDateString(time) {
  const date = new Date(time)
  let dateString
  let day = date.getDate()
  let month = date.getMonth() + 1
  let year = date.getFullYear()

  dateString = `${day < 10 ? '0' + day : day}/${month < 10 ? '0' + month : month}/${year}`

  return dateString
}

async function validOff(type) {
  const title = 'Confirm'
  const okButtonLabel = 'Confirm'
  let message = 'Confirm'
  if (type === 'pipeline') {
    message = "Switching off this button will terminate this school's pipeline agreement with Classcipe, do you confirm this action?"
  } else if (type === 'content') {
    message = "Switching off this button will terminate this school's content provider agreement with Classcipe, do you confirm this action?"
  }

  $q.dialog({
    component: ConfirmDialog,
    componentProps: {title, message, okButtonLabel},
  }).onOk(async () => {
    emit('closeValid', type)
  })
}

async function inviteClick(type) {
  emit('inviteClick', type)
}

onMounted(async () => {
  console.log('School data', props.data)
})
</script>
