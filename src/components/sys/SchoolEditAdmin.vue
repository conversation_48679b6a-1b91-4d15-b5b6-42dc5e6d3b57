<template>
  <div>
    <q-toolbar>
      <div class="text-subtitle1 text-bold">Admin list</div>
      <q-space></q-space>
      <Auth :action="'admin_page'">
        <q-btn class="bg-teal-4 text-white" flat rounded no-caps label="Admin" icon="add" @click="dialog = true"></q-btn>
      </Auth>
    </q-toolbar>

    <q-table class="col" virtual-scroll :rows="adminList?.data ?? []" row-key="_id" :columns="columns">
      <template v-slot:body-cell-mainContact="{row}">
        <q-td class="q-gutter-sm">
          <Auth :action="'admin_page'">
            <q-radio :modelValue="currentMainContact === row.uid" @update:modelValue="(v) => onSelectMainContact(v, row)" :val="true" />
          </Auth>
        </q-td>
      </template>
      <template v-slot:body-cell-action="{row}">
        <q-td class="q-gutter-sm text-right">
          <Auth :action="'admin_page'">
            <q-btn flat dense text-color="red-4" icon="delete" @click="rmAdmin(row)"></q-btn>
          </Auth>
        </q-td>
      </template>
    </q-table>

    <q-dialog v-model="dialog">
      <q-card class="q-pa-md" style="width: 480px">
        <q-form @submit="setAdmin">
          <q-input
            label="Email"
            v-model="formAdmin.email"
            ref="emailRef"
            lazy-rules="ondemand"
            @blur="emailRef.validate()"
            :rules="[(val) => mailReg.test(val), checkAdminEmail]">
            <template v-slot:append> </template>
          </q-input>
          <div class="row flex justify-between q-col-gutter-md" v-if="!formAdmin._id">
            <q-input class="col-6" label="First Name" v-model="formAdmin.name[0]" :rules="[(val) => !isEmpty(val)]"> </q-input>
            <q-input class="col-6" label="last Name" v-model="formAdmin.name[1]" :rules="[(val) => !isEmpty(val)]"> </q-input>
          </div>
          <div class="q-pb-md text-grey" v-else>Teacher already exists</div>
          <div class="row justify-end">
            <q-btn class="bg-teal-4 text-white" flat rounded type="submit" :label="!formAdmin._id ? 'Invite & Add admin' : 'Set admin'" no-caps />
          </div>
        </q-form>
      </q-card>
    </q-dialog>
  </div>
</template>
<script setup>
import {computed, ref, watch, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import Auth from '../../pages/sys/Auth.vue'

const route = useRoute()
const router = useRouter()
const props = defineProps(['school'])
const model = 'school-user'
const mailReg = Acan.regexp.mail
const emailRef = ref(null)
const adminList = ref({})
const dialog = ref(false)
const formAdmin = ref({name: []})
const statusList = ['inactive', 'pending', 'active']
const statusColor = ['grey', 'red-2', 'teal']

const columns = [
  {align: 'left', name: 'name', label: 'Name', sortable: true, field: (row) => row.name.join(' ')},
  {align: 'center', name: 'email', label: 'Email', sortable: true, field: (row) => row.email},
  {
    name: 'status',
    label: 'Status',
    sortable: true,
    field: (row) => statusList[row.status].toFirstUpperCase(),
    classes: (row) => `text-${statusColor[row.status]}`,
  },
  {name: 'del', label: 'Archive', sortable: true, field: (row) => (row.del ? 'Yes' : 'Normal'), classes: (row) => `text-${row.del ? '' : 'grey'}`},
  {name: 'mainContact', label: 'Main contact', align: 'center'},
  {name: 'action', label: 'Action'},
]

onMounted(async () => {
  await findAdmin()
  await getMainContact()
})

async function findAdmin() {
  adminList.value = await App.service(model).find({query: {role: 'admin', school: props.school}})
}
async function rmAdmin(o) {
  $q.dialog({
    title: 'Confirm remove',
    message: `Please confirm that you want to remove "${o.email}"`,
    cancel: true,
  }).onOk(async () => {
    await App.service(model).patch(o._id, {$pull: {role: 'admin'}})
    findAdmin()
  })
}
async function checkAdminEmail(val) {
  const rs = await App.service(model).get('byEmail', {query: {email: [val], school: props.school}})
  formAdmin.value._id = rs?.[0]?._id || ''
  return true
}
async function setAdmin() {
  console.log('submit', formAdmin.value)
  Acan.objClean(formAdmin.value)
  let rs
  if (!formAdmin.value._id)
    rs = await App.service(model)
      .create({...formAdmin.value, school: props.school, role: ['admin']})
      .catch((e) => e)
  else rs = await App.service('school-user').patch(formAdmin.value._id, {$addToSet: {role: ['admin']}})
  if (rs?._id) {
    $q.notify({type: 'positive', message: 'Admin set successfully'})
    formAdmin.value = {name: []}
    findAdmin()
    dialog.value = false
  } else $q.notify({type: 'negative', message: rs.message || 'server error'})
}

const currentMainContact = ref('')
async function getMainContact() {
  try {
    const res = await App.service('school-plan').get(props.school)
    currentMainContact.value = res?.contact || ''
  } catch (error) {
    console.error(error)
  }
}
async function onSelectMainContact(isMainContact, teacher) {
  const uid = teacher?.uid
  if (uid) {
    await App.service('school-plan').patch(props.school, {contact: uid})
    await getMainContact()
  }
}
</script>
