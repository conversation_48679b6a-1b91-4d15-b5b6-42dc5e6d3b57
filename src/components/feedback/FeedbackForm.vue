<template>
  <div class="feed-back-form bg-white">
    <div class="feed-back-img">
      <div class="img-mask">
        <div class="img-edit" @click="handleEditImg">
          <q-icon name="img:/v2/icons/feedback/screenshot.png" size="lg" />
          <div class="edit-text">Click to edit screenshot.</div>
        </div>
      </div>
      <div :style="{backgroundImage: 'url(' + imgBase64Data + ')'}" alt="feedback-img" class="img-item" v-if="imgBase64Data" />
    </div>
    <div class="q-pa-md column q-gutter-sm">
      <div class="text-body2 text-left text-grey-8">The screenshot will be submitted with your text comment.</div>
      <q-input type="textarea" outlined :auto-size="{minRows: 3, maxRows: 6}" allow-clear v-model="feedbackComment" placeholder="Enter text comment." />

      <div class="text-body2 text-left text-grey-8">Feedback category</div>
      <q-select
        label="Choose a feedback category"
        outlined
        :getPopupContainer="(trigger) => trigger.parentElement"
        v-model="feedbackType"
        style="width: 100%"
        :options="feedbackTypeOptions" />
      <div class="flex justify-end q-gutter-md q-mt-md">
        <q-btn no-caps color="grey-8" @click="handleCancelFeedback">Cancel</q-btn>
        <q-btn no-caps color="teal-5" type="primary" @click="handleSubmitFeedback" :loading="submitting">Submit</q-btn>
      </div>
    </div>

    <q-dialog @cancel="handleCancelEditImg" :title="null" :footer="null" :closable="false" :maskClosable="false" v-model="imgEditModalVisible">
      <ImageDraw class="edit-img-modal" :img-raw-data="imgBase64Data" v-if="imgBase64Data" @close-drawing="handleCloseEditImg" />
    </q-dialog>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import ImageDraw from 'components/feedback/ImageDraw.vue'

const props = defineProps({
  imgData: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['submit-feedback', 'cancel-feedback'])

const feedbackTypeOptions = ['Function problem', 'Improvement suggestion', 'Content error', 'Assistance needed']

const imgBase64Data = ref(null)
const feedbackComment = ref('')
const feedbackType = ref('')
const imgEditModalVisible = ref(false)
const submitting = ref(false)

onMounted(() => {
  imgBase64Data.value = props.imgData
  submitting.value = false
})

function handleSubmitFeedback() {
  submitting.value = true
  emit('submit-feedback', {
    imgBase64Data: imgBase64Data.value,
    comment: feedbackComment.value,
    feedbackType: feedbackType.value,
    pageUrl: window.location.href,
  })
}

function handleCancelFeedback() {
  submitting.value = false
  emit('cancel-feedback')
}

function handleEditImg() {
  imgEditModalVisible.value = true
}

function handleCancelEditImg() {
  imgEditModalVisible.value = false
}

function handleCloseEditImg(data) {
  if (data) {
    imgBase64Data.value = data
  }
  imgEditModalVisible.value = false
}
</script>

<style lang="scss" scoped>
.feed-back-form {
  width: 500px;
}

.feed-back-img {
  position: relative;
  width: 500px;
  overflow: hidden;

  .img-mask {
    z-index: 200;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.6);

    .img-edit {
      width: 200px;
      line-height: 40px;
      cursor: pointer;
      position: absolute;
      left: 50%;
      top: 30%;
      margin-left: -100px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .edit-text {
        color: #fff;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  .img-item {
    z-index: 100;
    width: 500px;
    height: 200px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }
}

.edit-img-modal {
  width: 100%;
  height: calc(100% - 10rem);
}
</style>
