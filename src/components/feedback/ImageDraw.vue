<template>
  <div class="img-draw bg-grey-3 position-relative" style="max-width: 960px; height: 600px">
    <div class="canvas-wrap flex flex-center bg-grey-3" ref="canvasWrap">
      <canvas class="canvas" ref="canvas" @mousedown="mousedown" @mouseup="mouseup" @mousemove="mousemove" @click="canvasClick">
        Your device does not support drawing. Please use a modern browser
      </canvas>
    </div>

    <div class="tool-wrap">
      <div class="tool-list bg-transparent">
        <q-btn unelevated size="md" class="tool-item bg-blue-3" :class="{'active-item': drawingType === 'rectangle'}" @click="drawingType = 'rectangle'">
          <q-icon name="img:/v2/icons/feedback/rect.svg" size="md" />
        </q-btn>
        <q-btn unelevated size="md" class="tool-item bg-orange-3" :class="{'active-item': drawingType === 'radius'}" @click="drawingType = 'radius'">
          <q-icon name="img:/v2/icons/feedback/circle.svg" size="md" />
        </q-btn>
        <q-btn unelevated size="md" class="tool-item bg-teal-3" :class="{'active-item': drawingType === 'route'}" @click="drawingType = 'route'">
          <q-icon name="drive_file_rename_outline" size="md" />
        </q-btn>
        <!-- <q-btn unelevated size="md" class="tool-item bg-teal-3" :class="{'active-item': drawingType === 'text'}" @click="textareaShow = true">
          <q-icon name="drive_file_rename_outline" size="md" />
        </q-btn> -->
        <q-btn unelevated size="md" class="tool-item bg-grey-5" @click="revokeAndRecovery(1)">
          <q-icon name="reply" size="md" />
        </q-btn>
        <q-btn unelevated size="md" class="tool-item bg-grey-5" @click="revokeAndRecovery(-1)">
          <q-icon name="reply" size="md" style="transform: scaleX(-1)" />
        </q-btn>
        <q-btn unelevated size="md" class="tool-item bg-grey-5" @click="clear">
          <q-icon name="img:/v2/icons/feedback/clear.svg" size="md" />
        </q-btn>
        <q-separator vertical flat class="q-mx-sm" />
        <q-btn unelevated size="md" class="tool-item cancel-item bg-red-3" @click="drawingCancel">
          <q-icon name="img:/v2/icons/feedback/close.svg" size="md" color="red-4" />
        </q-btn>
        <q-btn unelevated size="md" class="tool-item bg-green-3" @click="drawingSure">
          <q-icon name="img:/v2/icons/feedback/yes.svg" size="md" />
        </q-btn>
      </div>
    </div>
    <!-- <div v-show="textareaShow" class="textarea-wrap" ref="textarea">
      <q-input type="textarea" :rows="3" placeholder="Please enter the content." v-model="textareaText" />
      <div class="btn-wrap">
        <a-button @click="fontSure" type="success">Confirm</a-button>
        <a-button @click="fontCancel">Cancel</a-button>
      </div>
    </div> -->
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue'

const props = defineProps({
  imgRawData: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['close-drawing'])

const canvas = ref(null)
const canvasWrap = ref(null)
const canvasInstance = ref(null)

const downX = ref(0)
const downY = ref(0)
const downMs = ref(0)
const drawingType = ref('')
const drawingStatus = ref(false)
const imgData = ref([])
const imgDataIndex = ref(0)
const textareaText = ref('')
const textareaShow = ref(false)
const lineWidth = ref(2)
const strokeStyle = ref('#ff0000')

const FIXED_NUMBER = 1.04

onMounted(() => {
  canvasInstance.value = canvas.value.getContext('2d')
  drawingImg()
})

// push history list
function pushCanvasData() {
  const W = canvas.value.width
  const H = canvas.value.height
  imgData.value = imgData.value.slice(0, imgDataIndex.value + 1)
  imgData.value.push(canvasInstance.value.getImageData(0, 0, W, H))
  imgDataIndex.value = imgData.value.length - 1
  console.log('imgData', imgData.value, 'imgDataIndex', imgDataIndex.value)
}

// set canvas size, max is `img` min is `100%`
function setCanvasAttr(imgW, imgH) {
  if (!canvas.value) return
  const canvasWrapW = canvas.value.offsetWidth
  const canvasWrapH = canvas.value.offsetHeight
  canvas.value.width = imgW < canvasWrapW ? canvasWrapW : imgW
  canvas.value.height = imgH < canvasWrapH ? canvasWrapH : imgH
  return {
    x: (canvas.value.width - imgW) / 2,
    y: (canvas.value.height - imgH) / 2,
    w: imgW,
    h: imgH,
  }
}

function drawingImg() {
  const image = new Image()
  image.crossOrigin = 'anonymous'
  image.src = props.imgRawData
  image.onload = () => {
    const pixelRatio = 1000 / image.width
    const imageH = image.height * pixelRatio
    const position = setCanvasAttr(1000, imageH)
    canvasInstance.value.drawImage(image, position.x, position.y, position.w, position.h)
    setStyle()
    pushCanvasData()
  }
}

function setStyle() {
  canvasInstance.value.font = '14px Arial'
  canvasInstance.value.fillStyle = '#15c39a'
  canvasInstance.value.strokeStyle = '#15c39a'
  canvasInstance.value.lineWidth = 2
}

function strokeStyleChange(color) {
  canvasInstance.value.fillStyle = color
  canvasInstance.value.strokeStyle = color
}

function mousedown(event) {
  if (drawingType.value === '') {
    return
  }
  downMs.value = new Date().getTime()
  drawingStatus.value = true
  downX.value = event.offsetX * FIXED_NUMBER
  downY.value = event.offsetY * FIXED_NUMBER
  console.info('x:', downX.value, 'y:', downY.value, event)
  canvasInstance.value.beginPath()
}

function mouseup() {
  const interval = new Date().getTime() - downMs.value > 20 && downMs.value
  console.log('interval', interval)
  if (interval) {
    pushCanvasData()
  }
  if (drawingType.value !== '') {
    drawingStatus.value = false
  }
}

function mousemove(event) {
  if (!drawingType.value || !drawingStatus.value) {
    return
  }
  canvasInstance.value.clearRect(0, 0, canvas.value.width, canvas.value.height)
  canvasInstance.value.putImageData(imgData.value[imgDataIndex.value], 0, 0)
  console.log(event)
  const x = event.offsetX * FIXED_NUMBER
  const y = event.offsetY * FIXED_NUMBER
  if (drawingType.value === 'rectangle') {
    rectangle(x, y)
  } else if (drawingType.value === 'radius') {
    radius(x, y)
  } else if (drawingType.value === 'route') {
    route(x, y)
  }
}

function canvasClick(event) {
  if (drawingType.value !== 'font') {
    return
  }
  console.info('canvasClick font', event)
  textareaShow.value = true
  const x = event.offsetX
  const y = event.offsetY
  downX.value = x
  downY.value = y
  textarea.value.style.left = `${x}px`
  textarea.value.style.top = `${y}px`
}

function fontCancel() {
  textareaText.value = ''
  textareaShow.value = false
}

function fontSure() {
  let X = 0
  let Y = 0
  for (let i = 0, length = textareaText.value.length; i < length; i++) {
    X = (i % 11) * 8 + 12 // 8 is text padding, 12 is for text debounce
    Y = (Math.floor(i / 11) + 1) * 15 + 2 // 15 is text padding, 2 is for text debounce
    canvasInstance.value.fillText(textareaText.value[i], X + downX.value, Y + downY.value)
  }
  fontCancel()
  pushCanvasData()
}

function rectangle(X, Y) {
  canvasInstance.value.strokeRect(downX.value, downY.value, X - downX.value, Y - downY.value)
}

function radius(X, Y) {
  const radius = Math.sqrt(Math.pow(X - downX.value, 2) + Math.pow(Y - downY.value, 2))
  canvasInstance.value.beginPath()
  canvasInstance.value.arc(downX.value, downY.value, radius, 0, Math.PI * 2, true)
  canvasInstance.value.stroke()
  canvasInstance.value.closePath()
}

function route(X, Y) {
  console.log(X, Y)
  canvasInstance.value.lineTo(X, Y)
  canvasInstance.value.stroke()
}

function revokeAndRecovery(n) {
  imgDataIndex.value = imgDataIndex.value - n < 0 ? 0 : imgDataIndex.value - n
  imgDataIndex.value = imgDataIndex.value === imgData.value.length ? imgData.value.length - 1 : imgDataIndex.value
  canvasInstance.value.putImageData(imgData.value[imgDataIndex.value], 0, 0)
}

function clear() {
  imgData.value = imgData.value.slice(0, imgDataIndex.value + 1)
  imgData.value.push(imgData.value[0])
  imgDataIndex.value = imgData.value.length - 1
  canvasInstance.value.putImageData(imgData.value[imgDataIndex.value], 0, 0)
}

async function drawingSure() {
  const base64 = canvas.value.toDataURL()
  emit('close-drawing', base64)
}

async function drawingCancel() {
  return emit('close-drawing')
}
</script>

<style lang="scss" scoped>
.img-draw {
  position: relative;
  width: 1000px;
}

.drawing-wrap {
  width: 100%;
  height: 100%;
  background: #f1f1f1;
  display: flex;
  flex-direction: column;
}

.canvas-wrap {
  flex: 1;
  background-size: 30px;
  overflow: auto;
  .canvas {
    width: 100%;
    display: block;
    max-width: 960px;
    max-height: 600px;
  }
}
.textarea-wrap {
  position: absolute;
  width: 180px;
}

.tool-wrap {
  position: absolute;
  left: 45%;
  bottom: 0px;
  margin-left: -142px;
  width: 285px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.75rem;

  .tool-list {
    display: flex;
    align-items: center;
    background: #fff;
    height: 40px;

    .tool-item {
      cursor: pointer;
      padding: 0 8px;
      margin: 0 0.25rem;
      height: 40px;
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #e5e5e5;
      }
      svg {
        height: 20px;
        width: 20px;
      }
    }
  }
}

.active-item {
  border: 2px dotted red;
}
</style>
