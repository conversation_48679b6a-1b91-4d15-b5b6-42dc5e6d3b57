<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div v-if="isFeedbackShow" id="classcipe-feedback" class="my-feed-back">
    <div id="feed-back" class="classcipe-feedback">
      <div class="classcipe-hidden-feedback" @click.stop="handleCloseFeedback">
        <q-icon class="img" name="img:/v2/icons/feedback/close.png" size="sm" />
      </div>
      <div class="feed-back-icon">
        <!-- <q-icon name="img:/v2/icons/feedback/serve.svg" size="lg" @click.stop="handleSelectCaptureFeedback" /> -->
        <q-icon name="img:/v2/icons/feedback/feedback.png" size="lg" style="font-size: 72px" @click.stop="handleSelectCaptureFeedback" />
      </div>
      <!-- <div class="feed-back-text" @click.stop="handleSelectCaptureFeedback">
        <div class="text-item">Feedback</div>
      </div> -->
    </div>

    <q-dialog class="feed-back-modal" @cancel="handleCancelFeedback" title="Feedback" :footer="null" width="550px" v-model="feedbackModalVisible">
      <div id="my-canvas-container">
        <template v-if="!captureCreating">
          <FeedbackForm :img-data="feedbackImgData" v-if="feedbackImgData" @cancel-feedback="handleCancelFeedback" @submit-feedback="handleSubmitFeedback" />
          <NoData tips="Something wrong" v-if="!feedbackImgData" />
        </template>
      </div>
    </q-dialog>
  </div>
</template>

<script setup>
import {ref, watch, onMounted} from 'vue'
import {useQuasar} from 'quasar'
import {pubStore} from 'stores/pub'
import html2canvas from 'html2canvas'
import FeedbackForm from 'components/feedback/FeedbackForm.vue'

const $q = useQuasar()

const pub = pubStore()

const feedbackTypeVisible = ref(false)
const feedbackModalVisible = ref(false)
const feedbackImgData = ref(null)

const isFeedbackShow = ref(false)
onMounted(() => {
  const isInIframe = window.self !== window.top
  const existFeedback = document.getElementById('classcipe-feedback')
  // if new web feedback exist, don't second feedback
  if (existFeedback || isInIframe) {
    isFeedbackShow.value = false
  } else {
    isFeedbackShow.value = true
  }
})

const captureCreating = ref(false)
watch(captureCreating, (bool) => {
  if (bool) $q.loading.show()
  else $q.loading.hide()
})

function handleSelectChatFeedback() {
  feedbackTypeVisible.value = false
  document.getElementById('chat-widget-container').style.display = 'block'
}

function handleSelectCaptureFeedback() {
  feedbackTypeVisible.value = false
  if (!feedbackModalVisible.value) {
    captureCreating.value = true
    feedbackModalVisible.value = true
    feedbackImgData.value = null
    html2canvas(document.body, {
      allowTaint: true,
      useCORS: true,
      scrollX: window.pageXOffset,
      scrollY: window.pageYOffset,
      x: window.pageXOffset,
      y: window.pageYOffset,
      width: window.innerWidth,
      height: window.innerHeight,
    })
      .then((canvas) => {
        canvas.style.opacity = '1'
        canvas.style.zIndex = '99999999'
        canvas.style.transition =
          'transform 0.3s cubic-bezier(0.42, 0, 0.58, 1),opacity 0.3s cubic-bezier(0.42, 0, 0.58, 1),-webkit-transform 0.3s cubic-bezier(0.42, 0, 0.58, 1)'
        feedbackImgData.value = canvas.toDataURL('image/png', 1)
      })
      .finally(() => {
        captureCreating.value = false
      })
  }
}

function handleCancelFeedback() {
  feedbackModalVisible.value = false
  feedbackImgData.value = null
}

function handleCloseFeedback() {
  document.getElementById('feed-back').style.display = 'none'
}

async function handleSubmitFeedback(data) {
  const dto = {
    uid: pub?.user?._id,
    nickname: pub?.user?.nickname ?? '',
    name: pub?.user?.name ?? [],
    ua: navigator.userAgent,
    text: data.comment,
    url: data.pageUrl,
    pic: data.imgBase64Data,
    type: data.feedbackType,
  }
  const res = await App.service('feedback').create(dto)
  console.log(res)
  if (res) {
    $q.notify({type: 'positive', message: 'Thank you for your feedback, we will get back to you as soon as possible.'})
    feedbackModalVisible.value = false
  } else {
    $q.notify({type: 'negative', message: 'Submitted feedback unsuccessfully!'})
  }
}
</script>

<style lang="scss" scoped>
#feed-back {
  position: fixed;
  right: 35px;
  bottom: 80px;
  color: #fff;
  line-height: 30px;
  border-radius: 5px;
  z-index: 3000;
  cursor: pointer;
  font-size: 12px;
  user-select: none;
}

.feed-back-detail {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  padding: 5px;
}

.feed-back-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    width: 30px;
    height: 30px;
  }
  img {
    width: 48px;
    height: 48px;
  }
}

.feed-back-text {
  padding: 5px;
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.05);
  .text-item {
    color: #15c39a;
    font-weight: 500;
    font-size: 12px;
    line-height: 12px;
  }
}

#my-canvas-container {
  position: relative;
  text-align: center;

  .capture-creating {
    width: 500px;
    height: 100%;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.feed-back-content {
  user-select: none;
  width: 180px;
  font-family: 'Noto Sans', sans-serif;
  box-sizing: border-box;
}

.feed-type-title {
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
  color: #15c39a;
  line-height: 15px;
  padding: 0 5px 5px 5px;
}

.feed-type-item {
  margin-top: 8px;
  padding: 5px 8px;
  font-weight: 500;
  font-size: 13px;
  line-height: 20px;
  color: #ffffff;
  cursor: pointer;
  background: rgba(21, 195, 154);
  border-radius: 2px;

  &:hover {
    background: rgba(17, 174, 137);
  }
}

.classcipe-hidden-feedback {
  display: none;
}

.classcipe-hidden-feedback .img {
  z-index: 100;
  width: 1.5rem;
  position: absolute;
  right: -1rem;
  top: -1rem;
}

#feed-back:hover .classcipe-hidden-feedback {
  display: block;
}

.feed-back-type {
  .feed-back-type-title {
    line-height: 30px;
    font-size: 14px;
    font-weight: 500;
  }
}
</style>
