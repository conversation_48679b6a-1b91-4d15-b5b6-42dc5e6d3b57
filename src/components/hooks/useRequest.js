import { ref } from 'vue';

const useRequest = (requestPromise, options = {}) => {
  const data = ref(null);
  const loading = ref(false);

  const fetchData = async () => {
    try {
      loading.value = true;
      const result = await requestPromise();
      data.value = result;
      if(result){
        options.onSuccess && options.onSuccess(result);
      }

    } catch (error) {
      if(options.onError){
        options.onError(error);
      }else {
        $q.notify({type: 'negative', message: error?.message})
      }
    } finally {
      loading.value = false;
      options.onFinally && options.onFinally();
    }
  };

  return {
    data,
    loading,
    run: fetchData
  };
};

export default useRequest;
