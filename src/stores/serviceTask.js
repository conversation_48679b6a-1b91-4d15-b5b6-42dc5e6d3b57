import {defineStore} from 'pinia'

export const serviceTaskStore = defineStore('serviceTask', {
  state: () => ({
    sections: [],
    currentSection: {},
    isSectionsAvaialable: false,
  }),
  actions: {
    async saveSection(data) {
      await App.service('section').create(data)
      this.sections.push(data)
    },
    async getSections(serviceTaskId) {
      const query = {
        serviceTaskId: serviceTaskId,
      }

      const sections = await App.service('section').find({
        query: {
          serviceTaskId: serviceTaskId,
        },
      })

      console.log('query', query, sections)
      this.sections = sections.data
      this.isSectionsAvaialable = true
      return sections.data
    },
    async getSection(id) {
      const section = await App.service('section').get(id)
      this.currentSection = section
      return section
    },
    async updateSection(id, data) {
      await App.service('section').patch(id, data)
      const index = this.sections.findIndex((section) => section.id === id)
      if (index !== -1) {
        this.sections[index] = {...this.sections[index], ...data}
        this.currentSection = this.sections[index]
      }
      return this.currentSection
    },
    async deleteSection(id) {
      await App.service('section').remove(id)
      this.sections = this.sections.filter((section) => section.id !== id)
    },
    async sendMessage(id, message) {
      this.currentSection = await App.service('section').patch(id, {message})
    },
    async sendComment(id, comment) {
      this.currentSection = await App.service('section').patch(id, {comment})
    },
    async uploadFile(id, file) {
      this.currentSection = await App.service('section').patch(id, {file})
    },
    // async sendComplain(sectionId, content, attachments, status) {

    //   this.currentSection = await App.service('teaching-accident').patch(sectionId, {
    //     $addToSet: {
    //       evidencesStudent: {
    //         content,
    //         attachments,
    //       },
    //     },
    //     status,
    //   })
    // },
    async handleConfirmAsCompleted(id, isMarked) {
      if (isMarked) {
        this.currentSection = await App.service('section').patch(id, {
          status: 'completed',
          completedTime: new Date(),
        })
      }
    },

    async updateNextSectionStatus(nextSection, status) {
      console.log('nextSection is calling', nextSection, status, this.currentSection, this.currentSection.serviceTaskId)
      if (nextSection) {
        await App.service('section').patch(nextSection._id, {status})
      } else {
        await App.service('section').patch(this.currentSection.serviceTaskId, {
          lastOne: true,
          curServicer: this.currentSection.servicer,
          bookingId: this.currentSection.bookingId,
        })
      }
    },

    async handleMarkAsCompleted(id, isMarked) {
      console.log('zxc', id, isMarked)

      if (isMarked) {
        this.currentSection = await App.service('section').patch(id, {
          markAsCompleted: true,
          markAsCompletedTime: new Date(),
        })
      }
    },
    async deleteFile(fileId) {
      const updatedFiles = this.currentSection.files.filter((file) => file._id !== fileId)
      const updatedSection = await App.service('section').patch(this.currentSection._id, {files: updatedFiles})
      this.currentSection = updatedSection
      this.sections = this.sections.map((section) => (section._id === updatedSection._id ? updatedSection : section))
    },
  },
})
