import {defineStore} from 'pinia'
import {calContentOrientatedPrice, formDataOnCampusPrice} from 'src/pages/sys/package/const.js'
import {ref} from 'vue'

const contentOrientatedPrice = (item, salesTarget) => {
  let result = {}
  if (salesTarget?.includes('school')) {
    result.schoolPrice = item?.schoolPrice * 100
  }
  if (salesTarget?.includes('personal')) {
    result.price = item?.price * 100
  }

  return result
}

export const servicePackageStore = defineStore('servicePackage', {
  state: () => ({
    _id: ref(''),
    name: ref(''), // service package name
    description: ref(''), // service-task package description
    cover: ref(''), // id of cover image
    coverName: ref(''), //name of cover image
    points: ref(['']), //selling points
    sections: ref([]), // sections of service-task package
    state: ref(''), // service package state. ServicePackageState: ['unassigned', 'ongoing', 'completed']
    addedAssociatedTask: ref(null), // associated task if exist
    type: ref('mentoring'), // REQUIRED service type. ServiceType: ['mentoring'],
    mentoringType: ref(''), // mentoring type if exist. MentoringType: ['essay', 'academic', 'overseasStudy', 'teacherTraining', 'steam]
    serviceRoles: ref(''), // REQUIRED service type. ServiceRoles: ['mentoring', 'substitute', 'correcting', consultant],
    countryCode: ref(''), // country code if exist
    curriculum: ref({
      value: '',
      label: '',
    }), // selected curriculum
    subject: ref([]), // selected subject
    topic: ref([]),
    keywords: ref([]),
    gradeGroup: ref({
      label: '',
      grades: [],
    }), // selected grade
    price: ref(0), // single price * 100
    income: ref(0),
    discount: ref([
      {
        count: 0, // number
        discount: 0, // %
        gifts: 0,
        // orderCount: 0,
      },
    ]),
    discountConfig: ref({
      enable: false,
      end: '',
      discount: 0,
      associateTaskDiscount: 0,
    }),
    freq: ref(0), // frequency of each package [7, 14, 30, 120]
    duration: ref(0), // session duration
    break: ref(0), // session break
    status: ref(false), // publish status
    filled: ref(false),
    lastPublished: ref(null),
    count: ref({
      sold: 0, // number of sold
      valid: 0, // number of valid
    }),
    qualification: ref(),
    consultant: ref({
      type: 'carer',
      carerService: 'academic',
      servicePack: null,
    }),
    contentOrientatedEnable: ref(false),
    contentOrientated: ref([]),

    packageList: ref([]),
    subjectList: ref([]),
    attachments: ref([]),

    interview: ref(false),
    backgroundCheck: ref(false),
    bundledCarer: ref(false),
    bundledInterview: ref(false),
    carerPack: ref(''),
    interviewPack: ref({}),
    associatedTask: ref({}),
    requirements: ref({}),
    requirementsItems: ref([]),

    splitSale: ref(false),
    salesTarget: ref(['personal']),
    serviceSubjects: ref({}),
    serviceRolesList: ref([
      {value: 'mentoring', label: 'Mentoring', main: true},
      {value: 'substitute', label: 'Substitute', main: true},
      {value: 'correcting', label: 'Correcting', main: true},
      {value: 'consultant', label: 'Education consultant', main: true},
      //{value: 'history', label: 'History'},
    ]),
    mentoringTypeList: ref([]),
    consultantTypeList: ref([
      {value: 'carer', label: 'Carer service package'},
      {value: 'interview', label: 'Interview for enrolment'},
      {value: 'interviewTeacher', label: 'Interview for teacher verification'},
      {value: 'interviewThesisDefense', label: 'Thesis defense'},
    ]),
    carerServiceTypeList: ref([
      {value: 'academic', label: 'Academic consultant'},
      {value: 'career', label: 'Career/Interests development'},
      {value: 'certificate', label: 'University certificate course'},
    ]),
    qualificationList: ref([
      {
        value: 'workshopLeader',
        label: 'Workshop leader/Senior lecturer',
        educators: true,
        consultant: ['interview', 'interviewTeacher', 'interviewThesisDefense'],
      },
      {
        value: 'experiencedTeacher',
        label: 'Experienced teacher',
        students: true,
        educators: true,
        consultant: ['carer', 'interview', 'interviewTeacher', 'interviewThesisDefense'],
      },
      {value: 'studentTutor', label: 'Student tutor', students: true, educators: false, consultant: []},
      {value: 'consultant', label: 'Consultant', students: true, educators: false, consultant: []},
      {value: 'seniorConsultant', label: 'Senior Consultant', students: true, educators: false, consultant: []},
    ]),

    isOnCampus: ref(false),
    country: ref('NZ'),
    onCampusPrice: ref([]),
    topicWithChildsTypes: ref(['essay', 'teacherTrainingSubject']),
    hourRate: ref(0),
    hourRateList: ref([]),
    inflationRate: ref(100),
    campusCityList: ref([]),
  }),
  getters: {
    // countryCodeRequired: (state) => ['overseasStudy', 'academicPlanning', 'personalStatement'].includes(state.mentoringType),
    countryCodeRequired: (state) => ['overseasStudy'].includes(state.mentoringType),
    uniResarchSubjects: (state) => state.serviceSubjects['essay']?.['topic'],
    teachingAreaSubjects: (state) => state.serviceSubjects['teacherTraining']?.['topic'],
    topicWithChilds: (state) => state.topicWithChildsTypes.includes(state.mentoringType),
    isConsultantInterview: (state) => state.serviceRoles == 'consultant' && ['interview', 'interviewTeacher'].includes(state.consultant.type),
    isEducatorServiceType: (state) => state.mentoringTypeList.find((e) => e.value == state.mentoringType)?.educator === true,
    totalTimes: (state) =>
      state.contentOrientated.reduce((sum, current) => {
        //https://github.com/zran-nz/bug/issues/5479#issuecomment-2480498346
        return sum + Math.ceil(current.times / Math.ceil(28 / state.freq))
      }, 0),
  },
  actions: {
    isTopicWithChilds(type) {
      return this.topicWithChildsTypes.includes(type)
    },
    async getPackageList() {
      const rs = await App.service('service-pack').find({query: {$limit: 1000}})

      this.packageList = rs.data.sort((a, b) => {
        return new Date(b.updatedAt) - new Date(a.updatedAt)
      })
      // makePackageTags()
      return this.packageList
    },

    async getCampusCityList() {
      const res = await App.service('campus-location').find({
        query: {
          $limit: 1000,
          $sort: {updatedAt: -1},
          country: this.country,
          archive: false,
        },
      })

      if (res) {
        this.campusCityList = res?.data?.map((item) => {
          return {
            ...item,
            label: item?.city,
            value: item?.city,
          }
        })
      }
    },

    async deletePackage(packageId) {
      const rs = await App.service('service-pack').remove(packageId)
      return rs
    },

    async getOnePackage(packageId) {
      /*
      const rs = await App.service('service-pack').find({query: {_id: packageId, $limit: 1000}})
      return rs?.data?.[0]
      */
      return await App.service('service-pack').get(packageId)
    },

    // async findTeacher(pack){
    //   let rs
    //   if(pack.type === 'mentoring') {
    //     rs = await App.service("service-auth").get("stats", { query: { type: pack.type, mentoringType: pack.mentoringType } });
    //   } else {
    //     rs = await App.service("service-auth").get("stats", { query: { type: pack.type } });
    //   }
    //   return rs
    // },

    initData() {
      this._id = ''
      this.name = ''
      this.description = ''
      this.sections = []
      this.state = 'unassigned'
      this.cover = ''
      this.coverName = ''
      this.points = ['']
      this.type = 'mentoring'
      this.mentoringType = ''
      this.serviceRoles = ''
      this.countryCode = []
      this.curriculum = {
        value: '',
        label: '',
      }
      this.subject = []
      this.topic = []
      this.keywords = []
      this.gradeGroup = {
        grades: [],
        label: '',
      }

      this.price = 0
      this.income = 0
      this.discount = [
        {
          count: 0,
          discount: 0,
          gifts: 0,
          // orderCount: 0,
        },
      ]
      this.discountConfig = {
        enable: false,
        end: '',
        discount: 0,
        associateTaskDiscount: 0,
      }

      this.contentOrientatedEnable = false
      this.contentOrientated = []
      this.bundledCarer = false
      this.bundledInterview = false
      this.carerPack = {}
      this.interviewPack = {}
      this.associatedTask = {}

      this.freq = 0
      this.duration = 0
      this.break = 0
      this.status = false
      this.filled = false
      this.lastPublished = null
      this.count = {
        sold: 0,
        valid: 0,
      }
      this.qualification = ''
      this.consultant = {type: 'carer', carerService: 'academic', servicePack: null}

      this.attachments = []

      this.splitSale = false
      this.salesTarget = ['personal']
      this.backgroundCheck = false
      this.requirements = ''
      this.requirementsItems = []
      this.interview = false
      this.isOnCampus = false
      this.country = 'NZ'
      this.onCampusPrice = []
      this.reason = ''
    },

    async loadData(pack, packCurriculum, gradeGroup) {
      console.log('pack', pack)
      this._id = pack._id
      this.name = pack.name || ''
      this.description = pack.description || ''
      this.sections = pack.sections || []
      this.state = pack.state || 'unassigned'
      this.cover = pack.cover || ''
      this.coverName = pack.coverName || ''
      this.points = pack.points || ['']
      this.type = pack.type || 'mentoring'
      this.mentoringType = pack.mentoringType || ''
      this.serviceRoles = pack.serviceRoles || ''
      this.countryCode = pack.countryCode || []
      this.curriculum = {
        value: packCurriculum.value || '',
        label: packCurriculum.label || '',
      }
      this.subject = pack.subject || []
      this.topic = pack.topic || []
      this.keywords = pack.keywords || []
      this.gradeGroup = {
        grades: gradeGroup.grades || [],
        label: gradeGroup.label || '',
      }
      // this.price = pack.price || 0
      this.income = pack.income || 0

      this.discount = pack?.discount?.map((e) => ({...e, discount: 100 - (e.discount || 0)})) || {count: 0, discount: 0, gifts: 0}

      if (pack.discountConfig) {
        if (!pack.discountConfig.end) {
          pack.discountConfig.end = ''
        } else {
          let endDate = new Date(pack.discountConfig.end)

          const today = new Date()

          const endDateStr = this.makeDateString(endDate)
          const todayStr = this.makeDateString(today)
          if (endDateStr < todayStr) {
            pack.discountConfig = {enable: false, end: ''}
          } else {
            pack.discountConfig.end = endDateStr
          }
        }
        this.discountConfig = pack.discountConfig
      } else {
        this.discountConfig = {enable: false, end: ''}
      }

      this.discountConfig.discount = pack.discountConfig.discount
      this.discountConfig.associateTaskDiscount = pack.discountConfig.associateTaskDiscount

      this.freq = pack.freq || 0
      this.duration = pack.duration || 0
      this.break = pack.break || 0
      this.status = pack.status || false
      this.filled = pack.filled || false
      this.lastPublished = pack.lastPublished || null
      this.count = pack.count || {sold: 0, valid: 0}
      this.qualification = pack.qualification || ''
      this.consultant = pack.consultant || {type: 'carer', carerService: 'academic', servicePack: ''}
      this.contentOrientatedEnable = pack.contentOrientatedEnable
      if (this.contentOrientatedEnable) {
        this.contentOrientated = [...(pack.contentOrientated || [])].map((item) => {
          const percentPrice = pack?.salesTarget?.length === 2 ? Math.round((item?.price / item?.schoolPrice) * 100) : undefined
          return {
            ...item,
            schoolPrice: item?.schoolPrice ? item?.schoolPrice / 100 : undefined,
            price: item?.price ? item?.price / 100 : undefined,
            percentPrice,
          }
        })
      }

      if (pack.carerPack?._id) {
        this.carerPack = pack.carerPack
        this.bundledCarer = true
      } else {
        this.carerPack = {}
        this.bundledCarer = false
      }
      if (pack.interviewPack?._id) {
        this.interviewPack = pack.interviewPack
        this.bundledInterview = true
      } else {
        this.interviewPack = {}
        this.bundledInterview = false
      }
      if (pack.associatedTask?._id) {
        this.associatedTask = pack.associatedTask
      } else {
        this.associatedTask = {}
      }

      this.attachments = pack.attachments || []
      this.salesTarget = pack.salesTarget
      this.backgroundCheck = pack.backgroundCheck
      this.requirements = pack.requirements
      this.requirementsItems = pack.requirementsItems
      this.splitSale = pack.splitSale
      this.interview = pack.interview
      this.isOnCampus = pack.isOnCampus
      this.country = pack.country
      if (pack.onCampusPrice) {
        this.onCampusPrice = pack?.onCampusPrice.map((item) => ({
          ...item,
          price: item?.price / 100,
        }))
      }
      if (pack?.serviceRoles === 'substitute' && !pack?.isOnCampus) {
        this.price = pack?.price / 100
      } else {
        this.price = pack?.price
      }
      if (this.associatedTask._id) {
        this.addedAssociatedTask = await this.getOnePackage(this.associatedTask._id)
      }
    },

    makeDateString(date) {
      let year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()

      if (month < 10) month = '0' + month
      if (day < 10) day = '0' + day
      return `${year}-${month}-${day}`
    },

    async packageSubmit(hideServiceRoles = false) {
      let data = {}

      data.name = this.name
      data.description = this.description
      data.sections = this.sections
      data.state = this.state
      data.points = Acan.clone(this.points)
      data.type = this.type
      data.mentoringType = this.mentoringType
      if (!hideServiceRoles) {
        data.serviceRoles = this.serviceRoles
      }
      data.curriculum = this.curriculum.value
      data.subject = Acan.clone(this.subject)
      data.topic = Acan.clone(this.topic)
      data.keywords = Acan.clone(this.keywords || [])
      data.gradeGroup = this.gradeGroup.grades
      // data.price = this.price
      data.income = this.income
      data.discount = []
      this.discount.map((discount) => {
        data.discount.push({
          count: parseInt(discount.count),
          discount: discount.discount ? 100 - discount.discount : undefined,
          gifts: parseInt(discount.gifts || 0),
          // orderCount: parseInt(discount.orderCount),
        })
      })
      data.discountConfig = this.discountConfig
      // data.discountConfig = {}
      // if (this.discountConfig.end === '') {
      //   data.discountConfig.enable = this.discountConfig.enable
      //   data.discountConfig.end = null
      // } else {
      //   data.discountConfig = this.discountConfig
      // }
      // data.discountConfig.discount = this.discountConfig.discount
      data.discountConfig.associateTaskDiscount = this.discountConfig.associateTaskDiscount
      data.freq = this.freq || 7
      data.duration = this.duration
      data.break = this.break
      data.filled = this.filled

      if (this.cover) {
        data.cover = this.cover
        data.coverName = this.coverName
      }

      if (this.countryCodeRequired) {
        data.countryCode = this.countryCode
      }

      data.qualification = this.qualification
      if (this.serviceRoles == 'consultant') {
        data.consultant = this.consultant
      }
      if (this.contentOrientatedEnable) {
        data.contentOrientatedEnable = true
      } else {
        data.contentOrientatedEnable = false
      }

      if (this.contentOrientatedEnable && this.contentOrientated?.length) {
        data.contentOrientated = this.contentOrientated.map((e) => ({
          premium: e?.premium?._id,
          times: e?.times,
          subject: e?.subject,
          servicePack: e?.servicePack?._id,
          ...contentOrientatedPrice(e, this.salesTarget),
        }))
      } else {
        data.contentOrientated = []
      }

      data.carerPack = {}

      if (this.carerPack?._id && this.bundledCarer) {
        data.carerPack._id = this.carerPack._id
        data.carerPack.times = this.totalTimes
      }

      data.interviewPack = {}

      if (this.interviewPack?._id && this.bundledInterview) {
        data.interviewPack._id = this.interviewPack._id
        data.interviewPack.times = this.interviewPack.times
      }
      data.associatedTask = {}

      if (this.associatedTask?._id) {
        data.associatedTask._id = this.associatedTask._id
      }

      data.attachments = this.attachments
      data.backgroundCheck = this.backgroundCheck
      data.requirements = this.requirements
      data.requirementsItems = this.requirementsItems
      data.splitSale = this.splitSale
      data.interview = this.interview
      data.salesTarget = this.salesTarget
      data.reason = ''

      data.contentOrientatedConfig = calContentOrientatedPrice(this)
      if (this.serviceRoles === 'substitute' && this.isOnCampus) {
        data.onCampusPrice = formDataOnCampusPrice(this.onCampusPrice)
        data.isOnCampus = this.isOnCampus
        data.country = this.country
      }

      if (this.serviceRoles === 'substitute' && !this.isOnCampus) {
        data.price = this.price * 100
      } else {
        data.price = this.price
      }

      console.log(data)
      // return
      let rs
      if (this._id) {
        rs = await App.service('service-pack').patch(this._id, data)
      } else {
        rs = await App.service('service-pack').create(data)
      }
      return rs
    },
    async saveSections(curId) {
      let rs = await App.service('service-pack').patch(this._id || curId, {sections: this.sections, price: this.price})
      return rs
    },
    async setAssociatedTask() {
      if (this._id) {
        let rs = await App.service('service-pack').patch(this._id, {
          associatedTask: {
            _id: this.associatedTask._id,
          },
        })
        this.addedAssociatedTask = await this.getOnePackage(this.associatedTask._id)
        return rs
      } else {
        this.addedAssociatedTask = await this.getOnePackage(this.associatedTask._id)
      }
    },
    async unSetAssociatedTask() {
      if (this._id) {
        let rs = await App.service('service-pack').patch(this._id, {associatedTask: {}})
        this.addedAssociatedTask = null
        return rs
      }
    },
    async setState(changeState) {
      let rs = await App.service('service-pack').patch(this._id, {state: changeState})
      this.state = changeState
      return rs
    },
    async publishPackage(packId, status) {
      let rs
      rs = await App.service('service-pack').patch(packId, {status: status})
      return rs
    },

    async findServiceTypes() {
      if (!this.mentoringTypeList.length) {
        if (!this.subjectList.length) {
          await this.findSubjectList('all')
        }
        this.mentoringTypeList = [
          {label: 'Academic', value: 'academic'},
          {label: 'Thesis defense', value: 'thesis-defense'},
          ...this.subjectList
            .filter((e) => e.curriculum[0] == 'pd' && e.del == false)
            .map((e) => ({label: e.name, value: e.subjectCode, educator: e.participants == 'educators'})),
        ]
      }
    },
    async findAllServiceSubjectList() {
      for (let i = 0; i < this.mentoringTypeList.length; i++) {
        const e = this.mentoringTypeList[i]
        if (e.value !== 'academic' && e.value !== 'thesis-defense') {
          await this.findSubjectList(e.value)
        }
      }
    },
    async findSubjectList(type) {
      let subjectList = this.subjectList
      let tempSubjectList
      if (type === 'all') {
        if (!this.subjectList.length) {
          subjectList = await App.service('subjects').find({query: {uid: '1', $limit: 500, isLib: true, del: false}})
          subjectList = subjectList.data
          this.subjectList = subjectList
        }
        return subjectList
      } else {
        if (this.serviceSubjects[type]) {
          return this.serviceSubjects[type]
        }

        if (!this.subjectList.length) {
          await this.findSubjectList('all')
        }

        const serviceSubject = this.subjectList.find((subject) => subject.curriculum[0] === 'pd' && subject.subjectCode === type)
        tempSubjectList = await App.service('subjects').get(serviceSubject._id, {query: {isLib: true, del: false}})
        this.serviceSubjects[type] = tempSubjectList
        return tempSubjectList
      }
    },
    async init(mentoringType) {
      await this.findServiceTypes()
      if (this.mentoringTypeList.some((e) => e.value == mentoringType) && mentoringType !== 'academic' && mentoringType !== 'thesis-defense') {
        await this.findSubjectList(mentoringType)
      } else if (!mentoringType) {
        await this.findAllServiceSubjectList()
      }
    },
  },
})
